<?php

namespace App\Console\Commands;

use App\Models\Demande;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetCinFromUserInDemandes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demandes:set-cin 
                            {--batch-size=1000 : Number of records to process in each batch}
                            {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set CIN values in demandes table from related users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $batchSize = (int) $this->option('batch-size');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }

        $this->info('🚀 Starting CIN update process...');

        // Get database names for cross-database operations
        $defaultDb = config('database.connections.mysql.database');
        $secondDb = config('database.connections.mysql2.database');

        // Count total demandes that need CIN update
        $totalCount = DB::table('demandes')
            ->whereNull('cin')
            ->whereNotNull('user_id')
            ->count();

        if ($totalCount === 0) {
            $this->info('✅ All demandes already have CIN values set.');
            return 0;
        }

        $this->info("📊 Found {$totalCount} demandes that need CIN update");

        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();

        $updated = 0;
        $errors = 0;

        // Process in batches to avoid memory issues
        DB::table('demandes')
            ->whereNull('cin')
            ->whereNotNull('user_id')
            ->orderBy('id')
            ->chunk($batchSize, function ($demandes) use ($secondDb, $dryRun, &$updated, &$errors, $progressBar) {
                
                // Get user IDs from this batch
                $userIds = collect($demandes)->pluck('user_id')->unique()->toArray();
                
                // Fetch users with their CINs from the second database
                $users = DB::connection('mysql2')
                    ->table('users')
                    ->whereIn('id', $userIds)
                    ->whereNotNull('cin')
                    ->pluck('cin', 'id')
                    ->toArray();

                foreach ($demandes as $demande) {
                    try {
                        if (isset($users[$demande->user_id])) {
                            $cin = $users[$demande->user_id];
                            
                            if (!$dryRun) {
                                DB::table('demandes')
                                    ->where('id', $demande->id)
                                    ->update(['cin' => $cin]);
                            }
                            
                            $updated++;
                        } else {
                            $this->warn("⚠️  User ID {$demande->user_id} not found or has no CIN for demande ID {$demande->id}");
                            $errors++;
                        }
                    } catch (\Exception $e) {
                        $this->error("❌ Error updating demande ID {$demande->id}: " . $e->getMessage());
                        $errors++;
                    }
                    
                    $progressBar->advance();
                }
            });

        $progressBar->finish();
        $this->newLine();

        // Display results
        if ($dryRun) {
            $this->info("🔍 DRY RUN RESULTS:");
            $this->info("   - Would update: {$updated} demandes");
        } else {
            $this->info("✅ COMPLETED:");
            $this->info("   - Updated: {$updated} demandes");
        }
        
        if ($errors > 0) {
            $this->warn("⚠️  Errors encountered: {$errors}");
        }

        // Verify results if not dry run
        if (!$dryRun) {
            $remainingCount = DB::table('demandes')
                ->whereNull('cin')
                ->whereNotNull('user_id')
                ->count();
                
            if ($remainingCount > 0) {
                $this->warn("⚠️  {$remainingCount} demandes still need CIN update (likely due to missing users)");
            } else {
                $this->info("🎉 All demandes now have CIN values!");
            }
        }

        return 0;
    }
}
