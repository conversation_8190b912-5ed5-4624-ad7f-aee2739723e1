<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('etat_dossier')->nullable();
            $table->text('comment_incomplete')->nullable();
            $table->text('comment_incoherent')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('etat_dossier');
            $table->dropColumn('comment_incomplete');
            $table->dropColumn('comment_incoherent');

        });
    }
};
