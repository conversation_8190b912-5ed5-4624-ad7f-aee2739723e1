<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Preparation extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    const ETAT = [
        'EN_ATTENTE'=> 1,
        'EN_COURS'=> 2,
        'DECISION'=> 3,
    ];

    protected $fillable = [
        'code',
        'type',
        'ndec',
        'etat',
        'date_envoi',
        'criteria',
        'annee_universitaire_id'
    ];

    protected $casts = [
        'criteria' => 'array'
    ];

    protected $appends = [
        'ndec_code'
    ];

    public function anneeUniversitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,'annee_universitaire_id');
    }
    public function getNdecCodeAttribute() : string
    {
        $clfinalLength = $this->code ? strlen($this->code) : 0;
        $format = "%0" . (5 - $clfinalLength) . "d";
        return $this->code . sprintf($format, $this->ndec );
    }
}
