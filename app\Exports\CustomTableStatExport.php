<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;


class CustomTableStatExport implements WithEvents, FromView
{
    protected $data;
    protected $year;
    protected $totals;
    protected $date_export;
    protected $office;


    public function __construct($data,$year,$totals, $office)
    {
        $this->data = $data;
        $this->year = $year;
        $this->totals = $totals;
        $this->office = $office;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');

    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }


    public function view(): View
    {
        return view('statistiques.customTableStat', [
            'data' => $this->data,
            'year' => $this->year,
            'totals' => $this->totals,
            'date_export' => $this->date_export,
            'office' => $this->office,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title
        ]);
    }
}

