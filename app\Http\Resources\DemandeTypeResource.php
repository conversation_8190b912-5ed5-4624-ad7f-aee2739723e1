<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class DemandeTypeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'type' => $this->type,
            'group' => $this->group,
            'title' => $this->title,
            'title_fr' => $this->title_fr,
            'title_ar' => $this->title_ar,
            'active' => $this->active,
            'order' => $this->order,
            'bourse' => $this->bourse,
            'aide_sociale' => $this->aide_sociale,
            'bourses_de_stage' => $this->bourses_de_stage,
            'bourse_insertion' => $this->bourse_insertion,
            'pret' => $this->pret,
            'last_config' => $this->lastConfig,
            'fils_count' =>$this->fils?->count(),
            'fils' => $this->fils,
//            'fils' => DemandeTypeResource::collection($this->fils),
//            'parent' => DemandeTypeResource::resolve($this->parent),
//            'diplomes' => $this->diplomes,

            'parent' =>$this->parent,
            'parent_id' =>$this->parent_id,
            'created_at' => $this->created_at->format('Y-m-d'),
            'start' => $this->start?->format('Y-m-d'),
            'start_fr' => $this->start?->format('d/m/Y'),
            'end' => $this->end?->format('Y-m-d'),
            'end_fr' => $this->end?->format('d/m/Y'),
            'date_dossier_end' => $this->date_dossier_end?->format('Y-m-d'),
            'date_dossier_end_fr' => $this->date_dossier_end?->format('d/m/Y'),
            'date_complement_end' => $this->date_complement_end?->format('Y-m-d'),
            'date_complement_end_fr' => $this->date_complement_end?->format('d/m/Y'),
            'date_contrat_pret_end' => $this->date_contrat_pret_end?->format('Y-m-d'),
            'date_contrat_pret_end_fr' => $this->date_contrat_pret_end?->format('d/m/Y'),
            'visibility' =>$this->visibility,

        ];
    }
}
