<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Notifications\CheckAllAndCreateStudentCompletedNotification;
use App\Notifications\ImportCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedVerificationAndCreatStudent implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $nb;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(Admin $user, int $nb)
    {
        $this->user = $user;
        $this->nb = $nb;
    }

    public function handle()
    {
        $this->user->notify(new CheckAllAndCreateStudentCompletedNotification($this->nb));
    }
}
