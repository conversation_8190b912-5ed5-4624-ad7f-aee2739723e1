<?php

namespace App\Http\Controllers;

use App\Http\Resources\AbsenceResource;
use App\Models\Absence;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AbsenceController extends Controller
{
    function __construct()
    {
        //        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index() //: AnonymousResourceCollection
    {
        return AbsenceResource::collection(Absence::with('student')->with('annee_universitaire')->with('etablissement')->get());
        //return Absence::with('student')->get();        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            //code...
            $request->validate([
                'annee_universitaire_id' => 'required|string',
                'student_id' => 'required|string',
                'nb_jours' => 'required|string',
                'etablissement_id' => 'required|string'
            ]);
            
            $absence = Absence::create([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_id' => $request->student_id,
                'nb_jours' => $request->nb_jours,
                'etablissement_id' => $request->etablissement_id,
            ]);
        } catch (\Throwable $th) {
            //throw $th;
            $request->validate([
                'annee_universitaire_id' => 'required|string',
                'student_name' => 'required|string',
                'student_cin' => 'required|string',
                'nb_jours' => 'required|string',
                'etablissement_id' => 'required|string'
            ]);

            $absence = Absence::create([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_name' => $request->student_name,
                'student_cin' => $request->student_cin,
                'nb_jours' => $request->nb_jours,
                'etablissement_id' => $request->etablissement_id,
            ]);
        }
        

        

        return response(new AbsenceResource($absence), 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Absence $absence
     * @return AbsenceResource
     */
    public function show(Absence $absence): AbsenceResource
    {
        return new AbsenceResource($absence);
    }

    public function edit(Request $request): Response
    {
        try {
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'student_id' => 'required|string',
                'nb_jours' => 'required|string',
                'etablissement_id' => 'required|string',
            ]);
    
    
            $absence = Absence::findOrFail($request->id)->update([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_id' => $request->student_id,
                'nb_jours' => $request->nb_jours,
                'etablissement_id' => $request->etablissement_id,
            ]);  
        } catch (\Throwable $th) {
            //throw $th;
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'student_name' => 'required|string',
                'student_cin' => 'required|string',
                'nb_jours' => 'required|string',
                'etablissement_id' => 'required|string',
            ]);
    
    
            $absence = Absence::findOrFail($request->id)->update([
                'id' => $request->id,
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_name' => $request->student_name,
                'student_cin' => $request->student_cin,
                'nb_jours' => $request->nb_jours,
                'etablissement_id' => $request->etablissement_id,
            ]);  
        }
              

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Absence $absence
     * @return Response
     */
    public function destroy(Absence $absence): Response
    {
        $absence->delete();

        return response("", 204);
    }


}
