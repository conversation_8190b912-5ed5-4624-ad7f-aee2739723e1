<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Diplome extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'code',
        'active',
        'nbr_annee_etude',
        'cycle',
        'troisieme_cycle',
    ];

    protected $casts = [
        'active' => 'boolean',
        'troisieme_cycle' => 'boolean',
    ];



    public function filieres() : HasMany
    {
        return $this->hasMany(Filiere::class,'code_diplome','code');
    }

//    public function etablissements(): BelongsToMany
//    {
//        return $this->belongsToMany(Etablissement::class, 'diplome_etablissements', 'code_diplome', 'code_etab','code','code');
//    }

    public function demandeTypes(): BelongsToMany
    {
        return $this->belongsToMany(DemandeType::class, 'diplome_demande_types', 'code_diplome', 'demande_type_id','code','id');
    }

    public function diplomesEtablissements() : HasMany
    {
        return $this->hasMany(DiplomeEtablissement::class,'code_diplome','code');
    }
}
