<?php

namespace App\Notifications;

use App\Models\ExportedFile;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class RestoreNotCompletedNotification extends Notification
{
    use Queueable;

    public $syncCount;

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }
//        $syncCount = ['decision_favorable' => 0, 'decision_non_favorable' => 0, 'decision_ambigu' => 0];

        return [
            "title" => "Restore Not Completed",
            "subtitle" => " Restore Not Completed , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Restore Not Complete ",
            "subtitle_fr" => " Restore Not Complete à : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => "لم تكتمل الاستعادة",
            "subtitle_ar" => " لم تكتمل الاستعادة : " .  Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "recycle",
            "avatarAlt" => "Restore",
            "avatarText" => "Restore",
            "avatarColor" => "error",
            "type" => "Restore",
            "target_id" => '',
            "target" => '',
            "model" => "Preparation",
            "url" => '',

        ];
    }
}
