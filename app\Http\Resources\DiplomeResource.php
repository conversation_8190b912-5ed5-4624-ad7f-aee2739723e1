<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class DiplomeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_fr' => $this->name_fr,
            'name_ar' => $this->name_ar,
            'code' => $this->code,
            'active' => $this->active,
            'troisieme_cycle' => $this->troisieme_cycle,
            'nbr_annee_etude' => $this->nbr_annee_etude,
            'cycle' => $this->cycle,
            'diplomesEtablissements' => $this->diplomesEtablissements,
//            'etablissements' => $this->etablissements,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
//            'etablissements' => EtablissementResource::collection($this->etablissements),
        ];
    }
}
