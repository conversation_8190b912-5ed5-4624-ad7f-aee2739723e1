<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class WaitingStudentResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'firstName' => $this->firstName,
            'name' => $this->name,
            'name_ar' => $this->name_ar,
            'username' => $this->username,
            'email' => $this->email,
            'password' => $this->password,
            'phoneNumber' => $this->phoneNumber,
            'address' => $this->address,
            'annee_bac' => $this->annee_bac,
            'num_bac' => $this->num_bac,
            'cin' => $this->cin,
            'matricule' => $this->matricule,
            'num_passport' => $this->num_passport,
            'nationality_id' => $this->nationality_id,
            'country_id' => $this->country_id,
            'code_gouv' => $this->code_gouv,
            'delegation_id' => $this->delegation_id,
            'zipCode' => $this->zipCode,
            'sex' => $this->sex,
            'type' => $this->type,
            'pere' => $this->pere,
            'mere' => $this->mere,
            'profile_photo' => $this->profile_photo,
            'phoneNumber2' => $this->phoneNumber2,
            'code_postal' => $this->code_postal,
            'email_perso' => $this->email_perso,
            'date_naissance' => $this->date_naissance,//->format('Y-m-d'),
            'address_naissance' => $this->address_naissance,
            'created_at' => $this->created_at->format('Y-m-d'),
            'etudiantAnneeUniversitaires' => EtudiantAnneeUniversitairesResource::collection($this->etudiantAnneeUniversitaires),
            'gouvernorat' => $this->gouvernorat,
            'nationality' => $this->nationality,
            'anneeBac' => $this->anneeBac,

        ];
    }
}
