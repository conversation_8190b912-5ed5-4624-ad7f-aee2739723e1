<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Models\ExportedFile;
use App\Notifications\ExportCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedExport implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $type;
    public $id;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(Admin $user, ?string $type , ?int $id )
    {
        $this->user = $user;
        $this->type = $type;
        $this->id = $id;
    }

    public function handle()
    {

        if($this->id != null){
            $row = ExportedFile::find($this->id);
            $row->etat = 'termine';
            $row->attached_file = 'demande_'.$this->type.'_'.$this->id.'.xlsx';
            $row->save();
            $this->user->notify(new ExportCompletedNotification($row));
        }
    }
}
