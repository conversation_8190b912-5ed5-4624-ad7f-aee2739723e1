<?php

namespace App\Console\Commands;

use App\Models\AnneeUniversitaire;
use App\Models\Demande;
use App\Models\DemandeAnneeEtude;
use Illuminate\Console\Command;

class SetIsBourseInsertionIfTypeBourseInsertion extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'demande:set-bourse-insertion-if-type';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Set etudiant_annee_universitaire_id for all demandes';

  /**
   * Execute the console command.
   */
  public function handle()
  {
    $this->info('Starting to update demandes...');

    $count = 0;

    try {
      Demande::chunk(500, function ($demandes) use (&$count) {
        foreach ($demandes as $demande) {
          try {
            if ($demande->demandeType->bourse_insertion) {
              $demande->is_bourse_insertion = true;
              $demande->save();
              $count++;
            }
          } catch (\Exception $e) {
            $message = "Error processing demande ID {$demande->id} etudiant {$demande->user->name}( {$demande->user->cin} ) etat {$demande->etat} : {$e->getMessage()}";

            $this->error($message); // Console output for command
            \Log::error($message); // Laravel log
            continue; // Continue with next record
          }
        }
      });
    } catch (\Exception $e) {
      $message = "Error in chunk processing: {$e->getMessage()}";
      $this->error($message); // Console output for command
      \Log::error($message); // Laravel log
    }

    $this->info("Completed! Updated {$count} demandes.");
  }
}
