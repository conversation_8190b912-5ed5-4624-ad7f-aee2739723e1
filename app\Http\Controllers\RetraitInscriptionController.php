<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Resources\RetraitInscriptionResource;
use App\Models\RetraitInscription;
use Illuminate\Http\Response;



class RetraitInscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index() //: AnonymousResourceCollection
    {
        return RetraitInscriptionResource::collection(RetraitInscription::with('student')->with('annee_universitaire')->with('etablissement')->with('motif_retrait_inscription')->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            //code...
            $request->validate([
                'annee_universitaire_id' => 'required|string',
                'student_id' => 'string|required',
                'etablissement_id' => 'required|string',
                'motif_retrait_inscription_id' => 'required'
            ]);
            
            $retraitInscription = RetraitInscription::create([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_id' => $request->student_id,
                'motif_retrait_inscription_id' => $request->motif_retrait_inscription_id,
                'etablissement_id' => $request->etablissement_id,
            ]);
        } catch (\Throwable $th) {
            //throw $th;
            $request->validate([
                'annee_universitaire_id' => 'required|string',
                'student_name' => 'required|string',
                'student_cin' => 'required|string',
                'motif_retrait_inscription_id' => 'required|string',
                'etablissement_id' => 'required|string'
            ]);
            
            $retraitInscription = RetraitInscription::create([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_name' => $request->student_name,
                'student_cin' => $request->student_cin,
                'motif_retrait_inscription_id' => $request->motif_retrait_inscription_id,
                'etablissement_id' => $request->etablissement_id,
            ]);
        }
        

        return response(new RetraitInscriptionResource($retraitInscription), 201);
    }

    /**
     * Display the specified resource.
     *
     * @param RetraitInscription $retraitInscription
     * @return RetraitInscriptionResource
     */
    public function show(RetraitInscription $retraitInscription): RetraitInscriptionResource
    {
        return new RetraitInscriptionResource($retraitInscription);
    }

    public function edit(Request $request): Response
    {
        try {
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'student_id' => 'required|string',
                'motif_retrait_inscription_id' => 'required|string',
                'etablissement_id' => 'required|string',
            ]);
    
    
            $retraitInscription = RetraitInscription::findOrFail($request->id)->update([
                'id' => $request->id,
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_id' => $request->student_id,
                'motif_retrait_inscription_id' => $request->motif_retrait_inscription_id,
                'etablissement_id' => $request->etablissement_id,
            ]); 
        } catch (\Throwable $th) {
            //throw $th;
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'student_name' => 'required|string',
                'student_cin' => 'required|string',
                'motif_retrait_inscription_id' => 'required|string',
                'etablissement_id' => 'required|string',
            ]);
    
    
            $retraitInscription = RetraitInscription::findOrFail($request->id)->update([
                'id' => $request->id,
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_name' => $request->student_name,
                'student_cin' => $request->student_cin,
                'motif_retrait_inscription_id' => $request->motif_retrait_inscription_id,
                'etablissement_id' => $request->etablissement_id,
            ]); 
        }
               

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param RetraitInscription $retraitInscription
     * @return Response
     */
    public function destroy(RetraitInscription $retraitInscription): Response
    {
        $retraitInscription->delete();

        return response("", 204);
    }


}
