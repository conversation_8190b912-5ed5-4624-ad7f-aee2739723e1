<?php

namespace App\Helpers;

use GuzzleHttp\Client;

class Helpers {

    public static function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }

    public static function clearCacheIdp($items = [])
    {
        $client = new Client();
        $resp = $client->post(env("IDP_URL")."/api/clearCacheIdp", [
            'form_params' => [
                'items' => $items,
            ]
        ]);

        $body = (string) $resp->getBody();

        return $body;
    }
}

