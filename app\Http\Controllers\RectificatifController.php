<?php

namespace App\Http\Controllers;

use App\Exports\RectificatifExport;
use App\Http\Resources\RectificatifResource;
use App\Models\Decision;
use App\Models\Rectificatif;
use App\Models\RectificatifNumero;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class RectificatifController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return RectificatifResource::collection(Rectificatif::with('annee_universitaire')->with('rectificatif_numero')->get());
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //code...
        $request->validate([
            'cin' => 'required|max:8|min:8|string',
            'catb' => 'required|string',
            'nom' => 'required|string',
            //'datnais' => 'required|string',
            'sexe' => 'required|string',
            'discip' => 'required|string',
            'anet' => 'required|string',
            'fac' => 'required|string',
            'type' => 'required|string',
            'office' => 'required|string',
            'annee_id' => 'required|numeric',
            'rectificatif_numero_id' => 'required|string',
        ]);

        try {
            $decision = Decision::where('cin', $request->cin)
//                ->where('anet', $request->anet)
                ->where('annee_id', $request->annee_id)->firstOrFail();
        } catch (\Throwable $th) {
            return response("Decision inexistante pour les données saisies. Vérifiez : CIN, ANET et Année Universitaire", 404);
        }

        $rectificatifNum = RectificatifNumero::findOrFail($request->rectificatif_numero_id);

        $rectif = Rectificatif::create([
            'rectificatif_numero_id' => $request->rectificatif_numero_id,
            'annee_id' =>  $request->annee_id,
            'cin' => $request->cin,
            'catb' => $request->catb,
            'lot' => $decision->lot,
            'nom' => $request->nom,
            'datnais' => $decision->datnais,
            'gouv' => $decision->gouv,
            'sexe' => $request->sexe,
            'profp' => $decision->profp,
            'discip' => $request->discip,
            'anet' => $request->anet,
            'fac' => $request->fac,
            'univ' => $decision->univ,
            'inf' => $decision->inf,
            'sup' => $decision->sup,
            'enf' => $decision->enf,
            'revp' => $decision->revp,
            'revm' => $decision->revm,
            'avis' => $decision->avis,
            'res' => $decision->res,
            'moy' => $decision->moy,
            'natdec' => $decision->natdec,
            'situa' => $decision->situa,
            'nmb' => $decision->nmb,
            'mbs' => $decision->mbs,
            'mf' => $decision->mf,
            'ndec' => $rectificatifNum->num_decision,
            'dat' => $decision->dat,
            'montanttotal' => $decision->montanttotal,
            'pourcentage' => $decision->pourcentage,
            'office' => $request->office,
            'type' => $request->type,
        ]);

        return response(new RectificatifResource($rectif), 201);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Rectificatif  $rectificatif
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'cin' => 'required|digits:8|string',
                'catb' => 'required|string',
                'nom' => 'required|string',
                'datnais' => 'required|string',
                'sexe' => 'required|string',
                'discip' => 'required|string',
                'anet' => 'required|string',
                'fac' => 'required|string',
                'type' => 'required|string',
                'office' => 'required|string',
                'annee_id' => 'required|numeric',
                'rectificatif_numero_id' => 'required|numeric',
            ]);


            $rectif = Rectificatif::findOrFail($request->id)->update([
                'cin' => $request->cin,
                'catb' => $request->catb,
                'nom' => $request->nom,
                'datnais' => $request->datnais,
                'sexe' => $request->sexe,
                'discip' => $request->discip,
                'anet' => $request->anet,
                'fac' => $request->fac,
                'type' => $request->type,
                'office' => $request->office,
                'annee_id' => $request->annee_id,
            ]);

        return response("created" , 201);

    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Rectificatif  $rectificatif
     * @return \Illuminate\Http\Response
     */
    public function destroy(Rectificatif $rectificatif)
    {
        $rectificatif->delete();

        return response("", 204);
    }

    public function exportExcel(Request $request)
    {
//        return response()->json($request->all());

        $export = new RectificatifExport($request);
        return Excel::download($export, 'rectificatifs.xlsx');
    }
}
