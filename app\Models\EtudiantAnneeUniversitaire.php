<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class EtudiantAnneeUniversitaire extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.secondConnection'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.secondConnection');
    }

    protected $fillable = [
        'user_id',
        'waiting_user_id',
        'filiere_id',
        'code_etab',
        'code_diplome',
        'annee_etude',
        'annee_universitaire_id',
        'resultat_id',
        'moyenne',
        'credit',
        'is_nouveau_bachelier',
    ];
//    protected $attributes = [
//        'code_univ'
//    ];

    public function resultat() : BelongsTo
    {
        return $this->setConnection( config('database.default') )->belongsTo(Resultat::class, 'resultat_id', 'id');
    }

    public function demandes() : HasMany
    {
        return $this->setConnection(config('database.default'))->hasMany(Demande::class,'student_id','id');
    }
    public function filiere() : BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Filiere::class,'filiere_id','id');
    }
    public function etablissement() : BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Etablissement::class,'code_etab','code');
    }
//    public function getCodeUnivAttribute() : ?string
//    {
//        return $this->etablissement?->universite?->code_univ;
//    }
    public function diplome() : BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Diplome::class,'code_diplome','code');
    }

    public function anneeUniversitaire() : BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(AnneeUniversitaire::class,'annee_universitaire_id','id');
    }

    public function user(): BelongsTo
    {
        return $this->setConnection(config('database.secondConnection'))->belongsTo(User::class,'user_id','id');
    }

    public function waitingUser(): BelongsTo
    {
        return $this->setConnection(config('database.secondConnection'))->belongsTo(WaitingUser::class,'waiting_user_id','id');
    }

}
