<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fiche_organismes', function (Blueprint $table) {
            $table->id();
            $table->string('organisme');
            $table->unsignedBigInteger('code_organisme_id');
            $table->string('type');
            $table->date('date_emission');
            $table->bigInteger('nb_total');
            $table->float('montant_total', 12,3);
            $table->float('tax_total', 12,3);
            $table->bigInteger('premier');
            $table->bigInteger('dernier');
            $table->float('montant_ttc', 12,3);
            $table->string('fichier')->nullable();

            $table->unsignedBigInteger('annee_universitaire_id')->nullable();
            $table->timestamps();

            $table->foreign('annee_universitaire_id')->references('id')->on('annee_universitaires');
            $table->foreign('code_organisme_id')->references('id')->on('code_organismes');
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fiche_organismes');
    }
};
