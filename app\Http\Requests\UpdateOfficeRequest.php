<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateOfficeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'nullable|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'code' => 'required|string|unique:offices,code,'.$this->id,
            'active' => 'required|boolean',
            'adresse' => 'nullable|string',
            'tel' => 'nullable|numeric',
            'fax' => 'nullable|numeric',
            'site_web' => 'nullable|string',
            'gouvernorat' => 'nullable|string',
            'gouvernorat_ar' => 'nullable|string',
            'email' => 'nullable|string|email',
            'parent_id' => 'nullable|string|numeric',
        ];
    }


    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }
}
