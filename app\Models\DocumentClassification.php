<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;


class DocumentClassification extends Model implements Auditable
{
    use AuditableTrait, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

//    protected $with = [
//        'classification',
//    ];

    protected $fillable = [
        'code',
        'type',
        'title',
        'title_fr',
        'title_ar',
        'active',
        'classification_id',
        'resultat',
        'boursier',
        'document_file',
    ];

    public $appends=[
        'document_file_url',
    ];

    public function getDocumentFileUrlAttribute(){
        if ( $this->document_file ) {
            return asset('uploads/document_classification_files/'.$this->classification_id.'/'.$this->document_file);
        }
        return null;
    }

    public function classification() : BelongsTo
    {
        return $this->belongsTo(Classification::class, 'classification_id');
    }


    public function documentsClassificationsDemands(): HasMany
    {
        return $this->hasMany(DocumentClassificationDemande::class);
    }


    public function resultatData(): BelongsTo
    {
        return $this->belongsTo(Resultat::class, 'resultat' , 'id');
    }

}
