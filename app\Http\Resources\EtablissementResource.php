<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class EtablissementResource extends JsonResource
{
    public static $wrap = false;
    public bool $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_fr' => $this->name_fr,
            'name_ar' => $this->name_ar,
            'code' => $this->code,
            'password' => $this->password,
            'code_office' => $this->code_office,
            'code_ministre' => $this->code_ministre,
            'code_dir_reg' => $this->code_dir_reg,
            'code_univ' => $this->code_univ,
            'code_gouv' => $this->code_gouv,
            'office' => $this->office,
            'gouvernorat' => $this->gouvernorat,
            'universite' => $this->universite,
//            'filieres' => FiliereResource::collection($this->filieres),
//            'diplomes' => DiplomeResource::collection($this->diplomes),
//            'diplomes' => $this->diplomes,
            'diplomesEtablissements' => $this->diplomesEtablissements,

            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
