<?php

namespace App\Exports;

use App\Models\Attestation;
use App\Models\Etablissement;
use App\Models\Reclamation;
use App\Models\Rectificatif;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AttestationExport implements FromCollection,WithHeadings
{

    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function headings():array{
        return[

            'cin',
            'nom_ar',
            'nom_fr',
            'reclamation_type',
            'detail',
            'etat',
        ];
    }

    public function collection()
    {
        $request = $this->request;
        $etablissement_id = $request->query('etablissement_id', null);
        $university_id = $request->query('university_id', null);

        $attestations = Attestation::with('student')->withOut('student.attestations')
            ->with('attestationType')
            ->with('etablissement')
            ->with('office');
        if ($request->has('year') && $request->year && $request->year !== 'all') {
            $attestations = $attestations->where('year', $request->year);
        }
        if ($request->has('typeId') && $request->typeId) {
            $attestations = $attestations->where('attestation_types_id', $request->typeId);
        }
        if ($request->has('etat') && $request->etat) {
            $attestations = $attestations->where('status', $request->etat);
        }
        if ($request->has('startDate') && $request->startDate && $request->has('endDate') && $request->endDate) {
            $attestations = $attestations->whereBetween('created_at', [$request->startDate, $request->endDate]);
        } else{
            if ($request->has('startDate') && $request->startDate) {
                $attestations = $attestations->whereDate('created_at', '>=', $request->startDate);
            }
            if ($request->has('endDate') && $request->endDate) {
                $attestations = $attestations->whereDate('created_at', '<=', $request->endDate);
            }
        }

        if ($university_id){
            if ($etablissement_id){
                $attestations = $attestations->where('etablissement_id', $etablissement_id);
            } else {
                $etabIds = Etablissement::where('code_univ', $university_id)->pluck('id');
                $attestations = $attestations->whereIn('etablissement_id', $etabIds);
            }
        }
        else {
            if ($etablissement_id){
                $attestations = $attestations->where('etablissement_id', $etablissement_id);
            }
        }
        if ($request->q) {
            $userIds = User::where(function (Builder $query) use ($request) {
                $query
                    ->orWhere('email', 'like', '%' . $request->q . '%')
                    ->orWhere('name', 'like', '%' . $request->q . '%')
                    ->orWhere('name_ar', 'like', '%' . $request->q . '%')
                    ->orWhere('firstName', 'like', '%' . $request->q . '%')
                    ->orWhere('cin', 'like', '%' . $request->q . '%')
                    ->orWhere('num_bac', 'like', '%' . $request->q . '%')
                    ->orWhere('matricule', 'like', '%' . $request->q . '%')
                    ->orWhere('num_passport', 'like', '%' . $request->q . '%')
                ;
            })->pluck('id');
            $attestations = $attestations->whereIn('student_id', $userIds);
        }

        if ($request->orderByColumn && $request->orderByDirection) {
            $attestations = $attestations->orderBy($request->orderByColumn, $request->orderByDirection)->get();
        } else {
            $attestations = $attestations->orderBy('id', 'desc')->get();
        }



        return $attestations->map( fn (Attestation $attestation) => [
            'cin' => $attestation->student?->cin ?? '',
            'nom_ar' => $attestation->student?->name_ar ?? '',
            'nom_fr' => $attestation->student?->name ?? '',
            'reclamation_type' => $attestation->attestationType?->title_fr ?  $attestation->attestationType?->title_fr.' / '.$attestation->attestationType?->title_ar  : '',
            'detail' => $attestation->detail ?? '',
            'etat' => $attestation->status ?? '',
        ]);
    }
}
