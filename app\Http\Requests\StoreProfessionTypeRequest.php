<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProfessionTypeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'code' => 'required|unique:professions,code|integer',
            ];
    }


}
