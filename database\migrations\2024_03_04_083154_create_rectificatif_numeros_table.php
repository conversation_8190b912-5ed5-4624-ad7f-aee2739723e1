<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rectificatif_numeros', function (Blueprint $table) {
            $table->id();
            $table->string('num_decision');
            $table->unsignedBigInteger('annee_universitaire_id');

            $table->foreign('annee_universitaire_id')->references('id')->on('annee_universitaires');
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rectificatif_numeros');
    }
};
