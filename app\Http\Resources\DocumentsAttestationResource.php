<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class DocumentsAttestationResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'type' => $this->type,
            'title' => $this->title,
            'title_fr' => $this->title_fr,
            'title_ar' => $this->title_ar,
            'active' => $this->active,
            'attestation_type_id' =>$this->attestation_type_id,
            //'obligatoire' => $this->obligatoire,
            //'classification_id' =>$this->classification_id,
            //'classification' => $this->classification,
            //'boursier' => $this->boursier,
            //'resultat' => $this->resultat,
            //'document_file' => $this->document_file,
            //'document_file_url' => $this->document_file_url,
//            'resultatData' => $this->resultatData,
            //'created_at' => $this->created_at->format('Y-m-d'),
        ];
    }
}
