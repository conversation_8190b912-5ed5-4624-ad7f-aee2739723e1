<?php

namespace App\Exports;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RegisteredStudentExport implements FromCollection,WithHeadings
{

    private mixed $annee_bac;
    private mixed $type;

    public function __construct($annee_bac,$type){
        $this->annee_bac = $annee_bac;
        $this->type = $type;
    }


    public function headings():array{
        return[
            'id',

            'name',
            'name_ar',
            'firstName',
            'email',

            'annee_bac',
            'num_bac',
            'cin',
            'matricule',
            'num_passport',

            'status',

            'type',

            'sex',
            'date_naissance',
            'phoneNumber',
            'phoneNumber2',
            'nationality',
            'gouvernorat',
            'delegation',
            'code_postal',
            'address',

            'pere',
            'mere',
            'email_perso',
            'created_at',
            'updated_at'
        ];
    }

    public function collection()
    {
        return
            DB::connection(config('database.secondConnection'))->table('users')
            ->select(
            'users.id',
            'users.name',
            'users.name_ar',
            'users.firstName',
            'users.email',

            'a.title as annee_bac',
            'users.num_bac',
            'users.cin',
            'users.matricule',
            'users.num_passport',

            'users.status',

            'users.type',

            'users.sex',
            'users.date_naissance',
            'users.phoneNumber',
            'users.phoneNumber2',
            'c.name_fr as nationality',
            'g.name_fr as gouvernorat',
            'd.name_fr as delegation',
            'users.code_postal',
            'users.address',

            'users.pere',
            'users.mere',
            'users.email_perso',
            'users.created_at',
            'users.updated_at'
        )
            ->when($this->type, function ($query) {
                return $query->where('users.type', $this->type);
            })
            ->when($this->annee_bac, function ($query) {
                return $query->where('users.annee_bac', $this->annee_bac);
            })
            ->leftJoin(DB::connection(config('database.default'))->getDatabaseName() .'.countries as c', 'c.id', '=', 'users.nationality_id')
            ->leftJoin(DB::connection(config('database.default'))->getDatabaseName() .'.gouvernorats as g', 'g.code', '=', 'users.code_gouv')
            ->leftJoin(DB::connection(config('database.default'))->getDatabaseName() .'.delegations as d', 'd.id', '=', 'users.delegation_id')
            ->leftJoin(DB::connection(config('database.default'))->getDatabaseName() .'.annee_bacs as a', 'a.id', '=', 'users.annee_bac')
            ->get();
}

}
