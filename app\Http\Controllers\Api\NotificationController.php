<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        /** @var Admin $user */
        $user = Auth::user();
        $unreadNotifications = $user->unreadNotifications;
        $notifications = $user->notifications;

        return response()->json([
            "unreadNotifications"=> $unreadNotifications,
            "notifications"=> $notifications,
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     */
    public function markAsRead(Request $request): Response
    {
        auth()->user()
            ->unreadNotifications
            ->when($request->id , function ($query) use ($request) {
                return $query->where('id', $request->id);
            })
            ->markAsRead();
        return response()->noContent();
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function readAll(): Response
    {
        auth()->user()
            ->unreadNotifications
            ->markAsRead();
        return response()->noContent();
    }

}
