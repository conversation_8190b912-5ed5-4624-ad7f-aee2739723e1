<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCatbRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string|unique:catbs,code,'.$this->id,
            'name' => 'required|string',
            'tunisien' => 'required|boolean',
            'troisieme_cycle' => 'required|boolean',
            'active' => 'nullable|boolean',
            'type_etude' => 'nullable|string',
            ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
            'troisieme_cycle' => Helpers::toBoolean($this->troisieme_cycle),
            'tunisien' => Helpers::toBoolean($this->tunisien),
        ]);
    }




}
