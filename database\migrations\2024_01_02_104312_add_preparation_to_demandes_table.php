<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('ndec')->nullable();
            $table->integer('preparation_bourse_id')->nullable();
            $table->integer('preparation_insertion_id')->nullable();
            $table->integer('preparation_pret_id')->nullable();
            $table->integer('preparation_aide_sociale_id')->nullable();
            $table->integer('preparation_stage_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('ndec');
            $table->dropColumn('preparation_bourse_id');
            $table->dropColumn('preparation_insertion_id');
            $table->dropColumn('preparation_pret_id');
            $table->dropColumn('preparation_aide_sociale_id');
            $table->dropColumn('preparation_stage_id');

        });
    }
};
