<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demande_types', function (Blueprint $table) {
            $table->boolean('bourse_insertion')->nullable();
            $table->boolean('pret')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demande_types', function (Blueprint $table) {
            $table->dropColumn('bourse_insertion');
            $table->dropColumn('pret');

        });
    }
};
