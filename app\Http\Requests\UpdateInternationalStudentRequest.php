<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateInternationalStudentRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'num_passport' => 'required',
            'matricule' => ['required','min:8','max:8', Rule::unique('international_students')->ignore($this->id)->whereNull('deleted_at')],
            'name' => 'required',
            'firstName' => 'nullable',
            'phoneNumber' => 'sometimes|nullable',
            'address' => 'sometimes|nullable',
            'zipCode' => 'sometimes|nullable',
            'dateOfBirth' => 'required',
            'placeOfBirth' => 'nullable',
            'sex' => 'nullable',
            'nationality_id' => 'required',
            'code_etab' => 'nullable',
            'filiere_id' => 'nullable',
            'foyer' => 'sometimes|nullable',
            'annee_bac' => 'nullable',
            'annee_universitaire' => 'required',
        ];
    }
}
