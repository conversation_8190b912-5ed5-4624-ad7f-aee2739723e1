<?php

namespace App\Http\Controllers\Api;

use App\Exports\DemandesExport;
use App\Exports\PreparationExport;
use App\Exports\PreparationMapExport;
use App\Http\Controllers\Controller;
use App\Jobs\NotifyUserOfCompletedExport;
use App\Jobs\NotifyUserOfCompletedPreparationExport;
use App\Jobs\ProcessCreatePreparation;
use App\Jobs\ProcessPreparationExport;
use App\Jobs\ProcessRestorePreparation;
use App\Jobs\ProcessSyncPreparation;
use App\Models\Admin;
use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use App\Models\AttestationType;
use App\Models\Decision;
use App\Models\Demande;
use App\Models\ExportedFile;
use App\Models\Preparation;
use App\Models\RetraitInscription;
use App\Models\User;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class PreparationController extends Controller
{

    public function index(Request $request, $type): JsonResponse
    {
        $files = Preparation::where('type', $type)
            ->when(
                $request->has('ndec'),
                function ($query) use ($request) {
                    return $query->where('code', 'like', '%' . $request->ndec . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request->annee_universitaire_id);
                }
            )
            ->orderBy('id', 'desc')
            ->with('anneeUniversitaire')
            ->paginate(
                $request->input('perPage') ?? config('constants.pagination'),
            );
        return response()->json($files, 200);
    }


    /**
     * @param Request $request
     * @return Application|ResponseFactory|Response|JsonResponse
     */
    public function createPreparation(Request $request): Application|ResponseFactory|Response|JsonResponse
    {
        $validator1 = Validator::make($request->all(), [
            'code' => 'required',
            'demande_type_code' => 'required',
            'type_preparation' => 'required',
            'annee_universitaire_id' => 'required',
            'revenu_net_min' => 'nullable|integer',
            'revenu_net_max' => 'nullable|integer',
            'attestation_status' => 'nullable',
            'attestation_pret_status' => 'nullable',
            'retrait_status' => 'nullable',
            'demande_type_ids' => 'required|array',
            'budget_max' => 'nullable',
            'montant_unite' => 'nullable',
            'by_score' => 'nullable',
        ]);
        if ($validator1->fails()) {
            return response()->json([
                'message' => 'Validations fails',
                'errors' => $validator1->errors(),
            ], 422);
        }
        $data = $validator1->validated();

        $anneeUniversitaire = AnneeUniversitaire::find($data['annee_universitaire_id']);
        $demandes = Demande::select(
            [
                'id', 'code', 'user_id', 'etat',
                'annee_universitaire_id', 'demande_type_id', 'revenu_net',
                'etat_bourse', 'etat_bourse_insertion', 'etat_pret', 'etat_bourse_stage', 'etat_aide_sociale',
                'lot',
            ])
            ->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION'])
            ->where('annee_universitaire_id', $request->annee_universitaire_id);

        if ($request->demande_type_ids && count($request->demande_type_ids) > 0) {
            $demandes = $demandes->whereIn('demande_type_id', $data['demande_type_ids']);
        }

        $usersRetraitids = [];
        $usersRetraitidsNo = [];
        if ($request->attestation_status === "true") {
            $attestationTypesNonBoursier = AttestationType::where('non_boursier',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
        }
        if ($request->attestation_status === "false") {
            $attestationTypesNonBoursier = AttestationType::where('non_boursier',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)
                ->where('year', $anneeUniversitaire->title)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
        }
        if ($request->attestation_pret_status === "true") {
            $attestationTypesNonBoursier = AttestationType::where('non_pret',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
        }
        if ($request->attestation_pret_status === "false") {
            $attestationTypesNonBoursier = AttestationType::where('non_pret',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)
                ->where('year', $anneeUniversitaire->title)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
        }
        if ($request->retrait_status === "true") {
            $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_cin');
            $usersIds = User::where('cin', 'IN', $usersRetrait)->pluck('id');
            $usersRetraitids = $usersIds;
            $demandes = $demandes->whereIn('user_id', $usersIds);
        }
        if ($request->retrait_status === "false") {
            $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_cin');
            $usersIds = User::where('cin', 'IN', $usersRetrait)->pluck('id');
            $usersRetraitidsNo = $usersIds;
            $demandes = $demandes->whereNotIn('user_id', $usersIds);
        }
        if ($request->type_preparation) {
            if ($request->type_preparation === 'bourse') {
                $demandes = $demandes->where('etat_bourse', '=', Demande::ETAT_BOURSE['ELIGIBLE']);
            }
            if ($request->type_preparation === 'insertion') {
                $demandes = $demandes->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']);
            }
            if ($request->type_preparation === 'pret') {
                $demandes = $demandes->where('etat_pret', '=', Demande::ETAT_PRET['ELIGIBLE']);
            }
            if ($request->type_preparation === 'aide_sociale') {
                $demandes = $demandes->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['ELIGIBLE']);
            }
            if ($request->type_preparation === 'stage') {
                $demandes = $demandes->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['ELIGIBLE']);
            }

        }

        if ($request->has('revenu_net_min') && $request->revenu_net_min != '') {
            $demandes = $demandes->where('revenu_net', '>=', $request->revenu_net_min);
        }
        if ($request->has('revenu_net_max') && $request->revenu_net_max != '') {
            $demandes = $demandes->where('revenu_net', '<=', $request->revenu_net_max);
        }

        if ($request->has('by_score') && $request->by_score === 'active'){
            $demandes = $demandes->orderBy('score_total', 'desc');
        } else {
            $demandes = $demandes->orderBy('id');
        }

        $limit = null;
        if ($request->has('budget_max') && $request->has('montant_unite') && $request->budget_max != '' && $request->montant_unite != '') {
            $limit = ($request->budget_max / $request->montant_unite);
        }

        if ($limit) {
            $demandes = $demandes->skip(0)->take($limit)->get();
        } else {
            $demandes = $demandes->get();
        }
        if (!$demandes->isEmpty() &&
            ($request->type_preparation === 'bourse' ||
                $request->type_preparation === 'insertion' ||
                $request->type_preparation === 'pret' ||
                $request->type_preparation === 'aide_sociale' ||
                $request->type_preparation === 'stage')
        ) {

            $demandesUnique = $demandes->unique('user_id', true);
            $demandesDupes = $demandes->diff($demandesUnique);
            if (!$demandesDupes->isEmpty()) {
                return response()->json([
                    'errors' => $validator1->errors(),
                    'duplicate' => 'oui',
                    'message' => 'La liste contient des demandes en double',
                    'demandesDupes' => $demandesDupes
                ], 422);
            }


            $preparationsSameYearSameCode = Preparation::where('code', $request->code)
                ->where('annee_universitaire_id', $request->annee_universitaire_id)
                ->orderBy('ndec', 'desc');
            $preparationsMaxNdec = $preparationsSameYearSameCode->get()->max('ndec') ?? 0;
            $prep = Preparation::create([
                'code' => $request->code,
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'ndec' => $preparationsMaxNdec + 1,
                'criteria' => $request->all(),
                'type' => $request->type_preparation,
                'etat' => Preparation::ETAT['EN_ATTENTE'],
            ]);

            $prepId = $prep->id;
            $demandesIds = $demandes->pluck('id')->toArray();
            $type_preparation = $request->type_preparation;

            dispatch(new ProcessCreatePreparation($prepId,$demandesIds,$type_preparation, auth()->user()));

            return response()->json($prep, 200);
        }

        return response()->json([
            'message' => 'La liste des demandes est vide',
            'usersRetraitids' => $usersRetraitids,
            'usersRetraitidsNo' => $usersRetraitidsNo,
            'errors' => ['liste' => 'La liste des demandes est vide'],
        ], 422);


    }


    /**
     * @param Request $request
     * @return Application|ResponseFactory|Response|JsonResponse
     */
    public function verifyPreparation(Request $request): Application|ResponseFactory|Response|JsonResponse
    {
        $validator1 = Validator::make($request->all(), [
            'code' => 'required',
            'demande_type_code' => 'required',
            'type_preparation' => 'required',
            'annee_universitaire_id' => 'required',
            'revenu_net_min' => 'nullable|integer',
            'revenu_net_max' => 'nullable|integer',
            'attestation_status' => 'nullable',
            'attestation_pret_status' => 'nullable',
            'retrait_status' => 'nullable',
            'demande_type_ids' => 'required|array',
            'budget_max' => 'nullable',
            'montant_unite' => 'nullable',
            'by_score' => 'nullable',
        ]);
        if ($validator1->fails()) {
            return response()->json([
                'message' => 'Validations fails',
                'errors' => $validator1->errors(),
            ], 422);
        }
        $data = $validator1->validated();

        $anneeUniversitaire = AnneeUniversitaire::find($data['annee_universitaire_id']);
        $demandes = Demande::select(
            [
                'id', 'code', 'user_id', 'etat',
                'annee_universitaire_id', 'demande_type_id', 'revenu_net',
                'etat_bourse', 'etat_bourse_insertion', 'etat_pret', 'etat_bourse_stage', 'etat_aide_sociale',
                'lot',
            ])
            ->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION'])
            ->where('annee_universitaire_id', $request->annee_universitaire_id);



        if ($request->demande_type_ids && count($request->demande_type_ids) > 0) {
            $demandes = $demandes->whereIn('demande_type_id', $data['demande_type_ids']);
        }

        $usersRetraitids = [];
        $usersRetraitidsNo = [];
        if ($request->attestation_status === "true") {
            $attestationTypesNonBoursier = AttestationType::where('non_boursier',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
        }
        if ($request->attestation_status === "false") {
            $attestationTypesNonBoursier = AttestationType::where('non_boursier',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)
                ->where('year', $anneeUniversitaire->title)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
        }
        if ($request->attestation_pret_status === "true") {
            $attestationTypesNonBoursier = AttestationType::where('non_pret',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
        }
        if ($request->attestation_pret_status === "false") {
            $attestationTypesNonBoursier = AttestationType::where('non_pret',  true)->pluck('id');
            $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)
                ->where('year', $anneeUniversitaire->title)->whereNot('status', 'refuse')->whereNot('status','annulee')->pluck('student_id');
            $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
        }
        if ($request->retrait_status === "true") {
            $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_cin');
            $usersIds = User::where('cin', 'IN', $usersRetrait)->pluck('id');
            $usersRetraitids = $usersIds;
            $demandes = $demandes->whereIn('user_id', $usersIds);
        }
        if ($request->retrait_status === "false") {
            $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_cin');
            $usersIds = User::where('cin', 'IN', $usersRetrait)->pluck('id');
            $usersRetraitidsNo = $usersIds;
            $demandes = $demandes->whereNotIn('user_id', $usersIds);
        }

        if ($request->type_preparation) {
            if ($request->type_preparation === 'bourse') {
                $demandes = $demandes->where('etat_bourse', '=', Demande::ETAT_BOURSE['ELIGIBLE']);
            }
            if ($request->type_preparation === 'insertion') {
                $demandes = $demandes->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']);
            }
            if ($request->type_preparation === 'pret') {
                $demandes = $demandes->where('etat_pret', '=', Demande::ETAT_PRET['ELIGIBLE']);
            }
            if ($request->type_preparation === 'aide_sociale') {
                $demandes = $demandes->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['ELIGIBLE']);
            }
            if ($request->type_preparation === 'stage') {
                $demandes = $demandes->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['ELIGIBLE']);
            }

        }

        if ($request->has('revenu_net_min') && $request->revenu_net_min != '') {
            $demandes = $demandes->where('revenu_net', '>=', $request->revenu_net_min);
        }
        if ($request->has('revenu_net_max') && $request->revenu_net_max != '') {
            $demandes = $demandes->where('revenu_net', '<=', $request->revenu_net_max);
        }

        if ($request->has('by_score') && $request->by_score === 'active'){
            $demandes = $demandes->orderBy('score_total', 'desc');
        }
        else {
            $demandes = $demandes->orderBy('id');
        }

        $limit = null;
        if ($request->has('budget_max') && $request->has('montant_unite') && $request->budget_max != '' && $request->montant_unite != '') {
            $limit = ($request->budget_max / $request->montant_unite);
        }

        if ($limit) {
            $demandes = $demandes->skip(0)->take($limit)->get();
        }
        else {
            $demandes = $demandes->get();
        }
        if (!$demandes->isEmpty() &&
            ($request->type_preparation === 'bourse' ||
                $request->type_preparation === 'insertion' ||
                $request->type_preparation === 'pret' ||
                $request->type_preparation === 'aide_sociale' ||
                $request->type_preparation === 'stage')
        ) {

            $demandesUnique = $demandes->unique('user_id', true);
            $demandesDupes = $demandes->diff($demandesUnique);
            return response()->json([
                'count' => $demandes->count(),
                'usersRetraitids' => $usersRetraitids,
                'usersRetraitidsNo' => $usersRetraitidsNo,
                'duplicates' => $demandesDupes
            ], 200);
        }

        return response()->json([
            'message' => 'La liste des demandes est vide',
            'usersRetraitids' => $usersRetraitids,
            'usersRetraitidsNo' => $usersRetraitidsNo,
            'criteria' => $request->all(),
            'errors' => ['liste' => 'La liste des demandes est vide'],
        ], 422);


    }

    public function generateExcel(Preparation $preparation,Request $request): JsonResponse
    {
        ini_set('memory_limit', '-1');

        dispatch(new ProcessPreparationExport(auth()->user(), $preparation,  $request->changeStatus));

//        try{
//            $row = ExportedFile::create([
//                'type' => $preparation?->type,
//                'vue' => 0,
//                'etat' => 'en_cours',
//            ]);
//
//            (new PreparationMapExport($preparation->id, $preparation?->type))
//                ->store('uploads/exportedFile/preparation_' . $preparation->ndec_code . '_' . $row->type . '_' . $row->id . '.xlsx')
//                ->chain([
//                    new NotifyUserOfCompletedPreparationExport(request()->user(), $row->type, $row->id, $preparation->ndec_code)
//                ]);
//
//        } catch (\Exception $e) {
//            Log::error($e->getFile().$e->getLine().$e->getMessage());
//        }
        return response()->json("success", 200);
//        $demandes = [];
//        if ( $preparation->type ) {
//            $demandes = Demande::with( 'professionFinal' );
//            if ($preparation->type === 'bourse') {
//                $demandes = $demandes->where('preparation_bourse_id', $preparation->id)->get();
//            }
//            elseif ($preparation->type === 'insertion') {
//                $demandes = $demandes->where('preparation_insertion_id', $preparation->id)->get();
//            }
//            elseif ($preparation->type === 'pret') {
//                $demandes = $demandes->where('preparation_pret_id', $preparation->id)->get();
//            }
//            elseif ($preparation->type === 'aide_sociale') {
//                $demandes = $demandes->where('preparation_aide_sociale_id', $preparation->id)->get();
//            }
//            elseif ($preparation->type === 'stage') {
//                $demandes = $demandes->where('preparation_stage_id', $preparation->id)->get();
//            }
//            if ($request->changeStatus === 'ok') {
//                $preparation->etat = Preparation::ETAT['EN_COURS'];
//                $preparation->save();
//            }
//        }
//
////        return Excel::download(new DemandesExport($demandes), 'demande.xlsx');
//
//        return response()->json($demandes, 200);
    }

    public function show(Preparation $preparation): JsonResponse
    {
        return response()->json($preparation, 200);
    }

    public function restore(Preparation $preparation): JsonResponse
    {

        if ($preparation) {
            dispatch(new ProcessRestorePreparation(auth()->user(), $preparation));

            return response()->json("success", 200);
        }
        return response()->json( [
            'message' => 'Preparation not found'
        ], 404);
    }

    public function restoreDemande(Preparation $preparation, Demande $demande): JsonResponse
    {

        if ($preparation->type) {
            if ($preparation->type === 'bourse') {
                $demande->preparation_bourse_id = null;
                $demande->etat_bourse = Demande::ETAT_BOURSE['ELIGIBLE'];
                $demande->etat = Demande::ETAT['PRISE_DE_DECISION'];
                $demande->natdec_bourse = null;
                $demande->save();
            }
            if ($preparation->type === 'insertion') {
                $demande->preparation_insertion_id = null;
                $demande->etat_bourse_insertion = Demande::ETAT_BOURSE_INSERTION['ELIGIBLE'];
                $demande->etat = Demande::ETAT['PRISE_DE_DECISION'];
                $demande->natdec_insertion = null;
                $demande->save();
            }
            if ($preparation->type === 'pret') {
                $demande->preparation_pret_id = null;
                $demande->etat_pret = Demande::ETAT_PRET['ELIGIBLE'];
                $demande->etat = Demande::ETAT['PRISE_DE_DECISION'];
                $demande->natdec_pret = null;
                $demande->save();
            }
            if ($preparation->type === 'aide_sociale') {
                $demande->preparation_aide_sociale_id = null;
                $demande->etat_aide_sociale = Demande::ETAT_AIDE_SOCIALE['ELIGIBLE'];
                $demande->etat = Demande::ETAT['PRISE_DE_DECISION'];
                $demande->natdec_aide_sociale = null;
                $demande->save();
            }
            if ($preparation->type === 'stage') {
                $demande->preparation_stage_id = null;
                $demande->etat_bourse_stage = Demande::ETAT_BOURSE_STAGE['ELIGIBLE'];
                $demande->etat = Demande::ETAT['PRISE_DE_DECISION'];
                $demande->natdec_stage = null;
                $demande->save();
            }
        }

        return response()->json($demande, 200);
    }

    public function synchronize(Preparation $preparation): JsonResponse {

        if ($preparation) {
            dispatch(new ProcessSyncPreparation(auth()->user(), $preparation));

            return response()->json("success", 200);
        }
        return response()->json( [
            'message' => 'Preparation not found'
        ], 404);
    }

    public function exportExcel(Request $request, $type): JsonResponse
    {
        $files = Preparation::where('type', $type)
            ->when(
                $request->has('ndec'),
                function ($query) use ($request) {
                    return $query->where('code', 'like', '%' . $request->ndec . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request->annee_universitaire_id);
                }
            )
            ->orderBy('id', 'desc')
            ->with('anneeUniversitaire')
            ->get();
        return response()->json($files, 200);
    }
}
