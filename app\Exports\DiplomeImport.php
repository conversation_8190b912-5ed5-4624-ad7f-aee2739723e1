<?php
namespace App\Exports;

use App\Models\Diplome;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class DiplomeImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'code_dip' => 'required',
                'lib_ar' => 'required',
                'lib_fr' => 'required',
                'nbr_annee_etude' => 'required',
                'cycle' => 'required',
                'troisieme_cycle' => 'required',
            ],
            [],
            [
                'code_dip' => '(code_dip)',
                'lib_ar' => '(lib_ar)',
                'lib_fr' => '(lib_fr)',
                'nbr_annee_etude' => '(nbr_annee_etude)',
                'cycle' => '(cycle)',
                'troisieme_cycle' => '(troisième cycle)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.lib_ar' => 'required|string',
            '*.lib_fr' => 'required|string',
            '*.code_dip' => 'required|string',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Diplome::where('code', $row['code_dip'])->first();
            if ($gov){
                $gov->update([
                    'name' => $row['lib_fr'],
                    'name_fr' => $row['lib_fr'],
                    'name_ar' => $row['lib_ar'],
                    'nbr_annee_etude' => $row['nbr_annee_etude'],
                    'cycle' => $row['cycle'],
                    'troisieme_cycle' => $row['troisieme_cycle'],
                ]);
            } else {
                Diplome::create([
                    'name'  => $row['lib_fr'],
                    'name_fr'  => $row['lib_fr'],
                    'name_ar'  => $row['lib_ar'],
                    'code' => $row['code_dip'],
                    'nbr_annee_etude' => $row['nbr_annee_etude'],
                    'cycle' => $row['cycle'],
                    'troisieme_cycle' => $row['troisieme_cycle'],
                ]);
            }
        }
    }
}
