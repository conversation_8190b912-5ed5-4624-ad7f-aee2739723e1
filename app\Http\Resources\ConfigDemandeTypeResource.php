<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class ConfigDemandeTypeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
//            'config_array' => json_decode($this->config, true),
            'config' => $this->config,
//            'logic_array' => json_decode($this->logic, true),
            'logic' => $this->logic,
            'demandes' => DemandeResource::collection($this->demandes),
//            'parent' => DemandeTypeResource::resolve($this->parent),
            'demandeType' =>$this->demandeType,
            'demande_type_id' =>$this->demande_type_id,
        ];
    }
}
