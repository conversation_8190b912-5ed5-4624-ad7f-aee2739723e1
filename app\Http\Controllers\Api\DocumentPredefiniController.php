<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDocumentPredefiniRequest;
use App\Http\Requests\UpdateDocumentPredefiniRequest;
use App\Http\Resources\DocumentPredefiniResource;
use App\Models\DocumentClassification;
use App\Models\DocumentPredefini;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class DocumentPredefiniController extends Controller
{

    protected $cache_seconds = 900;

    public function __construct()
    {
        // $this->middleware('permission:product-create', ['only' => ['create','store']]);
        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('DocumentPredefini', $this->cache_seconds, function () {
            return DocumentPredefiniResource::collection(DocumentPredefini::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDocumentPredefiniRequest $request
     * @return Response
     */
    public function store(StoreDocumentPredefiniRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('DocumentPredefini');
        Helpers::clearCacheIdp();

        $documentPredefini = DocumentPredefini::create($data);

        return response(new DocumentPredefiniResource($documentPredefini) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateDocumentPredefiniRequest $request
     * @param DocumentPredefini $documentPredefini
     * @return Response
     */
    public function edit(UpdateDocumentPredefiniRequest $request,DocumentPredefini $documentPredefini): Response
    {
        $data = $request->validated();

        $code = $documentPredefini->code;

        $doc_class_to_change = DocumentClassification::where('code',$code)->get();

        foreach($doc_class_to_change as $doc_class){
            $doc_class->update([
                'code'=>$data['code'],
                'title'=>$data['title'],
                'title_fr'=>$data['title_fr'],
                'title_ar'=>$data['title_ar'],
            ]);
        }

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Cache::forget('DocumentPredefini');
        Helpers::clearCacheIdp();

        $documentPredefini->update($data);

        return response(new DocumentPredefiniResource($documentPredefini) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param DocumentPredefini $documents_predefini
     * @return Response
     */
    public function destroy(DocumentPredefini $documents_predefini)
    {
        Cache::forget('DocumentPredefini');
        Helpers::clearCacheIdp();

        $documents_predefini->delete();
        return response("ok", 204);
    }
}
