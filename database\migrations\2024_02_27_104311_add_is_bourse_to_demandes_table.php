<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->boolean('is_bourse')->default(false);
            $table->boolean('is_bourse_insertion')->default(false);
            $table->boolean('is_pret')->default(false);
            $table->boolean('is_bourse_stage')->default(false);
            $table->boolean('is_aide_sociale')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('is_bourse');
            $table->dropColumn('is_bourse_insertion');
            $table->dropColumn('is_pret');
            $table->dropColumn('is_bourse_stage');
            $table->dropColumn('is_aide_sociale');
        });
    }
};
