<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class StoreMontantPretRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'code_diplome' => 'required|string',
            'annee_etude' => 'required|integer',
            'resultat' => 'required|boolean',
            'montant' => 'required|integer',
            ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'resultat' => Helpers::toBoolean($this->resultat),
        ]);
    }




}
