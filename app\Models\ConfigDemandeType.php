<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class ConfigDemandeType extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'config',
        'logic',
        'demande_type_id',
    ];

//    protected $casts = [
//        'config' => 'array',
//        'logic' => 'array',
//    ];
    public function demandes(): HasMany
    {
        return $this->hasMany(Demande::class);
    }

    public function demandeType() : BelongsTo
    {
        return $this->belongsTo(DemandeType::class, 'demande_type_id');
    }

}
