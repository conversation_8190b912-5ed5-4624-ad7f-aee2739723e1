<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class StudentRefusedMail extends Mailable
{
    use Queueable, SerializesModels;
    protected $user;
    protected $raison_refus;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $raison_refus)
    {
        $this->user = $user;
        $this->raison_refus = $raison_refus;
        
    }
    public function build()
    {
        return $this->view('emails.studentRefusedMail',['user'=>$this->user, 'raison_refus'=>$this->raison_refus]);
    }
}
