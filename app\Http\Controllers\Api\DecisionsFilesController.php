<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDecisionFileRequest;
use App\Jobs\GenerateDecisionPDF;
use App\Models\AnneeUniversitaire;
use App\Models\DecisionsFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use \Mpdf\Mpdf as PDF;

class DecisionsFilesController extends Controller
{

    public function index(Request $request, $type)
    {
        $files =  DecisionsFile::where('type',$type)
        ->when(
            $request->has('annee_gestion'),
            function ($query) use ($request) {
                return $query->where('annee_gestion',  'like', '%' . $request->annee_gestion . '%');
            }
        )
            ->when(
                $request->has('ndec'),
                function ($query) use ($request) {
                    return $query->where('ndec',  'like', '%' . $request->ndec . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id',  $request->annee_universitaire_id);
                }
            )
            ->orderBy('id', 'desc')
            ->with('annee_universitaire')
            ->paginate(
                $request->input('perPage') ?? config('constants.pagination'),
            );
        return response()->json($files, 200);
    }

    public function generatePDF(StoreDecisionFileRequest $request)
    {

        $row=DecisionsFile::create([
            'ndec'  => $request->ndec,
            'annee_gestion'  => $request->annee_gestion,
            'type'  => $request->type,
            'etat' => 0,
            'annee_universitaire_id' => $request->annee_universitaire_id
        ]);

        dispatch(new GenerateDecisionPDF($request->all(), $row->id));
        return response()->json("success", 200);
    }

    public function downloadExistingPDF(Request $request)
    {
        return response()->download(storage_path('app/' . $request->fileName));
    }


}
