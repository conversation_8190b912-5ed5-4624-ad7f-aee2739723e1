<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Decision extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $table = "historiques";
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }


    protected $fillable = [
        'cin', 'catb', 'lot', 'nom', 'datnais', 'gouv', 'sexe', 'profp', 'anet', 'discip',
        'fac', 'univ', 'inf', 'sup', 'enf', 'revp', 'revm', 'avis', 'res', 'moy', 'natdec',
        'situa', 'mbs', 'nmb', 'mf', 'ndec', 'dat', 'montanttotal','montant', 'pourcentage', 'office',
        'type', 'annee_id'
    ];

    public function etablissement() : BelongsTo
    {
        return $this->belongsTo(Etablissement::class,"fac","code");
    }

    public function annee_universitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,"annee_id","id");
    }

    public function user() : BelongsTo
    {
        return $this->setConnection(config('database.secondConnection'))->belongsTo(User::class, 'cin');
    }

    protected $appends = ['cin_prefix'];

    public function country_iso(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'cin_prefix', 'code');
    }

    public function getCinPrefixAttribute(): string
    {
        return substr($this->cin, 0, 2);
    }
}
