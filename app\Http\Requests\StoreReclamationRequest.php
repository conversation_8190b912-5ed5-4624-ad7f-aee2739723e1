<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class StoreReclamationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'reclamation_type_id' => 'required|integer',
            'detail' => 'required|string',
            'response' => 'sometimes|required|string',
            'etat' => 'nullable|string',
            'student_id' => 'sometimes|required',
        ];
    }
}
