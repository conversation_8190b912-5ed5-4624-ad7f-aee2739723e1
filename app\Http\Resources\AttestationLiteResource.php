<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class AttestationLiteResource extends JsonResource
{
    public static $wrap = null;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'attestation_types_id'=> $this->attestation_types_id,
            'attestation_type'=> new AttestationTypeLiteResource($this->attestationType),
            'status'=> $this->status,
            'detail'=> $this->detail,
            'year'=> $this->year,
            'student_id'=> $this->student_id,
            'student'=> new StudentLiteResource($this->student),
            'annee_universitaire_id'=> $this->annee_universitaire_id,
            'etablissement_id'=> $this->etablissement_id,
            'date_enregistrement_bureau_ordre'=> $this->date_enregistrement_bureau_ordre,
            'reference_bureau_ordre'=> $this->reference_bureau_ordre,
            'langue'=> $this->langue,
            'office_id'=> $this->office_id,
            'response_date'=> $this->response_date,
            'montant'=> $this->montant,
            'date_mandat'=> $this->date_mandat,
            'raison_non_retrait'=> $this->raison_non_retrait,
            'num_emission' => $this->num_emission,
            'etudiant_annee_universitaire' => $this->etudiantAnneeUniversitaire,
            'attestation_documents' => $this->attestationDocuments,
            'etablissement' => $this->etablissement,
            'created_at' => $this->created_at,
        ];
    }
}
