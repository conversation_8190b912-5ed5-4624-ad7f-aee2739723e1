<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),
    'second_filesystem' => env('SECOND_FILESYSTEM', 'sftp'),
    'public_filesystem' => env('SECOND_FILESYSTEM', 'sftp_public'),
    'local' => env('LOCAL_FILESYSTEM', 'local'),
    'public' => env('PUBLIC_FILESYSTEM', 'public'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],
        'public' => [
            'driver' => 'local',
            'root' => storage_path(env('PUBLIC_STORAGE_PATH', 'app/public')),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
        ],
        'second_disk' => [
            'driver' => 'local',
            'root'   => env('SECOND_DISK_ROOT', 'C:\laragon\www\bpas_back\public'),
            'url' => env('SECOND_DISK_URL', 'https://bpas_back.test'),
            'visibility' => 'public',
        ],
        'ftp' => [
            'driver' => 'ftp',
            'host' => env('FTP_HOST', '127.0.0.1'),
            'username' => env('FTP_USERNAME', 'root'),
            'password' => env('FTP_PASSWORD', 'root'),
            'url' => env('FTP_URL', 'https://bpas_back.test'),
            'port' => (int)env('FTP_PORT', 21),

            // Optional FTP Settings...
             'root' => env('FTP_ROOT'),
            // 'passive' => true,
            // 'ssl' => true,
            // 'timeout' => 30,
        ],
        'sftp' => [
            'driver' => 'sftp',
            'host' => env('SFTP_HOST'),
            'port' => (int)env('SFTP_PORT', 22),
            // Settings for basic authentication...
            'username' => env('SFTP_USERNAME'),
            'password' => env('SFTP_PASSWORD'),
            'root' => env('SFTP_ROOT', 'storage'),
            'visibility' => 'private', // `private` = 0600, `public` = 0700
            'directory_visibility' => 'private', // `private` = 0700, `public` = 0755
            'url' => env('SFTP_URL', 'https://bpas_back.test'),

            // Settings for SSH key based authentication with encryption password...
            // 'privateKey' => env('SFTP_PRIVATE_KEY'),
            // 'passphrase' => env('SFTP_PASSPHRASE'),

            // Settings for file / directory permissions...
            // Optional SFTP Settings...
            // 'hostFingerprint' => env('SFTP_HOST_FINGERPRINT'),
            // 'maxTries' => 4,
            // 'passphrase' => env('SFTP_PASSPHRASE'),
            // 'timeout' => 30,
            // 'useAgent' => true,
        ],
        'sftp_public' => [
            'driver' => 'sftp',
            'host' => env('SFTP_HOST'),
            'port' => (int)env('SFTP_PORT', 22),
            // Settings for basic authentication...
            'username' => env('SFTP_USERNAME'),
            'password' => env('SFTP_PASSWORD'),
            'root' => env('SFTP_PUBLIC', 'public'),
            'visibility' => 'public', // `private` = 0600, `public` = 0700
            'directory_visibility' => 'public', // `private` = 0700, `public` = 0755
            'url' => env('SFTP_URL', 'https://bpas_back.test'),

            // Settings for SSH key based authentication with encryption password...
            // 'privateKey' => env('SFTP_PRIVATE_KEY'),
            // 'passphrase' => env('SFTP_PASSPHRASE'),

            // Settings for file / directory permissions...
            // Optional SFTP Settings...
            // 'hostFingerprint' => env('SFTP_HOST_FINGERPRINT'),
            // 'maxTries' => 4,
            // 'passphrase' => env('SFTP_PASSPHRASE'),
            // 'timeout' => 30,
            // 'useAgent' => true,
        ],
        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
