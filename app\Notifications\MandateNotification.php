<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Socket;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MandateNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    // public function toMail($notifiable)
    // {
    //     return (new MailMessage)
    //                 ->line('The introduction to the notification.')
    //                 ->action('Notification Action', url('/'))
    //                 ->line('Thank you for using our application!');
    // }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
            //Log::debug("Sent notification via websocket");

        } catch ( \Exception $e) {
            //Log::debug("cannot send notification via websocket");
        }

        return [
            "title" => "Your money order has been sent",
            "subtitle" => "Your money order has been sent",
            "title_fr" => "Votre mandat est envoyé",
            "subtitle_fr" => "Votre mandat est envoyé",
            "title_ar" => "لقد تم إرسال الحوالة المالية الخاصة بك",
            "subtitle_ar" => " لقد تم إرسال الحوالة المالية الخاصة بك " ,
            "avatarIcon" => "confetti",
            "avatarAlt" => "Attestation",
            "avatarText" => "Attestation",
            "avatarColor" => "success",
            "type" => "attestation",
            // "target_id" => $this->attestation->id,
            "target" => "attestation",
            "model" => "attestation",
            //"url" => "",
            "url" => "mes-mandats/"//. $this->attestation->id,

        ];
    }
}
