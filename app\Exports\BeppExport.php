<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;


class BeppExport implements WithEvents, FromView
{
    protected $office;
    protected $data;
    protected $year;
    protected $labels;
    protected $totals;
    protected $dataMesrs;
    protected $dataAideSociale;
    protected $dataInsertion;
    protected $res;
    protected $date_export;

    public function __construct($office,$data,$year,$labels,$totals, $dataMesrs,$dataAideSociale, $dataInsertion,$res)
    {
        $this->office = $office;
        $this->data = $data;
        $this->year = $year;
        $this->labels = $labels;
        $this->totals = $totals;
        $this->dataMesrs = $dataMesrs;
        $this->dataAideSociale = $dataAideSociale;
        $this->dataInsertion = $dataInsertion;
        $this->res = $res;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }


    public function view(): View
    {

        return view('statistiques.bepp', [
            'office' => $this->office,
            'data' => $this->data,
            'year' => $this->year,
            'labels' => $this->labels,
            'totals' => $this->totals,
            'dataMesrs' => $this->dataMesrs,
            'dataAideSociale' => $this->dataAideSociale,
            'dataInsertion' => $this->dataInsertion,
            'res' => $this->res,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title

        ]);
    }
}

