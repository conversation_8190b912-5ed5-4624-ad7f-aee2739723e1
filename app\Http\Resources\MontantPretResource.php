<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class MontantPretResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id'=> $this->id,
            'code_diplome'=> $this->code_diplome,
            'diplome'=> $this->diplome,
            'annee_etude'=> $this->annee_etude,
            'resultat'=> $this->resultat,
            'montant'=> $this->montant,
            'created_at' => $this->created_at->format('Y-m-d'),

        ];
    }
}
