<?php

namespace App\Traits;

use App\Models\Audit;
use OwenIt\Auditing\Contracts\Auditable;
use Illuminate\Support\Facades\Log;

trait PivotSyncAndAudit
{
    public function syncPivotAndLog(array $data, array $pivotRelations, $identifier= 'id')
    {
        // works only for default many to many relations (and not for diplomes() )
        /*foreach ($pivotRelations as $relation => $details) {
            if (isset($data[$relation])) {
                $relatedIds = collect($data[$relation])->pluck('id')->toArray();
                $oldIds = $this->$relation()->pluck($this->$relation()->getRelated()->getTable() . '.id')->toArray();

                $this->$relation()->sync($relatedIds);

                $newIds = $this->$relation()->pluck($this->$relation()->getRelated()->getTable() . '.id')->toArray();

                // Log changes
                $this->logPivotChanges($this->$relation()->getRelated(), $oldIds, $newIds, $relation, $details['display']);
            }
        }*/

        foreach ($pivotRelations as $relation => $details) {
            if (isset($data[$relation])) {

                $relatedIds = collect($data[$relation])->pluck($identifier)->toArray();
                $relationInstance = $this->$relation();

                // Determine the related and foreign pivot key names
                $relatedPivotKey = $relationInstance->getRelatedPivotKeyName();
                $foreignPivotKey = $relationInstance->getForeignPivotKeyName();

                // Retrieve old IDs
                $oldIds = $relationInstance->pluck($relatedPivotKey)->toArray();

                // Sync new IDs
                $relationInstance->sync($relatedIds);

                // Retrieve new IDs
                $newIds = $relationInstance->pluck($relatedPivotKey)->toArray();

                // Log changes
                $this->logPivotChanges($relationInstance->getRelated(), $oldIds, $newIds, $relation, $details['display'], $identifier);
            }
        }

        $this->update($data);
    }

    protected function logPivotChanges($relatedModel, array $oldIds, array $newIds, string $relation, string $displayColumn, $identifier= 'id')
    {
        // Find old and new models based on IDs

        $oldModels = $relatedModel::whereIn($identifier,$oldIds)->pluck($displayColumn)->implode(' - ');
        $newModels = $relatedModel::whereIn($identifier,$newIds)->pluck($displayColumn)->implode(' - ');

        // Prepare audit data
        $auditData = [
            'old_values' => [$relation => $oldModels],
            'new_values' => [$relation => $newModels],
            'event' => 'updated',
            'auditable_id' => $this->id,
            'auditable_type' => get_class($this),
            'tags' => 'pivot_sync', // Changed from array to string
            'user_id' => auth()->id(),
            'user_type' => auth()->user() ? get_class(auth()->user()) : null,
            'ip_address' => request()->ip()
        ];

        // Log audit data for debugging
//        Log::info('Audit data', ['auditData' => $auditData]);

        // Create audit entry
        $this->createAudit($auditData);
    }



    protected function createAudit($auditData)
    {
        $this->audits()->create($auditData);
    }

    // Aliased audits method to avoid conflict
    public function pivotAudits()
    {
        return $this->morphMany(Audit::class, 'auditable');
    }
}
