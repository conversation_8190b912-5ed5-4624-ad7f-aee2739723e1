<?php

namespace App\Console\Commands;

use App\Models\Demande;
use App\Models\DemandeAnneeEtude;
use Illuminate\Console\Command;

class SetAnneeEtudeDemande extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demande:set-annee-etude';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set etudiant_annee_universitaire_id for all demandes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update demandes...');

        $count = 0;

        try {
            Demande::chunk(500, function ($demandes) use (&$count) {
                foreach ($demandes as $demande) {
                    try {
                        if ($demande->etudiant_annee_universitaire_id !== null) {
                            continue;
                        }

                        $demande->etudiant_annee_universitaire_id = $demande->demande_last_annee_etude->id;
                        $demande->save();
                        $count++;
                    } catch (\Exception $e) {
                        $message = "Error processing demande ID {$demande->id}: {$e->getMessage()}";
                        $this->error($message); // Console output for command
                        \Log::error($message); // Laravel log
                        continue; // Continue with next record
                    }
                }
            });
        } catch (\Exception $e) {
            $message = "Error in chunk processing: {$e->getMessage()}";
            $this->error($message); // Console output for command
            \Log::error($message); // Laravel log
        }

        $this->info("Completed! Updated {$count} demandes.");
    }
}
