<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Etablissement extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }


    protected $hidden = ['created_at', 'updated_at'];

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'password',
        'code',
        'code_ministere',
        'active',
        'code_univ',
        'code_office',
        'code_dir_reg',
        'code_gouv',
    ];


    protected $casts = [
        'active' => 'boolean',
    ];
    public function gouvernorat() : BelongsTo
    {
        return $this->belongsTo(Gouvernorat::class,"code_gouv","code");
    }

    public function diplomes(): BelongsToMany
    {
        return $this->belongsToMany(Diplome::class, 'diplome_etablissements', 'code_etab', 'code_diplome','code', 'code');
    }

    public function office() : BelongsTo
    {
        return $this->belongsTo(Office::class,"code_office","code");
    }

    public function universite() : BelongsTo
    {
        return $this->belongsTo(Universite::class,"code_univ","code");
    }

    public function filieres() : HasMany
    {
        return $this->hasMany(Filiere::class,'code_etab','code');
    }
    public function diplomesEtablissements() : HasMany
    {
        return $this->hasMany(DiplomeEtablissement::class,'code_etab','code');
    }
}
