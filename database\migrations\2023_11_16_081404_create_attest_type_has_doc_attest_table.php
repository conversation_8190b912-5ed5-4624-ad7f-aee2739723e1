<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attest_type_has_doc_attest', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('attestation_types_id')->nullable();
            $table->unsignedBigInteger('documents_attestations_id')->nullable();

            $table->foreign('attestation_types_id')
                ->references('id')
                ->on('attestation_types');

            $table->foreign('documents_attestations_id')
                ->references('id')
                ->on('documents_attestations');



            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attest_type_has_doc_attest');
    }
};
