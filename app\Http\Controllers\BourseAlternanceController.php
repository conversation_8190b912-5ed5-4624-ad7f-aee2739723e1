<?php

namespace App\Http\Controllers;

use App\Http\Resources\BourseAlternanceResource;
use App\Models\BourseAlternance;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BourseAlternanceController extends Controller
{
    function __construct()
    {
        //        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index() //: AnonymousResourceCollection
    {
        return BourseAlternanceResource::collection(BourseAlternance::with('student')->with('annee_universitaire')->with('etablissement')->get());
        //return BourseAlternance::with('student')->get();        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            //code...
            $request->validate([
                'annee_universitaire_id' => 'required|string',
                'student_id' => 'required|string',
                'nb_mois' => 'required|string',
                'nb_decision' => 'required|string',
                'nature_decision' => 'required|string',
                'etablissement_id' => 'required|string'
            ]);
            
            $bourseAlternance = BourseAlternance::create([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_id' => $request->student_id,
                'nb_mois' => $request->nb_mois,
                'nb_decision' => $request->nb_decision,
                'nature_decision' => $request->nature_decision,
                'etablissement_id' => $request->etablissement_id,
            ]);
        } catch (\Throwable $th) {
            //throw $th;
            $request->validate([
                'annee_universitaire_id' => 'required|string',
                'student_name' => 'required|string',
                'student_cin' => 'required|string',
                'nb_mois' => 'required|string',
                'nb_decision' => 'required|string',
                'nature_decision' => 'required|string',
                'etablissement_id' => 'required|string'
            ]);
            
            $bourseAlternance = BourseAlternance::create([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_name' => $request->student_name,
                'student_cin' => $request->student_cin,
                'nb_mois' => $request->nb_mois,
                'nb_decision' => $request->nb_decision,
                'nature_decision' => $request->nature_decision,
                'etablissement_id' => $request->etablissement_id,
            ]);
        }
        

        return response(new BourseAlternanceResource($bourseAlternance), 201);
    }

    /**
     * Display the specified resource.
     *
     * @param BourseAlternance $bourseAlternance
     * @return BourseAlternanceResource
     */
    public function show(BourseAlternance $bourseAlternance): BourseAlternanceResource
    {
        return new BourseAlternanceResource($bourseAlternance);
    }

    public function edit(Request $request): Response
    {
        try {
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'student_id' => 'required|string',
                'nb_mois' => 'required|string',
                'nb_decision' => 'required|string',
                'nature_decision' => 'required|string',
                'etablissement_id' => 'required|string',
            ]);
    
    
            $bourseAlternance = BourseAlternance::findOrFail($request->id)->update([
                'id' => $request->id,
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_id' => $request->student_id,
                'nb_mois' => $request->nb_mois,
                'nb_decision' => $request->nb_decision,
                'nature_decision' => $request->nature_decision,
                'etablissement_id' => $request->etablissement_id,
            ]); 
        } catch (\Throwable $th) {
            //throw $th;
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'student_name' => 'required|string',
                'student_cin' => 'required|string',
                'nb_mois' => 'required|string',
                'nb_decision' => 'required|string',
                'nature_decision' => 'required|string',
                'etablissement_id' => 'required|string',
            ]);
    
    
            $bourseAlternance = BourseAlternance::findOrFail($request->id)->update([
                'id' => $request->id,
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'student_name' => $request->student_name,
                'student_cin' => $request->student_cin,
                'nb_mois' => $request->nb_mois,
                'nb_decision' => $request->nb_decision,
                'nature_decision' => $request->nature_decision,
                'etablissement_id' => $request->etablissement_id,
            ]); 
        }
               

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param BourseAlternance $bourseAlternance
     * @return Response
     */
    public function destroy(BourseAlternance $bourseAlternance): Response
    {
        $bourseAlternance->delete();

        return response("", 204);
    }


}
