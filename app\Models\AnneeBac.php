<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class AnneeBac extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    protected $fillable = [
        'active',
        'title',
        'order',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function studentFromMess() : HasMany
    {
        return $this->hasMany(StudentFromMes::class,'annee_bac','id');
    }

    public function anneeUniversitaire() : HasOne
    {
        return $this->hasOne(AnneeUniversitaire::class,'annee_bac','id');
    }

}
