<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Http\Controllers\Api\DemandeTypeController;
use App\Models\AnneeUniversitaire;
use App\Models\Classification;
use App\Models\Country;
use App\Models\Delegation;
use App\Models\Demande;
use App\Models\DemandeType;
use App\Models\Etape;
use App\Models\Gouvernorat;
use App\Models\Admin;
use App\Models\AttestationType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     * @throws \Exception
     */
    public function run()
    {

//        Admin::truncate();
//        Country::truncate();
//        Delegation::truncate();
//        Gouvernorat::truncate();

        //country
        $ar = [
            'IS' => 'آيسلندا',
            'ET' => 'إثيوبيا',
            'AZ' => 'أذربيجان',
            'AM' => 'أرمينيا',
            'AW' => 'أروبا',
            'ER' => 'إريتريا',
            'ES' => 'إسبانيا',
            'AU' => 'أستراليا',
            'EE' => 'إستونيا',
            'IL' => 'إسرائيل',
            'SZ' => 'إسواتيني',
            'AF' => 'أفغانستان',
            'PS' => 'الأراضي الفلسطينية',
            'AR' => 'الأرجنتين',
            'JO' => 'الأردن',
            'TF' => 'الأقاليم الجنوبية الفرنسية',
            'IO' => 'الإقليم البريطاني في المحيط الهندي',
            'EC' => 'الإكوادور',
            'AE' => 'الإمارات العربية المتحدة',
            'AL' => 'ألبانيا',
            'BH' => 'البحرين',
            'BR' => 'البرازيل',
            'PT' => 'البرتغال',
            'BA' => 'البوسنة والهرسك',
            'CZ' => 'التشيك',
            'ME' => 'الجبل الأسود',
            'DZ' => 'الجزائر',
            'DK' => 'الدانمرك',
            'CV' => 'الرأس الأخضر',
            'SV' => 'السلفادور',
            'SN' => 'السنغال',
            'SD' => 'السودان',
            'SE' => 'السويد',
            'EH' => 'الصحراء الغربية',
            'SO' => 'الصومال',
            'CN' => 'الصين',
            'IQ' => 'العراق',
            'GA' => 'الغابون',
            'VA' => 'الفاتيكان',
            'PH' => 'الفلبين',
            'CM' => 'الكاميرون',
            'CG' => 'الكونغو - برازافيل',
            'CD' => 'الكونغو - كينشاسا',
            'KW' => 'الكويت',
            'DE' => 'ألمانيا',
            'MA' => 'المغرب',
            'MX' => 'المكسيك',
            'SA' => 'المملكة العربية السعودية',
            'GB' => 'المملكة المتحدة',
            'NO' => 'النرويج',
            'AT' => 'النمسا',
            'NE' => 'النيجر',
            'IN' => 'الهند',
            'US' => 'الولايات المتحدة',
            'JP' => 'اليابان',
            'YE' => 'اليمن',
            'GR' => 'اليونان',
            'AQ' => 'أنتاركتيكا',
            'AG' => 'أنتيغوا وبربودا',
            'AD' => 'أندورا',
            'ID' => 'إندونيسيا',
            'AO' => 'أنغولا',
            'AI' => 'أنغويلا',
            'UY' => 'أورغواي',
            'UZ' => 'أوزبكستان',
            'UG' => 'أوغندا',
            'UA' => 'أوكرانيا',
            'IR' => 'إيران',
            'IE' => 'أيرلندا',
            'IT' => 'إيطاليا',
            'PG' => 'بابوا غينيا الجديدة',
            'PY' => 'باراغواي',
            'PK' => 'باكستان',
            'PW' => 'بالاو',
            'BB' => 'بربادوس',
            'BM' => 'برمودا',
            'BN' => 'بروناي',
            'BE' => 'بلجيكا',
            'BG' => 'بلغاريا',
            'BZ' => 'بليز',
            'BD' => 'بنغلاديش',
            'PA' => 'بنما',
            'BJ' => 'بنين',
            'BT' => 'بوتان',
            'BW' => 'بوتسوانا',
            'PR' => 'بورتوريكو',
            'BF' => 'بوركينا فاسو',
            'BI' => 'بوروندي',
            'PL' => 'بولندا',
            'BO' => 'بوليفيا',
            'PF' => 'بولينيزيا الفرنسية',
            'PE' => 'بيرو',
            'BY' => 'بيلاروس',
            'TH' => 'تايلاند',
            'TW' => 'تايوان',
            'TM' => 'تركمانستان',
            'TR' => 'تركيا',
            'TT' => 'ترينيداد وتوباغو',
            'TD' => 'تشاد',
            'CL' => 'تشيلي',
            'TZ' => 'تنزانيا',
            'TG' => 'توغو',
            'TV' => 'توفالو',
            'TK' => 'توكيلو',
            'TN' => 'تونس',
            'TO' => 'تونغا',
            'TL' => 'تيمور - ليشتي',
            'JM' => 'جامايكا',
            'GI' => 'جبل طارق',
            'AX' => 'جزر آلاند',
            'BS' => 'جزر البهاما',
            'KM' => 'جزر القمر',
            'MQ' => 'جزر المارتينيك',
            'MV' => 'جزر المالديف',
            'UM' => 'جزر الولايات المتحدة النائية',
            'PN' => 'جزر بيتكيرن',
            'TC' => 'جزر توركس وكايكوس',
            'SB' => 'جزر سليمان',
            'FO' => 'جزر فارو',
            'FK' => 'جزر فوكلاند',
            'VG' => 'جزر فيرجن البريطانية',
            'VI' => 'جزر فيرجن التابعة للولايات المتحدة',
            'KY' => 'جزر كايمان',
            'CK' => 'جزر كوك',
            'CC' => 'جزر كوكوس (كيلينغ)',
            'MH' => 'جزر مارشال',
            'MP' => 'جزر ماريانا الشمالية',
            'WF' => 'جزر والس وفوتونا',
            'BV' => 'جزيرة بوفيه',
            'CX' => 'جزيرة كريسماس',
            'IM' => 'جزيرة مان',
            'NF' => 'جزيرة نورفولك',
            'HM' => 'جزيرة هيرد وجزر ماكدونالد',
            'CF' => 'جمهورية أفريقيا الوسطى',
            'DO' => 'جمهورية الدومينيكان',
            'ZA' => 'جنوب أفريقيا',
            'SS' => 'جنوب السودان',
            'GE' => 'جورجيا',
            'GS' => 'جورجيا الجنوبية وجزر ساندويتش الجنوبية',
            'DJ' => 'جيبوتي',
            'JE' => 'جيرسي',
            'DM' => 'دومينيكا',
            'RW' => 'رواندا',
            'RU' => 'روسيا',
            'RO' => 'رومانيا',
            'RE' => 'روينيون',
            'ZM' => 'زامبيا',
            'ZW' => 'زيمبابوي',
            'CI' => 'ساحل العاج',
            'WS' => 'ساموا',
            'AS' => 'ساموا الأمريكية',
            'BL' => 'سان بارتليمي',
            'PM' => 'سان بيير ومكويلون',
            'MF' => 'سان مارتن',
            'SM' => 'سان مارينو',
            'VC' => 'سانت فنسنت وجزر غرينادين',
            'KN' => 'سانت كيتس ونيفيس',
            'LC' => 'سانت لوسيا',
            'SX' => 'سانت مارتن',
            'SH' => 'سانت هيلينا',
            'ST' => 'ساو تومي وبرينسيبي',
            'LK' => 'سريلانكا',
            'SJ' => 'سفالبارد وجان ماين',
            'SK' => 'سلوفاكيا',
            'SI' => 'سلوفينيا',
            'SG' => 'سنغافورة',
            'SY' => 'سوريا',
            'SR' => 'سورينام',
            'CH' => 'سويسرا',
            'SL' => 'سيراليون',
            'SC' => 'سيشل',
            'RS' => 'صربيا',
            'TJ' => 'طاجيكستان',
            'OM' => 'عُمان',
            'GM' => 'غامبيا',
            'GH' => 'غانا',
            'GD' => 'غرينادا',
            'GL' => 'غرينلاند',
            'GT' => 'غواتيمالا',
            'GP' => 'غوادلوب',
            'GU' => 'غوام',
            'GF' => 'غويانا الفرنسية',
            'GY' => 'غيانا',
            'GG' => 'غيرنزي',
            'GN' => 'غينيا',
            'GQ' => 'غينيا الاستوائية',
            'GW' => 'غينيا بيساو',
            'VU' => 'فانواتو',
            'FR' => 'فرنسا',
            'VE' => 'فنزويلا',
            'FI' => 'فنلندا',
            'VN' => 'فيتنام',
            'FJ' => 'فيجي',
            'CY' => 'قبرص',
            'QA' => 'قطر',
            'KG' => 'قيرغيزستان',
            'KZ' => 'كازاخستان',
            'NC' => 'كاليدونيا الجديدة',
            'HR' => 'كرواتيا',
            'KH' => 'كمبوديا',
            'CA' => 'كندا',
            'CU' => 'كوبا',
            'CW' => 'كوراساو',
            'KR' => 'كوريا الجنوبية',
            'KP' => 'كوريا الشمالية',
            'CR' => 'كوستاريكا',
            'CO' => 'كولومبيا',
            'KI' => 'كيريباتي',
            'KE' => 'كينيا',
            'LV' => 'لاتفيا',
            'LA' => 'لاوس',
            'LB' => 'لبنان',
            'LU' => 'لوكسمبورغ',
            'LY' => 'ليبيا',
            'LR' => 'ليبيريا',
            'LT' => 'ليتوانيا',
            'LI' => 'ليختنشتاين',
            'LS' => 'ليسوتو',
            'MT' => 'مالطا',
            'ML' => 'مالي',
            'MY' => 'ماليزيا',
            'YT' => 'مايوت',
            'MG' => 'مدغشقر',
            'EG' => 'مصر',
            'MK' => 'مقدونيا الشمالية',
            'MW' => 'ملاوي',
            'MO' => 'منطقة ماكاو الإدارية الخاصة',
            'MN' => 'منغوليا',
            'MR' => 'موريتانيا',
            'MU' => 'موريشيوس',
            'MZ' => 'موزمبيق',
            'MD' => 'مولدوفا',
            'MC' => 'موناكو',
            'MS' => 'مونتسرات',
            'MM' => 'ميانمار (بورما)',
            'FM' => 'ميكرونيزيا',
            'NA' => 'ناميبيا',
            'NR' => 'ناورو',
            'NP' => 'نيبال',
            'NG' => 'نيجيريا',
            'NI' => 'نيكاراغوا',
            'NZ' => 'نيوزيلندا',
            'NU' => 'نيوي',
            'HT' => 'هايتي',
            'HN' => 'هندوراس',
            'HU' => 'هنغاريا',
            'NL' => 'هولندا',
            'BQ' => 'هولندا الكاريبية',
            'HK' => 'هونغ كونغ الصينية (منطقة إدارية خاصة)',
        ];
        $fr = [
            'AF' => 'Afghanistan',
            'ZA' => 'Afrique du Sud',
            'AL' => 'Albanie',
            'DZ' => 'Algérie',
            'DE' => 'Allemagne',
            'AD' => 'Andorre',
            'AO' => 'Angola',
            'AI' => 'Anguilla',
            'AQ' => 'Antarctique',
            'AG' => 'Antigua-et-Barbuda',
            'SA' => 'Arabie saoudite',
            'AR' => 'Argentine',
            'AM' => 'Arménie',
            'AW' => 'Aruba',
            'AU' => 'Australie',
            'AT' => 'Autriche',
            'AZ' => 'Azerbaïdjan',
            'BS' => 'Bahamas',
            'BH' => 'Bahreïn',
            'BD' => 'Bangladesh',
            'BB' => 'Barbade',
            'BE' => 'Belgique',
            'BZ' => 'Belize',
            'BJ' => 'Bénin',
            'BM' => 'Bermudes',
            'BT' => 'Bhoutan',
            'BY' => 'Biélorussie',
            'BO' => 'Bolivie',
            'BA' => 'Bosnie-Herzégovine',
            'BW' => 'Botswana',
            'BR' => 'Brésil',
            'BN' => 'Brunei',
            'BG' => 'Bulgarie',
            'BF' => 'Burkina Faso',
            'BI' => 'Burundi',
            'KH' => 'Cambodge',
            'CM' => 'Cameroun',
            'CA' => 'Canada',
            'CV' => 'Cap-Vert',
            'CL' => 'Chili',
            'CN' => 'Chine',
            'CY' => 'Chypre',
            'CO' => 'Colombie',
            'KM' => 'Comores',
            'CG' => 'Congo-Brazzaville',
            'CD' => 'Congo-Kinshasa',
            'KP' => 'Corée du Nord',
            'KR' => 'Corée du Sud',
            'CR' => 'Costa Rica',
            'CI' => 'Côte d’Ivoire',
            'HR' => 'Croatie',
            'CU' => 'Cuba',
            'CW' => 'Curaçao',
            'DK' => 'Danemark',
            'DJ' => 'Djibouti',
            'DM' => 'Dominique',
            'EG' => 'Égypte',
            'AE' => 'Émirats arabes unis',
            'EC' => 'Équateur',
            'ER' => 'Érythrée',
            'ES' => 'Espagne',
            'EE' => 'Estonie',
            'SZ' => 'Eswatini',
            'VA' => 'État de la Cité du Vatican',
            'US' => 'États-Unis',
            'ET' => 'Éthiopie',
            'FJ' => 'Fidji',
            'FI' => 'Finlande',
            'FR' => 'France',
            'GA' => 'Gabon',
            'GM' => 'Gambie',
            'GE' => 'Géorgie',
            'GS' => 'Géorgie du Sud-et-les Îles Sandwich du Sud',
            'GH' => 'Ghana',
            'GI' => 'Gibraltar',
            'GR' => 'Grèce',
            'GD' => 'Grenade',
            'GL' => 'Groenland',
            'GP' => 'Guadeloupe',
            'GU' => 'Guam',
            'GT' => 'Guatemala',
            'GG' => 'Guernesey',
            'GN' => 'Guinée',
            'GQ' => 'Guinée équatoriale',
            'GW' => 'Guinée-Bissau',
            'GY' => 'Guyana',
            'GF' => 'Guyane française',
            'HT' => 'Haïti',
            'HN' => 'Honduras',
            'HU' => 'Hongrie',
            'BV' => 'Île Bouvet',
            'CX' => 'Île Christmas',
            'IM' => 'Île de Man',
            'NF' => 'Île Norfolk',
            'AX' => 'Îles Åland',
            'KY' => 'Îles Caïmans',
            'CC' => 'Îles Cocos',
            'CK' => 'Îles Cook',
            'FO' => 'Îles Féroé',
            'HM' => 'Îles Heard-et-MacDonald',
            'FK' => 'Îles Malouines',
            'MP' => 'Îles Mariannes du Nord',
            'MH' => 'Îles Marshall',
            'UM' => 'Îles mineures éloignées des États-Unis',
            'PN' => 'Îles Pitcairn',
            'SB' => 'Îles Salomon',
            'TC' => 'Îles Turques-et-Caïques',
            'VG' => 'Îles Vierges britanniques',
            'VI' => 'Îles Vierges des États-Unis',
            'IN' => 'Inde',
            'ID' => 'Indonésie',
            'IQ' => 'Irak',
            'IR' => 'Iran',
            'IE' => 'Irlande',
            'IS' => 'Islande',
            'IL' => 'Israël',
            'IT' => 'Italie',
            'JM' => 'Jamaïque',
            'JP' => 'Japon',
            'JE' => 'Jersey',
            'JO' => 'Jordanie',
            'KZ' => 'Kazakhstan',
            'KE' => 'Kenya',
            'KG' => 'Kirghizstan',
            'KI' => 'Kiribati',
            'KW' => 'Koweït',
            'RE' => 'La Réunion',
            'LA' => 'Laos',
            'LS' => 'Lesotho',
            'LV' => 'Lettonie',
            'LB' => 'Liban',
            'LR' => 'Liberia',
            'LY' => 'Libye',
            'LI' => 'Liechtenstein',
            'LT' => 'Lituanie',
            'LU' => 'Luxembourg',
            'MK' => 'Macédoine du Nord',
            'MG' => 'Madagascar',
            'MY' => 'Malaisie',
            'MW' => 'Malawi',
            'MV' => 'Maldives',
            'ML' => 'Mali',
            'MT' => 'Malte',
            'MA' => 'Maroc',
            'MQ' => 'Martinique',
            'MU' => 'Maurice',
            'MR' => 'Mauritanie',
            'YT' => 'Mayotte',
            'MX' => 'Mexique',
            'FM' => 'Micronésie',
            'MD' => 'Moldavie',
            'MC' => 'Monaco',
            'MN' => 'Mongolie',
            'ME' => 'Monténégro',
            'MS' => 'Montserrat',
            'MZ' => 'Mozambique',
            'MM' => 'Myanmar (Birmanie)',
            'NA' => 'Namibie',
            'NR' => 'Nauru',
            'NP' => 'Népal',
            'NI' => 'Nicaragua',
            'NE' => 'Niger',
            'NG' => 'Nigeria',
            'NU' => 'Niue',
            'NO' => 'Norvège',
            'NC' => 'Nouvelle-Calédonie',
            'NZ' => 'Nouvelle-Zélande',
            'OM' => 'Oman',
            'UG' => 'Ouganda',
            'UZ' => 'Ouzbékistan',
            'PK' => 'Pakistan',
            'PW' => 'Palaos',
            'PA' => 'Panama',
            'PG' => 'Papouasie-Nouvelle-Guinée',
            'PY' => 'Paraguay',
            'NL' => 'Pays-Bas',
            'BQ' => 'Pays-Bas caribéens',
            'PE' => 'Pérou',
            'PH' => 'Philippines',
            'PL' => 'Pologne',
            'PF' => 'Polynésie française',
            'PR' => 'Porto Rico',
            'PT' => 'Portugal',
            'QA' => 'Qatar',
            'HK' => 'R.A.S. chinoise de Hong Kong',
            'MO' => 'R.A.S. chinoise de Macao',
            'CF' => 'République centrafricaine',
            'DO' => 'République dominicaine',
            'RO' => 'Roumanie',
            'GB' => 'Royaume-Uni',
            'RU' => 'Russie',
            'RW' => 'Rwanda',
            'EH' => 'Sahara occidental',
            'BL' => 'Saint-Barthélemy',
            'KN' => 'Saint-Christophe-et-Niévès',
            'SM' => 'Saint-Marin',
            'MF' => 'Saint-Martin',
            'SX' => 'Saint-Martin (partie néerlandaise)',
            'PM' => 'Saint-Pierre-et-Miquelon',
            'VC' => 'Saint-Vincent-et-les Grenadines',
            'SH' => 'Sainte-Hélène',
            'LC' => 'Sainte-Lucie',
            'SV' => 'Salvador',
            'WS' => 'Samoa',
            'AS' => 'Samoa américaines',
            'ST' => 'Sao Tomé-et-Principe',
            'SN' => 'Sénégal',
            'RS' => 'Serbie',
            'SC' => 'Seychelles',
            'SL' => 'Sierra Leone',
            'SG' => 'Singapour',
            'SK' => 'Slovaquie',
            'SI' => 'Slovénie',
            'SO' => 'Somalie',
            'SD' => 'Soudan',
            'SS' => 'Soudan du Sud',
            'LK' => 'Sri Lanka',
            'SE' => 'Suède',
            'CH' => 'Suisse',
            'SR' => 'Suriname',
            'SJ' => 'Svalbard et Jan Mayen',
            'SY' => 'Syrie',
            'TJ' => 'Tadjikistan',
            'TW' => 'Taïwan',
            'TZ' => 'Tanzanie',
            'TD' => 'Tchad',
            'CZ' => 'Tchéquie',
            'TF' => 'Terres australes françaises',
            'IO' => 'Territoire britannique de l’océan Indien',
            'PS' => 'Territoires palestiniens',
            'TH' => 'Thaïlande',
            'TL' => 'Timor oriental',
            'TG' => 'Togo',
            'TK' => 'Tokelau',
            'TO' => 'Tonga',
            'TT' => 'Trinité-et-Tobago',
            'TN' => 'Tunisie',
            'TM' => 'Turkménistan',
            'TR' => 'Turquie',
            'TV' => 'Tuvalu',
            'UA' => 'Ukraine',
            'UY' => 'Uruguay',
            'VU' => 'Vanuatu',
            'VE' => 'Venezuela',
            'VN' => 'Viêt Nam',
            'WF' => 'Wallis-et-Futuna',
            'YE' => 'Yémen',
            'ZM' => 'Zambie',
            'ZW' => 'Zimbabwe',
        ];
        $en = [
            'AF' => 'Afghanistan',
            'AX' => 'Åland Islands',
            'AL' => 'Albania',
            'DZ' => 'Algeria',
            'AS' => 'American Samoa',
            'AD' => 'Andorra',
            'AO' => 'Angola',
            'AI' => 'Anguilla',
            'AQ' => 'Antarctica',
            'AG' => 'Antigua & Barbuda',
            'AR' => 'Argentina',
            'AM' => 'Armenia',
            'AW' => 'Aruba',
            'AU' => 'Australia',
            'AT' => 'Austria',
            'AZ' => 'Azerbaijan',
            'BS' => 'Bahamas',
            'BH' => 'Bahrain',
            'BD' => 'Bangladesh',
            'BB' => 'Barbados',
            'BY' => 'Belarus',
            'BE' => 'Belgium',
            'BZ' => 'Belize',
            'BJ' => 'Benin',
            'BM' => 'Bermuda',
            'BT' => 'Bhutan',
            'BO' => 'Bolivia',
            'BA' => 'Bosnia & Herzegovina',
            'BW' => 'Botswana',
            'BV' => 'Bouvet Island',
            'BR' => 'Brazil',
            'IO' => 'British Indian Ocean Territory',
            'VG' => 'British Virgin Islands',
            'BN' => 'Brunei',
            'BG' => 'Bulgaria',
            'BF' => 'Burkina Faso',
            'BI' => 'Burundi',
            'KH' => 'Cambodia',
            'CM' => 'Cameroon',
            'CA' => 'Canada',
            'CV' => 'Cape Verde',
            'BQ' => 'Caribbean Netherlands',
            'KY' => 'Cayman Islands',
            'CF' => 'Central African Republic',
            'TD' => 'Chad',
            'CL' => 'Chile',
            'CN' => 'China',
            'CX' => 'Christmas Island',
            'CC' => 'Cocos (Keeling) Islands',
            'CO' => 'Colombia',
            'KM' => 'Comoros',
            'CG' => 'Congo - Brazzaville',
            'CD' => 'Congo - Kinshasa',
            'CK' => 'Cook Islands',
            'CR' => 'Costa Rica',
            'CI' => 'Côte d’Ivoire',
            'HR' => 'Croatia',
            'CU' => 'Cuba',
            'CW' => 'Curaçao',
            'CY' => 'Cyprus',
            'CZ' => 'Czechia',
            'DK' => 'Denmark',
            'DJ' => 'Djibouti',
            'DM' => 'Dominica',
            'DO' => 'Dominican Republic',
            'EC' => 'Ecuador',
            'EG' => 'Egypt',
            'SV' => 'El Salvador',
            'GQ' => 'Equatorial Guinea',
            'ER' => 'Eritrea',
            'EE' => 'Estonia',
            'SZ' => 'Eswatini',
            'ET' => 'Ethiopia',
            'FK' => 'Falkland Islands',
            'FO' => 'Faroe Islands',
            'FJ' => 'Fiji',
            'FI' => 'Finland',
            'FR' => 'France',
            'GF' => 'French Guiana',
            'PF' => 'French Polynesia',
            'TF' => 'French Southern Territories',
            'GA' => 'Gabon',
            'GM' => 'Gambia',
            'GE' => 'Georgia',
            'DE' => 'Germany',
            'GH' => 'Ghana',
            'GI' => 'Gibraltar',
            'GR' => 'Greece',
            'GL' => 'Greenland',
            'GD' => 'Grenada',
            'GP' => 'Guadeloupe',
            'GU' => 'Guam',
            'GT' => 'Guatemala',
            'GG' => 'Guernsey',
            'GN' => 'Guinea',
            'GW' => 'Guinea-Bissau',
            'GY' => 'Guyana',
            'HT' => 'Haiti',
            'HM' => 'Heard & McDonald Islands',
            'HN' => 'Honduras',
            'HK' => 'Hong Kong SAR China',
            'HU' => 'Hungary',
            'IS' => 'Iceland',
            'IN' => 'India',
            'ID' => 'Indonesia',
            'IR' => 'Iran',
            'IQ' => 'Iraq',
            'IE' => 'Ireland',
            'IM' => 'Isle of Man',
            'IL' => 'Israel',
            'IT' => 'Italy',
            'JM' => 'Jamaica',
            'JP' => 'Japan',
            'JE' => 'Jersey',
            'JO' => 'Jordan',
            'KZ' => 'Kazakhstan',
            'KE' => 'Kenya',
            'KI' => 'Kiribati',
            'KW' => 'Kuwait',
            'KG' => 'Kyrgyzstan',
            'LA' => 'Laos',
            'LV' => 'Latvia',
            'LB' => 'Lebanon',
            'LS' => 'Lesotho',
            'LR' => 'Liberia',
            'LY' => 'Libya',
            'LI' => 'Liechtenstein',
            'LT' => 'Lithuania',
            'LU' => 'Luxembourg',
            'MO' => 'Macao SAR China',
            'MG' => 'Madagascar',
            'MW' => 'Malawi',
            'MY' => 'Malaysia',
            'MV' => 'Maldives',
            'ML' => 'Mali',
            'MT' => 'Malta',
            'MH' => 'Marshall Islands',
            'MQ' => 'Martinique',
            'MR' => 'Mauritania',
            'MU' => 'Mauritius',
            'YT' => 'Mayotte',
            'MX' => 'Mexico',
            'FM' => 'Micronesia',
            'MD' => 'Moldova',
            'MC' => 'Monaco',
            'MN' => 'Mongolia',
            'ME' => 'Montenegro',
            'MS' => 'Montserrat',
            'MA' => 'Morocco',
            'MZ' => 'Mozambique',
            'MM' => 'Myanmar (Burma)',
            'NA' => 'Namibia',
            'NR' => 'Nauru',
            'NP' => 'Nepal',
            'NL' => 'Netherlands',
            'NC' => 'New Caledonia',
            'NZ' => 'New Zealand',
            'NI' => 'Nicaragua',
            'NE' => 'Niger',
            'NG' => 'Nigeria',
            'NU' => 'Niue',
            'NF' => 'Norfolk Island',
            'KP' => 'North Korea',
            'MK' => 'North Macedonia',
            'MP' => 'Northern Mariana Islands',
            'NO' => 'Norway',
            'OM' => 'Oman',
            'PK' => 'Pakistan',
            'PW' => 'Palau',
            'PS' => 'Palestinian Territories',
            'PA' => 'Panama',
            'PG' => 'Papua New Guinea',
            'PY' => 'Paraguay',
            'PE' => 'Peru',
            'PH' => 'Philippines',
            'PN' => 'Pitcairn Islands',
            'PL' => 'Poland',
            'PT' => 'Portugal',
            'PR' => 'Puerto Rico',
            'QA' => 'Qatar',
            'RE' => 'Réunion',
            'RO' => 'Romania',
            'RU' => 'Russia',
            'RW' => 'Rwanda',
            'WS' => 'Samoa',
            'SM' => 'San Marino',
            'ST' => 'São Tomé & Príncipe',
            'SA' => 'Saudi Arabia',
            'SN' => 'Senegal',
            'RS' => 'Serbia',
            'SC' => 'Seychelles',
            'SL' => 'Sierra Leone',
            'SG' => 'Singapore',
            'SX' => 'Sint Maarten',
            'SK' => 'Slovakia',
            'SI' => 'Slovenia',
            'SB' => 'Solomon Islands',
            'SO' => 'Somalia',
            'ZA' => 'South Africa',
            'GS' => 'South Georgia & South Sandwich Islands',
            'KR' => 'South Korea',
            'SS' => 'South Sudan',
            'ES' => 'Spain',
            'LK' => 'Sri Lanka',
            'BL' => 'St. Barthélemy',
            'SH' => 'St. Helena',
            'KN' => 'St. Kitts & Nevis',
            'LC' => 'St. Lucia',
            'MF' => 'St. Martin',
            'PM' => 'St. Pierre & Miquelon',
            'VC' => 'St. Vincent & Grenadines',
            'SD' => 'Sudan',
            'SR' => 'Suriname',
            'SJ' => 'Svalbard & Jan Mayen',
            'SE' => 'Sweden',
            'CH' => 'Switzerland',
            'SY' => 'Syria',
            'TW' => 'Taiwan',
            'TJ' => 'Tajikistan',
            'TZ' => 'Tanzania',
            'TH' => 'Thailand',
            'TL' => 'Timor-Leste',
            'TG' => 'Togo',
            'TK' => 'Tokelau',
            'TO' => 'Tonga',
            'TT' => 'Trinidad & Tobago',
            'TN' => 'Tunisia',
            'TR' => 'Turkey',
            'TM' => 'Turkmenistan',
            'TC' => 'Turks & Caicos Islands',
            'TV' => 'Tuvalu',
            'UM' => 'U.S. Outlying Islands',
            'VI' => 'U.S. Virgin Islands',
            'UG' => 'Uganda',
            'UA' => 'Ukraine',
            'AE' => 'United Arab Emirates',
            'GB' => 'United Kingdom',
            'US' => 'United States',
            'UY' => 'Uruguay',
            'UZ' => 'Uzbekistan',
            'VU' => 'Vanuatu',
            'VA' => 'Vatican City',
            'VE' => 'Venezuela',
            'VN' => 'Vietnam',
            'WF' => 'Wallis & Futuna',
            'EH' => 'Western Sahara',
            'YE' => 'Yemen',
            'ZM' => 'Zambia',
            'ZW' => 'Zimbabwe',
        ];
        foreach ($en as $key => $value) {
            Country::create(
                ['code' => $key, 'name' => $value, 'name_fr' => $fr[$key], 'name_ar' => $ar[$key]]
            );
        }

        //gouvernorat / delegation
        $govDelArray = [
            [
                "Delegations" => [
                    "Carthage",
                    "La Médina",
                    "Bab Bhar",
                    "Bab Souika",
                    "Omrane",
                    "Omrane Supérieur",
                    "Attahrir",
                    "El Menzah",
                    "Cité Alkhadhra",
                    "Bardo",
                    "Séjoumi",
                    "Azzouhour",
                    "Alhrairia",
                    "Sidi Hsine",
                    "Ouardia",
                    "Kabaria",
                    "Sidi Elbéchir",
                    "Jebel Jelloud",
                    "La Goulette",
                    "Le Kram",
                    "La Marsa"
                ],
                "Code" => "11",
                "Nbre" => "21",
                "Gouvernorat" => "Tunis",
                "GouvernoratAr" => "تونس",
                "DelegationsAr" => [
                    "قرطاج",
                    "المدينة",
                    "باب البحر",
                    "باب سويقة",
                    "العمران",
                    "العمران الأعلى",
                    "التحرير",
                    "المنزه",
                    "حي الخضراء",
                    "باردو",
                    "السيجومي",
                    "الزهور",
                    "الحرائرية",
                    "سيدي حسين",
                    "الوردية",
                    "الكبارية",
                    "سيدي البشير",
                    "جبل الجلود",
                    "حلق الوادي",
                    "الكرم",
                    "المرسى"
                ]
            ],
            [
                "Delegations" => [
                    "Ariana Ville",
                    "Soukra",
                    "Raouède",
                    "Kalâat Andalous",
                    "Sidi Thabet",
                    "Cité Attadhamon",
                    "M’nihla"
                ],
                "Code" => "12",
                "Nbre" => "7",
                "Gouvernorat" => "Ariana",
                "GouvernoratAr" => "أريانة",
                "DelegationsAr" => [
                    "أريانة المدينة",
                    "سكرة",
                    "رواد",
                    "قلعة الأندلس",
                    "سيدي ثابت",
                    "حي التضامن",
                    "المنيهلة"
                ]
            ],
            [
                "Delegations" => [
                    "Manouba",
                    "Oued Ellil –Tebourba",
                    "Battan",
                    "Jedaida",
                    "Mornaguia",
                    "Borj Amri",
                    "Douar Hicher"
                ],
                "Code" => "15",
                "Nbre" => "8",
                "Gouvernorat" => "Manouba",
                "GouvernoratAr" => "منوبة",
                "DelegationsAr" => [
                    "منوبة",
                    "وادي الليل",
                    "طبربة",
                    "البطان",
                    "الجديدة",
                    "المرناقية",
                    "برج العامري",
                    "دوار هيشر"
                ]
            ],
            [
                "Delegations" => [
                    "Ben Arous",
                    "Nouvelle Médina",
                    "Mourouj",
                    "Hammam Lif",
                    "Hammam Chatt",
                    "Boumhel Bassatine",
                    "Ezzahra",
                    "Radès",
                    "Megrine",
                    "M’hamdia",
                    "Fouchana",
                    "Mornag"
                ],
                "Code" => "13",
                "Nbre" => "12",
                "Gouvernorat" => "Ben Arous",
                "GouvernoratAr" => "بن عروس",
                "DelegationsAr" => [
                    "بن عروس",
                    "المدينة الجديدة",
                    "المروج",
                    "حمام الأنف",
                    "حمام الشط",
                    "بومهل البساتين",
                    "الزهراء",
                    "رادس",
                    "مقرين",
                    "المحمدية",
                    "فوشانة",
                    "مرناق"
                ]
            ],
            [
                "Delegations" => [
                    "Nabeul",
                    "Dar Chaâbane Elfehri",
                    "Béni Khiar",
                    "Korba",
                    "Menzel Temime",
                    "Mida",
                    "Kelibia",
                    "Hammam Ghezaz",
                    "Haouaria",
                    "Takelsa",
                    "Slimane",
                    "Menzel Bouzelfa",
                    "Béni Khalled",
                    "Grombalia",
                    "Bouârgoub",
                    "Hammamet"
                ],
                "Code" => "91",
                "Nbre" => "16",
                "Gouvernorat" => "Nabeul",
                "GouvernoratAr" => "نابل",
                "DelegationsAr" => [
                    "نابل",
                    "دار شعبان الفهري",
                    "بني خيار",
                    "قربة",
                    "منزل تميم",
                    "الميدة",
                    "قليبية",
                    "حمام الأغزاز",
                    "الهوارية",
                    "تاكلسة",
                    "سليمان",
                    "منزل بوزلفة",
                    "بني خلاد",
                    "قرمبالية",
                    "بوعرقوب",
                    "الحمامات"
                ]
            ],
            [
                "Delegations" => [
                    "Bizerte Nord",
                    "Jarzouna",
                    "Bizerte Sud",
                    "Sejnane",
                    "Joumine",
                    "Mateur",
                    "Ghezala",
                    "Menzel Bourguiba",
                    "Tinja",
                    "Utique",
                    "Ghar El Melh",
                    "Menzel Jemil",
                    "El Alia",
                    "Ras Jebel"
                ],
                "Code" => "21",
                "Nbre" => "14",
                "Gouvernorat" => "Bizerte",
                "GouvernoratAr" => "بنزرت",
                "DelegationsAr" => [
                    "بنزرت الشمالية",
                    "جرزونة",
                    "بنزرت الجنوبية",
                    "سجنان",
                    "جومين",
                    "ماطر",
                    "غزالة",
                    "منزل بورقيبة",
                    "تينجة",
                    "أوتيك",
                    "غار الملح ",
                    "منزل جميل",
                    "العالية ",
                    "رأس الجبل"
                ]
            ],
            [
                "Delegations" => [
                    "Zaghouan",
                    "Zériba",
                    "Bir Mecharga",
                    "Fahs",
                    "Nadhour",
                    "Saouaf"
                ],
                "Code" => "14",
                "Nbre" => "6",
                "Gouvernorat" => "Zaghouan",
                "GouvernoratAr" => "زغوان",
                "DelegationsAr" => [
                    "زغوان",
                    "الزريبة",
                    "بئر مشارقة",
                    "الفحص",
                    "الناظور",
                    "صواف"
                ]
            ],
            [
                "Delegations" => [
                    "Sousse Ville",
                    "Zaouia",
                    "Ksiba",
                    "Thrayat",
                    "Sousse Ryadh",
                    "Sousse Jawhara",
                    "Sousse Sidi Abdelhamid",
                    "Hammam sousse",
                    "Akouda",
                    "Kalâa Elkébira",
                    "Sidi Bouali",
                    "Hergla",
                    "Enfidha",
                    "Bouficha",
                    "Koundar",
                    "Sidi Elheni",
                    "Msaken",
                    "Kalâa Ességhira"
                ],
                "Code" => "84",
                "Nbre" => "16",
                "Gouvernorat" => "Sousse",
                "GouvernoratAr" => "سوسة",
                "DelegationsAr" => [
                    "سوسة المدينة",
                    "الزاوية",
                    "القصيبة",
                    "الثريات ",
                    "سوسة الرياض",
                    "سوسة جوهرة",
                    "سوسة سيدي عبد الحميد",
                    "حمام سوسة",
                    "أكودة",
                    "القلعة الكبرى",
                    "سيدي بوعلي",
                    "هرقلة",
                    "النفيضة",
                    "بوفيشة",
                    "كندار",
                    "سيدي الهاني",
                    "مساكن",
                    "القلعة الصغرى"
                ]
            ],
            [
                "Delegations" => [
                    "Monastir",
                    "Ouerdanine",
                    "Sahline",
                    "Zéramdine",
                    "Béni Hassan",
                    "Jammel",
                    "Benbla",
                    "Moknine",
                    "Bekalta",
                    "Teboulba",
                    "Ksar Helal",
                    "Ksibet Medyouni",
                    "Sayada Lamta Bouhjar"
                ],
                "Code" => "11",
                "Nbre" => "13",
                "Gouvernorat" => "Monastir",
                "GouvernoratAr" => "المنستير",
                "DelegationsAr" => [
                    "المنستيـر",
                    "الوردانيـن",
                    "الساحليـن",
                    "زرمديـن",
                    "بنـي حسان",
                    "جمـال",
                    "بنبلة",
                    "المكنين",
                    "البقالطة",
                    "طبلبة",
                    "قصر هلال",
                    "قصيبة المديوني",
                    "صيادة لمطة بوحجر"
                ]
            ],
            [
                "Delegations" => [
                    "Mahdia",
                    "Boumerdes",
                    "Ouled Chamekh",
                    "Chorbane",
                    "Hbira",
                    "Souassi",
                    "Eljem",
                    "Chebba",
                    "Malloulech",
                    "Sidi Alouane",
                    "Ksour Essef"
                ],
                "Code" => "82",
                "Nbre" => "11",
                "Gouvernorat" => "Mahdia",
                "GouvernoratAr" => "المهدية",
                "DelegationsAr" => [
                    "المهدية",
                    "بومرداس",
                    "أولاد الشامخ",
                    "شربان",
                    "هبيرة",
                    "السواسي",
                    "الجم",
                    "الشابة",
                    "ملولش",
                    "سيدي علوان",
                    "قصور الساف"
                ]
            ],
            [
                "Delegations" => [
                    "Sfax Ville",
                    "Sfax Ouest",
                    "Sakiet Ezzit",
                    "Sakiet Eddaier",
                    "Sfax sud",
                    "Tina",
                    "Agareb",
                    "Jebeniana ",
                    "El Amra",
                    "El Hencha ",
                    "Menzel chaker",
                    "Ghraiba",
                    "Bir Ali Ben Khelifa",
                    "Sekhira",
                    "Mahrès",
                    "Kerkennah"
                ],
                "Code" => "71",
                "Nbre" => "16",
                "Gouvernorat" => "Sfax",
                "GouvernoratAr" => "صفاقس",
                "DelegationsAr" => [
                    "صفاقـس المدينة",
                    "صفاقـس الغربية",
                    "ساقية الزيت",
                    "ساقية الداير",
                    "صفاقس الجنوبية",
                    "طينة",
                    "عقارب",
                    "جبنيانة",
                    "العامرة",
                    "الحنشة",
                    "منزل شاكر",
                    "الغريبة",
                    "بئر علي بن خليفة",
                    "الصخيرة",
                    "المحرس",
                    "قـرقنـة"
                ]
            ],
            [
                "Delegations" => [
                    "Béja nord",
                    "Béja sud",
                    "Amdoun",
                    "Nefza",
                    "Teboursouk",
                    "Tibar",
                    "Testour",
                    "Goubellat",
                    "Mejez El Bab"
                ],
                "Code" => "31",
                "Nbre" => "9",
                "Gouvernorat" => "Béja",
                "GouvernoratAr" => "باجة",
                "DelegationsAr" => [
                    "باجة الشمالية",
                    "باجة الجنوبية",
                    "عمدون",
                    "نفزة",
                    "تبرسق",
                    "تيبار",
                    "تستور",
                    "قبلاط",
                    "مجاز الباب"
                ]
            ],
            [
                "Delegations" => [
                    "Jendouba",
                    "Jendouba Nord",
                    "Boussalem",
                    "Tabarka",
                    "Ain Drahem",
                    "Fernana",
                    "Ghardimaou",
                    "Oued Mliz",
                    "Balta Bouaouene"
                ],
                "Code" => "32",
                "Nbre" => "9",
                "Gouvernorat" => "Jendouba",
                "GouvernoratAr" => "جندوبة",
                "DelegationsAr" => [
                    "جنـدوبة",
                    "جنـدوبة الشمالية",
                    "بوسالم",
                    "طبرقـة",
                    "عين دراهم",
                    "فرنانة",
                    "غار الدماء",
                    "وادي مليز",
                    "بلطة بوعوان"
                ]
            ],
            [
                "Delegations" => [
                    "Kef Ouest",
                    "Kef Est",
                    "Nebeur",
                    "Sakiet Sidi Youssef",
                    "Tejerouine",
                    "Kalâat sinane",
                    "Kalâa El khasba",
                    "Jerissa",
                    "Gsour",
                    "Dahmani",
                    "Le Sers"
                ],
                "Code" => "41",
                "Nbre" => "11",
                "Gouvernorat" => "Kef",
                "GouvernoratAr" => "الكاف",
                "DelegationsAr" => [
                    "الكاف الغربية",
                    "الكاف الشرقية",
                    "نبـر",
                    "ساقية سيدي يوسف",
                    "تاجروين",
                    "قلعة سنان",
                    "القلعة الخصبة",
                    "الجريصة",
                    "القصور",
                    "الدهماني",
                    "السرس"
                ]
            ],
            [
                "Delegations" => [
                    "Siliana nord",
                    "Siliana sud",
                    "Bouarada ",
                    "Gâafour",
                    "El Aroussa",
                    "Le Krib",
                    "Bourouis",
                    "Makther",
                    "Rouhia",
                    "Kesra",
                    "Bargou"
                ],
                "Code" => "42",
                "Nbre" => "11",
                "Gouvernorat" => "Siliana",
                "GouvernoratAr" => "سليانة",
                "DelegationsAr" => [
                    "سليانة الشمالية",
                    "سليانة الجنوبية",
                    "بوعرادة",
                    "قعفور",
                    "العروسة",
                    "الكريب",
                    "بورويس",
                    "مكثر",
                    "الروحية",
                    "كسرى",
                    "برقو"
                ]
            ],
            [
                "Delegations" => [
                    "Kairouan Nord ",
                    "Kairouan Sud",
                    "Chebika",
                    "Sebikha",
                    "Oueslatia",
                    "Haffouz",
                    "El Ala",
                    "Hajeb El Ayoun",
                    "Nasrallah",
                    "Cherarda",
                    "Bouhajla"
                ],
                "Code" => "81",
                "Nbre" => "11",
                "Gouvernorat" => "Kairouan",
                "GouvernoratAr" => "القيروان",
                "DelegationsAr" => [
                    "القيروان الشمالية",
                    "القيروان الجنوبية",
                    "الشبيكة",
                    "السبيخة",
                    "الوسلاتية",
                    "حفوز",
                    "العلا",
                    "حاجب العيون",
                    "نصر الله",
                    "الشراردة",
                    "بوحجلة"
                ]
            ],
            [
                "Delegations" => [
                    "Sidi Bouzid Ouest",
                    "Sidi Bouzid Est",
                    "Jelma",
                    "Sabbalet Ouled Askar",
                    "Bir Hfay",
                    "Sidi Ali Benôun ",
                    "Menzel Bouzayane",
                    "Meknassi",
                    "Souk Jedid",
                    "Mezouna",
                    "Regueb",
                    "Ouled Haffouz"
                ],
                "Code" => "51",
                "Nbre" => "12",
                "Gouvernorat" => "Sidi Bouzid",
                "GouvernoratAr" => "سيدي بوزيد",
                "DelegationsAr" => [
                    "سيدي بوزيد الغربية",
                    "سيدي بوزيد الشرقية",
                    "جلمة",
                    "سبالة أولاد عسكر",
                    "بئر الحفي",
                    "سيدي علي بن عون",
                    "منزل بوزيان",
                    "المكناسي",
                    "سوق الجديد",
                    "المزونة",
                    "الرقاب",
                    "أولاد حفوز"
                ]
            ],
            [
                "Delegations" => [
                    "Kasserine Nord",
                    "Kasserine Sud",
                    "Azzouhour",
                    "Hassi ferid",
                    "Sbitla",
                    "Sbiba",
                    "Jedliane",
                    "El Ayoun",
                    "Tela",
                    "Hidra",
                    "Foussana",
                    "Feriana",
                    "Mejel Bel Abbes"
                ],
                "Code" => "43",
                "Nbre" => "13",
                "Gouvernorat" => "Kasserine",
                "GouvernoratAr" => "القصرين",
                "DelegationsAr" => [
                    "القصرين الشمالية",
                    "القصرين الجنوبية",
                    "الزهور",
                    "حاسي الفريد",
                    "سبيطلة",
                    "سبيبة",
                    "جدليان",
                    "العيون",
                    "تالة",
                    "حيدرة",
                    "فوسانة",
                    "فريانة",
                    "ماجل بلعباس"
                ]
            ],
            [
                "Delegations" => [
                    "Gabès ville",
                    "Gabès ouest",
                    "Gabès sud",
                    "Ghannouch",
                    "Metouia",
                    "Menzel habib",
                    "Hamma",
                    "Matmata",
                    "Matmata nouvelle",
                    "Mareth"
                ],
                "Code" => "64",
                "Nbre" => "10",
                "Gouvernorat" => "Gabès",
                "GouvernoratAr" => "قابس",
                "DelegationsAr" => [
                    "قابـس المدينة",
                    "قابـس الغربية",
                    "قابـس الجنوبية",
                    "غنوش",
                    "المطوية",
                    "منزل الحبيب",
                    "الحامة",
                    "مطماطة",
                    "مطماطة الجديدة",
                    "مارث"
                ]
            ],
            [
                "Delegations" => [
                    "Mednine Nord",
                    "Mednine Sud",
                    "Béni khedach",
                    "Ben Guerdene",
                    "Zazis",
                    "Jerba Houmet Souk",
                    "Jerba Midoun",
                    "Jerba Ajim",
                    "Sidi Makhlouf"
                ],
                "Code" => "63",
                "Nbre" => "9",
                "Gouvernorat" => "Medenine",
                "GouvernoratAr" => "مدنين",
                "DelegationsAr" => [
                    "مدنيـن الشمالية",
                    "مدنين الجنوبية",
                    "بني خداش",
                    "بن قردان",
                    "جرجيس",
                    "جربة حومة السوق",
                    "جربة ميدون",
                    "جربة أجيم",
                    "سيدي مخلوف"
                ]
            ],
            [
                "Delegations" => [
                    "Gafsa Nord",
                    "Sidi Aich",
                    "El Ksar",
                    "Gafsa Sud",
                    "Moulares",
                    "Redyef",
                    "Métlaoui",
                    "El Mdhilla",
                    "El Guettar",
                    "Belkhir",
                    "Sned"
                ],
                "Code" => "52",
                "Nbre" => "11",
                "Gouvernorat" => "Gafsa",
                "GouvernoratAr" => "قفصة",
                "DelegationsAr" => [
                    "قفصة الشمالية",
                    "سيدي عيش",
                    "القصر",
                    "قفصة الجنوبية",
                    "أم العرائس",
                    "الرديف",
                    "المتلوي",
                    "المظيلة",
                    "القطار",
                    "بلخير",
                    "السند"
                ]
            ],
            [
                "Delegations" => [
                    "Tozeur",
                    "Degueche",
                    "Tameghza",
                    "Nefta",
                    "Hezoua"
                ],
                "Code" => "53",
                "Nbre" => "5",
                "Gouvernorat" => "Tozeur",
                "GouvernoratAr" => "توزر",
                "DelegationsAr" => [
                    "توزر",
                    "دقاش",
                    "تمغزة",
                    "نفطة",
                    "حزوة"
                ]
            ],
            [
                "Delegations" => [
                    "Tataouine Nord",
                    "Tataouine Sud",
                    "Smar",
                    "Bir Lahmer",
                    "Ghomrassen",
                    "Dhehiba",
                    "Remada"
                ],
                "Code" => "62",
                "Nbre" => "7",
                "Gouvernorat" => "Tataouine",
                "GouvernoratAr" => "تطاوين",
                "DelegationsAr" => [
                    "تطاوين الشمالية",
                    "تطاوين الجنوبية",
                    "الصمار",
                    "البئر الأحمر",
                    "غمراسن",
                    "ذهيبة",
                    "رمادة"
                ]
            ],
            [
                "Delegations" => [
                    "Kébili Sud",
                    "Kébili Nord",
                    "Souk El Ahad",
                    "Douz nord",
                    "Douz sud",
                    "El Faouar"
                ],
                "Code" => "61",
                "Nbre" => "6",
                "Gouvernorat" => "Kébili",
                "GouvernoratAr" => "قبلي",
                "DelegationsAr" => [
                    "قبلي الجنوبية",
                    "قبلي الشمالية",
                    "سوق الأحد",
                    "دوز الشمالية",
                    "دوز الجنوبية ",
                    "الفوار"
                ]
            ]
        ];

        foreach ($govDelArray as $gov) {

            $gouvernorat = Gouvernorat::create(
                [
                    'nbr' => $gov['Nbre'],
                    'name' => $gov['Gouvernorat'],
                    'name_fr' => $gov['Gouvernorat'],
                    'name_ar' => $gov['GouvernoratAr']
                ]
            );

            foreach ($gov['Delegations'] as $key => $delegation) {
                $gouvernorat->delegations()->create(
                    ['name' => $delegation, 'name_fr' => $delegation, 'name_ar' => $gov['DelegationsAr'][$key]]
                );
            }
        }





        $a = AnneeUniversitaire::create(
            [
                'title' => '2022-2023',
                'start' => '2022-08-10 00:00:00',
                'end' => '2023-08-10 00:00:00',
            ]
        );


        // demande types
        $bourse = DemandeType::create(
            [
                'code' => 'bourses_universitaires',
                'title' => 'University grant',
                'title_fr' => 'Bourse universitaire',
                'title_ar' => ' منحة جامعية',
                'active' => true,
                'order'=> 0,
                'parent_id' => null,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => null,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );


        $NouveauBachelier = DemandeType::create(
            [
                'code' => 'nb',
                'title' => 'New Bachelor',
                'title_fr' => "Nouveau Bachelier",
                'title_ar' => 'ناجح جديد',
                'parent_id' => $bourse->id,
                'active' => true,
                'order'=> 1,
                'parent_id' => 1,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => 'nouveau_bachelier',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );

        $classnb = [
            [ 'id' => 1,  'parent_id'=> null , 'code'=> 'OC_D' ,  'title' => "NOUVEAUX BACHELIERS CAS SOCIAL"],
            [ 'id' => 2,  'parent_id'=> null , 'code'=> 'OC_K' ,  'title' => "NOUVEAUX BACHELIERS FILS PARENT A L ETRANGER"],
            [ 'id' => 3,  'parent_id'=> null , 'code'=> 'OC_RP' , 'title' => "NOUVEAUX BACHELIERS FILS PROF UNIVERSITAIRE"],
            [ 'id' => 4,  'parent_id'=> null , 'code'=> 'OC_R' ,  'title' => "NOUVEAUX BACHELIERS FILS AGENT MESRS"],
            [ 'id' => 5,  'parent_id'=> null , 'code'=> 'OC_P' ,  'title' => "NOUVEAUX BACHELIERS FILS PROF ENS SECONDARE"],
            [ 'id' => 6,  'parent_id'=> 5 ,    'code'=> 'OC_P0' , 'title' => "NOUVEAUX BACHELIERS FILS PROF ENS SECONDARE ORPHELIN"],
            [ 'id' => 7,  'parent_id'=> 5 ,    'code'=> 'OC_P1' , 'title' => "NOUVEAUX BACHELIERS FILS PROF ENS SECONDARE SALAIRE UNIQUE"],
            [ 'id' => 8,  'parent_id'=> 5 ,    'code'=> 'OC_P2' , 'title' => "NOUVEAUX BACHELIERS FILS PROF ENS SECONDARE DOUBLE SALAIRE"],
            [ 'id' => 9,  'parent_id'=> null , 'code'=> 'OC_M' ,  'title' => "NOUVEAUX BACHELIERS FILS MAITRE ENS PRIMAIRE"],
            [ 'id' => 10, 'parent_id'=> 9 ,    'code'=> 'OC_M0' , 'title' => "NOUVEAUX BACHELIERS FILS MAITRE ENS PRIMAIRE ORPHELIN"],
            [ 'id' => 11, 'parent_id'=> 9 ,    'code'=> 'OC_M1' , 'title' => "NOUVEAUX BACHELIERS FILS MAITRE ENS PRIMAIRE SALAIRE UNIQUE"],
            [ 'id' => 12, 'parent_id'=> 9 ,    'code'=> 'OC_M2' , 'title' => "NOUVEAUX BACHELIERS FILS MAITRE ENS PRIMAIRE DOUBLE SALAIRE"],
            [ 'id' => 13, 'parent_id'=> null , 'code'=> 'OC_O' ,  'title' => "NOUVEAUX BACHELIERS FILS OUVRIER EDUCATION"],
            [ 'id' => 14, 'parent_id'=> 13 ,   'code'=> 'OC_O0' , 'title' => "NOUVEAUX BACHELIERS FILS OUVRIER EDUCATION ORPHELIN"],
            [ 'id' => 15, 'parent_id'=> 13 ,   'code'=> 'OC_O1' , 'title' => "NOUVEAUX BACHELIERS FILS OUVRIER EDUCATION SALAIRE UNIQUE"],
            [ 'id' => 16, 'parent_id'=> 13 ,   'code'=> 'OC_O2' , 'title' => "NOUVEAUX BACHELIERS FILS OUVRIER EDUCATION DOUBLE SALAIRE"],
            [ 'id' => 17, 'parent_id'=> null , 'code'=> 'OC_S1' , 'title' => "NOUVEAUX BACHELIERS FILS SURVEILLANT"],
            [ 'id' => 18, 'parent_id'=> null , 'code'=> 'OC_S2' , 'title' => "NOUVEAUX BACHELIERS FILS SURVEILLANT  GENERAL"],
            [ 'id' => 19, 'parent_id'=> null , 'code'=> 'OC_W1' , 'title' => "NOUVEAUX BACHELIERS FILS TECHNICIEN LABO"],
            [ 'id' => 20, 'parent_id'=> null , 'code'=> 'OC_W2' , 'title' => "NOUVEAUX BACHELIERS FILS ADMINISTRATEUR"],
            [ 'id' => 21, 'parent_id'=> null , 'code'=> 'OC_Y1' , 'title' => "NOUVEAUX BACHELIERS FILS INSPECTEUR ENS PRIMAIRE"],
            [ 'id' => 22, 'parent_id'=> null , 'code'=> 'OC_Y2' , 'title' => "NOUVEAUX BACHELIERS FILS INSPECTEUR ENS SECONDAIRE"],
            [ 'id' => 23, 'parent_id'=> null , 'code'=> 'OC_Y3' , 'title' => "NOUVEAUX BACHELIERS FILS CONSEILLER EN INFORMATION ET EN ORIENTATION"],
            [ 'id' => 24, 'parent_id'=> null , 'code'=> 'OC_P3' , 'title' => "NOUVEAUX BACHELIERS FILS PROF RATTACHE A ENSEIG SUP"]
        ];

        foreach ($classnb as $c) {
            Classification::create(
                [
                    'id' => $c['id'],
                    'code' => $c['code'],
                    'title' => $c['title'],
                    'title_fr' => $c['title'],
                    'title_ar' => $c['title'],
                    'parent_id' => $c['parent_id'],
                    'demande_type_id' => $NouveauBachelier->id,
                    'classable' => true,
                    'classable_par_admin' => false,
                    'active' => true,
                ]
            );
        }


        $encoursdetude = DemandeType::create(
            [
                'code' => 'ce',
                'title' => 'Under study',
                'title_fr' => "En cours d'étude",
                'title_ar' => 'في طور الدراسة',
                'parent_id' => $bourse->id,
                'active' => true,
                'order'=> 2,
                'parent_id' => 1,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => 'en_cours_d_etude',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );

        $Renouvellement = DemandeType::create(
            [
                'code' => 'rnw',
                'title' => 'Renewal',
                'title_fr' => "Renouvellement",
                'title_ar' => 'تجديد',
                'parent_id' => $bourse->id,
                'active' => true,
                'order'=> 3,
                'parent_id' => 1,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => 'renouvellement',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );


        $Doctorat = DemandeType::create(
            [
                'code' => 'doc',
                'title' => 'Ph.D',
                'title_fr' => "Doctorat",
                'title_ar' => 'دكتوراه',
                'active' => true,
                'order'=> 5,
                'parent_id' => 1,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => 'doctorat',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );


        $Master = DemandeType::create(
            [
                'code' => 'mstr',
                'title' => 'Master',
                'title_fr' => "Maîtrise",
                'title_ar' => 'ماجستير',
                'active' => true,
                'order'=> 4,
                'parent_id' => 1,
                'bourse_insertion' => false,
                'pret' => true,
                'group' => 'master',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );

        $Aidesociale = DemandeType::create(
            [
                'code' => 'aide_sociale',
                'title' => 'Social assistance',
                'title_fr' => 'Aide sociale',
                'title_ar' => 'المساعدات الاجتماعية',
                'active' => true,
                'order'=> 1,
                'parent_id' => null,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => null,
                'bourse' => false,
                'aide_sociale' => true,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );

        $Boursesdestage = DemandeType::create(
            [
                'code' => 'bourses_de_stage',
                'title' => 'Internship grants',
                'title_fr' => 'Bourses de stage',
                'title_ar' => 'منح التربص',
                'active' => true,
                'order'=> 2,
                'parent_id' => null,
                'bourse_insertion' => false,
                'pret' => false,
                'group' => null,
                'bourse' => false,
                'aide_sociale' => false,
                'bourses_de_stage' => true,
                'visibility' => 'direction',
            ]
        );

        $EtudiantRegulier = DemandeType::create(
            [
                'code' => 'nb_etd_rg',
                'title' => 'Regular student',
                'title_fr' => 'Étudiant régulier',
                'title_ar' => 'طالب عادي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 2,
                'bourse_insertion' => true,
                'pret' => false,
                'group' => 'nouveau_bachelier',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );


        $FilsParentEtranger = DemandeType::create(
            [
                'code' => 'nb_prt_etrg',
                'title' => 'Fils parent a l\'etranger',
                'title_fr' => 'Fils parent à l\'étranger',
                'title_ar' => 'الوالدين في الخارج',
                'active' => true,
                'order'=> 1,
                'parent_id' => 2,
                'bourse_insertion' => true,
                'pret' => false,
                'group' => 'nouveau_bachelier',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );

        $FilsParentMes = DemandeType::create(
            [
                'code' => 'nb_prt_mes',
                'title' => 'Fils parent MES',
                'title_fr' => 'Fils parent MES',
                'title_ar' => 'أحد الوالدين في وزارة التعليم العالي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 2,
                'bourse_insertion' => true,
                'pret' => true,
                'group' => 'nouveau_bachelier',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );


        $FilsParentMeduc = DemandeType::create(
            [
                'code' => 'nb_prt_meduc',
                'title' => 'fils parent MEDUC',
                'title_fr' => 'fils parent MEDUC',
                'title_ar' => 'أحد الوالدين في وزارة التربية',
                'active' => true,
                'order'=> 1,
                'parent_id' => 2,
                'bourse_insertion' => true,
                'pret' => true,
                'group' => 'nouveau_bachelier',
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'visibility' => 'direction',
            ]
        );

        $ceEtudiantRegulier = DemandeType::create(
            [
                'code' => 'ce_etd_rg',
                'title' => 'Regular student',
                'title_fr' => 'Étudiant régulier',
                'title_ar' => 'طالب عادي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 3,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'en_cours_d_etude',
                'visibility' => 'direction',
            ]
        );

        $ceParentEtranger = DemandeType::create(
            [
                'code' => 'ce_prt_etrg',
                'title' => 'Fils parent a l\'etranger',
                'title_fr' => 'Fils parent a l\'etranger',
                'title_ar' => 'الوالدين في الخارج',
                'active' => true,
                'order'=> 1,
                'parent_id' => 3,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'en_cours_d_etude',
                'visibility' => 'direction',
            ]
        );


        $ceParentMes = DemandeType::create(
            [
                'code' => 'ce_prt_mes',
                'title' => 'Fils parent MES',
                'title_fr' => 'Fils parent MES',
                'title_ar' => 'أحد الوالدين في وزارة التعليم العالي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 3,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'en_cours_d_etude',
                'visibility' => 'direction',
            ]
        );


        $ceParentMeduc = DemandeType::create(
            [
                'code' => 'ce_prt_meduc',
                'title' => 'fils parent MEDUC',
                'title_fr' => 'fils parent MEDUC',
                'title_ar' => 'أحد الوالدين في وزارة التربية',
                'active' => true,
                'order'=> 1,
                'parent_id' => 3,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'en_cours_d_etude',
                'visibility' => 'direction',
            ]
        );


        $rnvEtudiantRegulier = DemandeType::create(
            [
                'code' => 'rnv_etd_rg',
                'title' => 'Regular student',
                'title_fr' => 'Étudiant régulier',
                'title_ar' => 'طالب عادي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 4,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement',
                'visibility' => 'direction',
            ]
        );


        $rnvParentMes = DemandeType::create(
            [
                'code' => 'rnv_prt_mes',
                'title' => 'Fils parent MES',
                'title_fr' => 'Fils parent MES',
                'title_ar' => 'أحد الوالدين في وزارة التعليم العالي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 4,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement',
                'visibility' => 'direction',
            ]
        );


        $rnvParentMeduc = DemandeType::create(
            [
                'code' => 'rnv_prt_meduc',
                'title' => 'fils parent MEDUC',
                'title_fr' => 'fils parent MEDUC',
                'title_ar' => 'أحد الوالدين في وزارة التربية',
                'active' => true,
                'order'=> 1,
                'parent_id' => 4,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement',
                'visibility' => 'direction',
            ]
        );


        $docRegulier = DemandeType::create(
            [
                'code' => 'doc_etd_rg',
                'title' => 'Regular student',
                'title_fr' => 'Étudiant régulier',
                'title_ar' => 'طالب عادي',
                'active' => true,
                'order'=> 0,
                'parent_id' => 5,
                'bourse_insertion' => false,
                'pret' => false,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'doctorat',
                'visibility' => 'direction',
            ]
        );


        $docParentEtranger = DemandeType::create(
            [
                'code' => 'doc_prt_etrg',
                'title' => 'Fils parent à l\'étranger',
                'title_fr' => 'Fils parent à l\'étranger',
                'title_ar' => 'الوالدين في الخارج',
                'active' => true,
                'order'=> 2,
                'parent_id' => 5,
                'bourse_insertion' => false,
                'pret' => false,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'doctorat',
                'visibility' => 'direction',
            ]
        );


        $mstrNouveau = DemandeType::create(
            [
                'code' => 'mstr_nv',
                'title' => 'Nouveau',
                'title_fr' => 'Nouveau',
                'title_ar' => 'طالب جديد',
                'active' => true,
                'order'=> 0,
                'parent_id' => 6,
                'bourse_insertion' => false,
                'pret' => false,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'nouveau_master',
                'visibility' => 'direction',
            ]
        );


        $mstrRenouv = DemandeType::create(
            [
                'code' => 'mstr_rnv',
                'title' => 'renouvellement',
                'title_fr' => 'renouvellement',
                'title_ar' => 'تجديد',
                'active' => true,
                'order'=> 1,
                'parent_id' => 6,
                'bourse_insertion' => false,
                'pret' => false,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement_master',
                'visibility' => 'direction',
            ]
        );


        $mstrMerite = DemandeType::create(
            [
                'code' => 'mstr_mrt',
                'title' => 'Mérite',
                'title_fr' => 'Mérite',
                'title_ar' => 'الاستحقاق',
                'active' => true,
                'order'=> 2,
                'parent_id' => 6,
                'bourse_insertion' => false,
                'pret' => false,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'master',
                'visibility' => 'direction',
            ]
        );



        $mstrRegulier = DemandeType::create(
            [
                'code' => 'mstr_nv_etd_rg',
                'title' => 'regular student',
                'title_fr' => 'Étudiant régulier',
                'title_ar' => 'طالب عادي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 25,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'nouveau_master',
                'visibility' => 'direction',
            ]
        );


        $mstrParentEtranger = DemandeType::create(
            [
                'code' => 'mstr_nv_prt_etrg',
                'title' => 'Fils parent a l\'etranger',
                'title_fr' => 'Fils parent a l\'etranger',
                'title_ar' => 'الوالدين في الخارج',
                'active' => true,
                'order'=> 1,
                'parent_id' => 25,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'nouveau_master',
                'visibility' => 'direction',
            ]
        );

        $mstrParentMes = DemandeType::create(
            [
                'code' => 'mstr_nv_prt_mes',
                'title' => 'Fils parent MES',
                'title_fr' => 'Fils parent MES',
                'title_ar' => 'أحد الوالدين في وزارة التعليم العالي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 25,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'nouveau_master',
                'visibility' => 'direction',
            ]
        );


        $mstrParentMeduc = DemandeType::create(
            [
                'code' => 'mstr_nv_prt_meduc',
                'title' => 'fils parent MEDUC',
                'title_fr' => 'fils parent MEDUC',
                'title_ar' => 'أحد الوالدين في وزارة التربية',
                'active' => true,
                'order'=> 1,
                'parent_id' => 25,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'nouveau_master',
                'visibility' => 'direction',
            ]
        );


        $mstrRenouvRegulier = DemandeType::create(
            [
                'code' => 'mstr_rnv_etd_rg',
                'title' => 'regular student',
                'title_fr' => 'Étudiant régulier',
                'title_ar' => 'طالب عادي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 26,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement_master',
                'visibility' => 'direction',
            ]
        );


        $mstrRenouvParentMes = DemandeType::create(
            [
                'code' => 'mstr_nv_prt_mes',
                'title' => 'Fils parent MES',
                'title_fr' => 'Fils parent MES',
                'title_ar' => 'أحد الوالدين في وزارة التعليم العالي',
                'active' => true,
                'order'=> 1,
                'parent_id' => 26,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement_master',
                'visibility' => 'direction',
            ]
        );


        $mstrRenouvParentMeduc = DemandeType::create(
            [
                'code' => 'mstr_nv_prt_meduc',
                'title' => 'fils parent MEDUC',
                'title_fr' => 'fils parent MEDUC',
                'title_ar' => 'أحد الوالدين في وزارة التربية',
                'active' => true,
                'order'=> 1,
                'parent_id' => 26,
                'bourse_insertion' => true,
                'pret' => true,
                'bourse' => true,
                'aide_sociale' => false,
                'bourses_de_stage' => false,
                'group' => 'renouvellement_master',
                'visibility' => 'direction',
            ]
        );




//        for ($i = 1; $i < 30; $i++) {
//            Demande::create(
//                [
//                    'code' => random_int(10000, 9999999),
//                    'etat' => random_int(0, 3),
//                    'user_id' => $i,
//                    'annee_universitaire_id' => $a->id,
//                    'demande_type_id' => $dts[random_int(0, 2)]->id
//                ]
//            );
//        }


        // New Seeders
        $this->call([
            PermissionsSeeder::class,
        ]);

        $this->call([
            AttestationTypeSeeder::class,
            DocumentsAttestationSeeder::class,
            AttestationTypeHasOfficesSeeder::class, // table intermidiaire
            AttestationTypeHasAttestationTypeDocumentsSeeder::class // table intermidiaire
        ]);

    }
}
