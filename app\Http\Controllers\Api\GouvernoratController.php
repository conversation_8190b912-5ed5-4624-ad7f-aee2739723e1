<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreGouvernoratRequest;
use App\Http\Requests\UpdateGouvernoratRequest;
use App\Http\Resources\GouvernoratResource;
use App\Models\Distance;
use App\Models\Gouvernorat;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class GouvernoratController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Gouvernorat', $this->cache_seconds, function () {
            return GouvernoratResource::collection(Gouvernorat::all());
        });
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(StoreGouvernoratRequest $request): Response
    {
        Cache::forget('Gouvernorat');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $g = Gouvernorat::create($data);

        return response(new GouvernoratResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Gouvernorat $gouvernorat
     * @return GouvernoratResource
     */
    #[Pure] public function show(Gouvernorat $gouvernorat): GouvernoratResource
    {
        return new GouvernoratResource($gouvernorat);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateGouvernoratRequest $request
     * @param Gouvernorat $gouvernorat
     * @return GouvernoratResource
     */
    public function edit(UpdateGouvernoratRequest $request, Gouvernorat $gouvernorat): GouvernoratResource
    {
        Cache::forget('Gouvernorat');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        foreach ($request->distances as $distance) {
            $d1 = Distance::where('code_gouv1',$distance['code_gouv1'])
                ->where('code_gouv2',$distance['code_gouv2'])->first();
            if ($d1) {
                $d1->update([
                    'distance' => $distance['distance'],
                ]);
            } else {
                Distance::create([
                    'code_gouv1' => $distance['code_gouv1'],
                    'code_gouv2' => $distance['code_gouv2'],
                    'distance' => $distance['distance'],
                ]);
            }
            $d2 = Distance::where('code_gouv1',$distance['code_gouv2'])
                ->where('code_gouv2',$distance['code_gouv1'])->first();
            if ($d2) {
                $d2->update([
                    'distance' => $distance['distance'],
                ]);
            } else {
                Distance::create([
                    'code_gouv1' => $distance['code_gouv2'],
                    'code_gouv2' => $distance['code_gouv1'],
                    'distance' => $distance['distance'],
                ]);
            }
        }
        $gouvernorat->update($data);

        return new GouvernoratResource($gouvernorat);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Gouvernorat $gouvernorat
     * @return Response
     */
    public function destroy(Gouvernorat $gouvernorat): Response
    {
        if($gouvernorat->delegations->count()){
            throw ValidationException::withMessages(["Cette gouvernorat est utilisé par d'autres tables"]);
//            return response("Cette gouvernorat est utilisé par d'autres tables", 422);
        }

        Cache::forget('Gouvernorat');
        Helpers::clearCacheIdp();

        $gouvernorat->delete();
        return response("", 204);
    }
}
