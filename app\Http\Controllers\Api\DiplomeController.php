<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDiplomeRequest;
use App\Http\Requests\UpdateDiplomeRequest;
use App\Http\Resources\DiplomeResource;
use App\Models\Diplome;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use JetBrains\PhpStorm\Pure;

class DiplomeController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Diplome', $this->cache_seconds, function () {
            return DiplomeResource::collection(Diplome::with('diplomesEtablissements')->get());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDiplomeRequest $request
     * @return Response
     */
    public function store(StoreDiplomeRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Diplome');
        Helpers::clearCacheIdp();

        $g = Diplome::create($data);

        return response(new DiplomeResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Diplome $diplome
     * @return DiplomeResource
     */
    #[Pure] public function show(Diplome $diplome): DiplomeResource
    {
        return new DiplomeResource($diplome);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateDiplomeRequest $request
     * @param Diplome $diplome
     * @return DiplomeResource
     */
    public function edit(UpdateDiplomeRequest $request, Diplome $diplome): DiplomeResource
    {
        $data = $request->validated();

        Cache::forget('Diplome');
        Helpers::clearCacheIdp();

        $diplome->update($data);

        return new DiplomeResource($diplome);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Diplome $diplome
     * @return Response
     */
    public function destroy(Diplome $diplome): Response
    {
        Cache::forget('Diplome');
        Helpers::clearCacheIdp();

        $diplome->delete();

        return response("", 204);
    }
}
