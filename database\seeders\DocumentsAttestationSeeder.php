<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DocumentsAttestationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('documents_attestations')->insert([
            'code' => 'cin',
            'title' => 'National Identification Card',
            'title_fr' => "Carte d'identité Nationale",
            'title_ar' => 'بطاقة التعريف الوطنية',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'attestation_inscription',
            'title' => 'Certificate of registration for the wanted year',
            'title_fr' => "Attestation d'Inscription pour l'année demandée",
            'title_ar' => 'شهادة الترسيم بالنسبة إلى السنة المطلوبة',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'cin_or_passport',
            'title' => 'National Identification Card OR The Three First Pages of The Passport For The International Student',
            'title_fr' => "Carte d'identité Nationale ou les 3 premières pages du passeport pour l’étudiant international",
            'title_ar' => 'بطاقة التعريف الوطنية أو الصفحات الثلاث الأولى من جواز السفر بالنسبة إلى الطالب الدولي',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'attestation_presence_or_attestation_reussite',
            'title' => 'Certificate of presence for the current year or a copy of a certificate of success for previous years',
            'title_fr' => "Attestation de présence pour l'année en cours ou copie d'une attestation de réussite pour les années précédentes",
            'title_ar' => 'شهادة حضور للسنة الجارية أو نسخة من شهادة نجاح بالنسبة للسنوات السابقة',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'attestation_presence',
            'title' => 'Certificate of presence for the current year',
            'title_fr' => "Attestation de présence pour l'année en cours",
            'title_ar' => 'شهادة حضور للسنة الجارية',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'copie_attestation_reussite',
            'title' => 'Copy of a certificate of success for previous years',
            'title_fr' => "Copie d'une attestation de réussite pour les années précédentes",
            'title_ar' => 'نسخة من شهادة نجاح بالنسبة للسنوات السابقة',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'cin_or_carte_versement_bourse_etudiant_etranger',
            'title' => 'A copy of the national identity card or a copy of the university scholarship disbursement card for the international student',
            'title_fr' => "Une copie de la carte nationale d'identité ou une copie de la carte de versement de la bourse universitaire pour l'étudiant international",
            'title_ar' => 'نسخة من بطاقة التعريف الوطنية أو نسخة من بطاقة صرف المنحة الجامعية بالنسبة إلى الطالب الدولي',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'cin_or_carte_versement_bourse_or_three_pages_passport',
            'title' => 'Copy of the national identity card or a copy of the scholarship disbursement card or a copy of the first three pages of the passport for the international student',
            'title_fr' => "Copie de la carte nationale d'identité ou une copie de la carte de versement de la bourse ou une copie des trois premières pages du passeport pour l'étudiant international",
            'title_ar' => 'نسخة من بطاقة التعريف الوطنية أو نسخة من بطاقة صرف المنحة أو نسخة من الصفحات الثلاث الأولى من جواز السفر بالنسبة إلى الطالب الدولي',
        ]);

        DB::table('documents_attestations')->insert([
            'code' => 'certificat_inscription_or_certificat_presence',
            'title' => 'A copy of a certificate of registration or a copy of a certificate of attendance for the requested year',
            'title_fr' => "Une copie d'un certificat d'inscription ou une copie d'un certificat de présence pour l'année demandée",
            'title_ar' => 'نسخة من شهادة ترسيم أو نسخة من شهادة حضور للسنة المطلوبة',
        ]);
    }
}
