<?php

namespace App\Http\Middleware;

use Illuminate\Http\Middleware\TrustHosts as Middleware;

class TrustHosts extends Middleware
{
    /**
     * Get the host patterns that should be trusted.
     *
     * @return array<int, string|null>
     */
    public function hosts()
    {
        $hosts = [
            $this->allSubdomainsOfApplicationUrl(),
            'http://idp_back.test',
            'http://bpas_back.test',
            'https://admin-bpas.oouc.demo.elastic-solutions.com',
            'https://idp.oouc.demo.elastic-solutions.com',
            'https://bpas.oouc.demo.elastic-solutions.com',
            'https://back.oouc.demo.elastic-solutions.com',
            'https://oouc.rnu.tn',
            'https://back-admin-oouc.rnu.tn',
            'https://back-etudiant-oouc.rnu.tn',
            'https://bpas-admin-oouc.rnu.tn',
            'https://bpas-etudiant-oouc.rnu.tn',
        ];
        if (config('app.env') === 'local') {
            $hosts[] = '*';
            $hosts[] = 'http://localhost';
            $hosts[] = 'http://localhost:3000';
            $hosts[] = 'http://localhost:3001';
        }
        return $hosts;
    }
}
