<?php

namespace App\Notifications;

use App\Models\ExportedFile;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class ExportCompletedNotification extends Notification
{
    use Queueable;

    public $row;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(ExportedFile $row)
    {
        $this->row = $row;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Export Completed",
            "subtitle" => " Export Completed for : ". $this->row?->type . " , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Export Complete ",
            "subtitle_fr" => "Export Complete pour ". $this->row?->type . " , à : "  .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => " تم الإصدار ",
            "subtitle_ar" => " تم الإصدار لـ : " . $this->row?->type . " في  " . Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "table-down",
            "avatarAlt" => "Export",
            "avatarText" => "Export",
            "avatarColor" => "info",
            "type" => "export",
            "target_id" => $this->row?->id,
            "target" => $this->row?->attached_file,
            "model" => "ExportedFile",
            "url" => '',

        ];
    }
}
