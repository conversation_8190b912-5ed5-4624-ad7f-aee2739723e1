1748264635O:58:"Illuminate\Http\Resources\Json\AnonymousResourceCollection":8:{s:8:"resource";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:12:{i:0;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:14;s:5:"title";s:9:"2024-2025";s:5:"start";s:10:"2024-09-02";s:3:"end";s:10:"2025-08-01";s:10:"created_at";s:19:"2024-07-06 11:02:57";s:10:"updated_at";s:19:"2024-09-05 13:09:50";s:6:"active";i:1;s:9:"annee_bac";i:15;s:5:"order";N;s:4:"smig";i:5899;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:14;s:5:"title";s:9:"2024-2025";s:5:"start";s:10:"2024-09-02";s:3:"end";s:10:"2025-08-01";s:10:"created_at";s:19:"2024-07-06 11:02:57";s:10:"updated_at";s:19:"2024-09-05 13:09:50";s:6:"active";i:1;s:9:"annee_bac";i:15;s:5:"order";N;s:4:"smig";i:5899;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:1;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:2;s:5:"title";s:9:"2023-2024";s:5:"start";s:10:"2023-09-01";s:3:"end";s:10:"2024-09-01";s:10:"created_at";s:19:"2023-08-25 10:48:41";s:10:"updated_at";s:19:"2024-09-04 06:56:52";s:6:"active";i:1;s:9:"annee_bac";i:2;s:5:"order";i:1;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:2;s:5:"title";s:9:"2023-2024";s:5:"start";s:10:"2023-09-01";s:3:"end";s:10:"2024-09-01";s:10:"created_at";s:19:"2023-08-25 10:48:41";s:10:"updated_at";s:19:"2024-09-04 06:56:52";s:6:"active";i:1;s:9:"annee_bac";i:2;s:5:"order";i:1;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:2;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:5:"title";s:9:"2022-2023";s:5:"start";s:10:"2022-09-01";s:3:"end";s:10:"2023-08-31";s:10:"created_at";s:19:"2023-06-08 09:46:40";s:10:"updated_at";s:19:"2024-05-07 07:00:33";s:6:"active";i:1;s:9:"annee_bac";i:1;s:5:"order";i:2;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:1;s:5:"title";s:9:"2022-2023";s:5:"start";s:10:"2022-09-01";s:3:"end";s:10:"2023-08-31";s:10:"created_at";s:19:"2023-06-08 09:46:40";s:10:"updated_at";s:19:"2024-05-07 07:00:33";s:6:"active";i:1;s:9:"annee_bac";i:1;s:5:"order";i:2;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:3;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:3;s:5:"title";s:9:"2021-2022";s:5:"start";s:10:"2021-09-01";s:3:"end";s:10:"2022-08-31";s:10:"created_at";s:19:"2023-12-01 10:06:39";s:10:"updated_at";s:19:"2024-05-07 07:00:47";s:6:"active";i:1;s:9:"annee_bac";i:3;s:5:"order";i:3;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:3;s:5:"title";s:9:"2021-2022";s:5:"start";s:10:"2021-09-01";s:3:"end";s:10:"2022-08-31";s:10:"created_at";s:19:"2023-12-01 10:06:39";s:10:"updated_at";s:19:"2024-05-07 07:00:47";s:6:"active";i:1;s:9:"annee_bac";i:3;s:5:"order";i:3;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:4;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:7;s:5:"title";s:9:"2020-2021";s:5:"start";s:10:"2020-09-01";s:3:"end";s:10:"2021-08-31";s:10:"created_at";s:19:"2024-04-19 13:35:30";s:10:"updated_at";s:19:"2024-05-07 07:00:59";s:6:"active";i:1;s:9:"annee_bac";i:4;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:7;s:5:"title";s:9:"2020-2021";s:5:"start";s:10:"2020-09-01";s:3:"end";s:10:"2021-08-31";s:10:"created_at";s:19:"2024-04-19 13:35:30";s:10:"updated_at";s:19:"2024-05-07 07:00:59";s:6:"active";i:1;s:9:"annee_bac";i:4;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:5;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:6;s:5:"title";s:9:"2019-2020";s:5:"start";s:10:"2019-09-01";s:3:"end";s:10:"2020-08-31";s:10:"created_at";s:19:"2024-03-15 07:32:23";s:10:"updated_at";s:19:"2024-05-07 07:01:09";s:6:"active";i:1;s:9:"annee_bac";i:5;s:5:"order";i:5;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:6;s:5:"title";s:9:"2019-2020";s:5:"start";s:10:"2019-09-01";s:3:"end";s:10:"2020-08-31";s:10:"created_at";s:19:"2024-03-15 07:32:23";s:10:"updated_at";s:19:"2024-05-07 07:01:09";s:6:"active";i:1;s:9:"annee_bac";i:5;s:5:"order";i:5;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:6;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:12;s:5:"title";s:9:"2018-2019";s:5:"start";s:10:"2018-09-01";s:3:"end";s:10:"2019-08-31";s:10:"created_at";s:19:"2024-05-06 17:55:31";s:10:"updated_at";s:19:"2024-05-07 07:01:21";s:6:"active";i:1;s:9:"annee_bac";i:6;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:12;s:5:"title";s:9:"2018-2019";s:5:"start";s:10:"2018-09-01";s:3:"end";s:10:"2019-08-31";s:10:"created_at";s:19:"2024-05-06 17:55:31";s:10:"updated_at";s:19:"2024-05-07 07:01:21";s:6:"active";i:1;s:9:"annee_bac";i:6;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:7;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:8;s:5:"title";s:9:"2017-2018";s:5:"start";s:10:"2017-09-01";s:3:"end";s:10:"2018-08-31";s:10:"created_at";s:19:"2024-05-06 10:24:18";s:10:"updated_at";s:19:"2024-05-06 10:24:18";s:6:"active";i:1;s:9:"annee_bac";i:7;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:8;s:5:"title";s:9:"2017-2018";s:5:"start";s:10:"2017-09-01";s:3:"end";s:10:"2018-08-31";s:10:"created_at";s:19:"2024-05-06 10:24:18";s:10:"updated_at";s:19:"2024-05-06 10:24:18";s:6:"active";i:1;s:9:"annee_bac";i:7;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:8;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:9;s:5:"title";s:9:"2016-2017";s:5:"start";s:10:"2016-09-01";s:3:"end";s:10:"2017-08-31";s:10:"created_at";s:19:"2024-05-06 17:45:40";s:10:"updated_at";s:19:"2024-05-07 07:01:36";s:6:"active";i:1;s:9:"annee_bac";i:8;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:9;s:5:"title";s:9:"2016-2017";s:5:"start";s:10:"2016-09-01";s:3:"end";s:10:"2017-08-31";s:10:"created_at";s:19:"2024-05-06 17:45:40";s:10:"updated_at";s:19:"2024-05-07 07:01:36";s:6:"active";i:1;s:9:"annee_bac";i:8;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:9;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:10;s:5:"title";s:9:"2015-2016";s:5:"start";s:10:"2015-09-01";s:3:"end";s:10:"2016-08-31";s:10:"created_at";s:19:"2024-05-06 17:47:15";s:10:"updated_at";s:19:"2024-05-07 07:03:21";s:6:"active";i:1;s:9:"annee_bac";i:9;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:10;s:5:"title";s:9:"2015-2016";s:5:"start";s:10:"2015-09-01";s:3:"end";s:10:"2016-08-31";s:10:"created_at";s:19:"2024-05-06 17:47:15";s:10:"updated_at";s:19:"2024-05-07 07:03:21";s:6:"active";i:1;s:9:"annee_bac";i:9;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:10;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:11;s:5:"title";s:9:"2014-2015";s:5:"start";s:10:"2014-09-01";s:3:"end";s:10:"2015-08-31";s:10:"created_at";s:19:"2024-05-06 17:48:56";s:10:"updated_at";s:19:"2024-05-07 07:03:55";s:6:"active";i:1;s:9:"annee_bac";i:10;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:11;s:5:"title";s:9:"2014-2015";s:5:"start";s:10:"2014-09-01";s:3:"end";s:10:"2015-08-31";s:10:"created_at";s:19:"2024-05-06 17:48:56";s:10:"updated_at";s:19:"2024-05-07 07:03:55";s:6:"active";i:1;s:9:"annee_bac";i:10;s:5:"order";N;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:11;O:45:"App\Http\Resources\AnneeUniversitaireResource":4:{s:8:"resource";O:29:"App\Models\AnneeUniversitaire":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"bpasBack.annee_universitaires";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:5;s:5:"title";s:9:"2013-2014";s:5:"start";s:10:"2013-09-01";s:3:"end";s:10:"2014-07-31";s:10:"created_at";s:19:"2024-03-11 10:44:58";s:10:"updated_at";s:19:"2024-05-06 17:52:54";s:6:"active";i:1;s:9:"annee_bac";i:11;s:5:"order";i:4;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:11:" * original";a:11:{s:2:"id";i:5;s:5:"title";s:9:"2013-2014";s:5:"start";s:10:"2013-09-01";s:3:"end";s:10:"2014-07-31";s:10:"created_at";s:19:"2024-03-11 10:44:58";s:10:"updated_at";s:19:"2024-05-06 17:52:54";s:6:"active";i:1;s:9:"annee_bac";i:11;s:5:"order";i:4;s:4:"smig";i:5512;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:6:"active";s:7:"boolean";s:5:"start";s:4:"date";s:3:"end";s:4:"date";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"active";i:1;s:5:"title";i:2;s:4:"smig";i:3;s:5:"start";i:4;s:3:"end";i:5;s:9:"annee_bac";i:6;s:5:"order";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:8:"collects";s:45:"App\Http\Resources\AnneeUniversitaireResource";s:10:"collection";r:2;s:29:" * preserveAllQueryParameters";b:0;s:18:" * queryParameters";N;s:12:"preserveKeys";b:1;}