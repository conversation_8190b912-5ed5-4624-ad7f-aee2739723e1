<?php

namespace App\Notifications;

use App\Models\Admin;
use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class DossierIncoherentAdminNotification extends Notification
{
    use Queueable;

    private Demande $demande;
    private Admin $sender;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande, Admin $sender)
    {
        $this->demande = $demande;
        $this->sender = $sender;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Incoherent folder",
            "subtitle" => "Incoherent folder for demand n°: ". $this->demande->code.". send by : ". $this->sender->name,
            "title_fr" => "Dossier incohérent",
            "subtitle_fr" => "Dossier incohérent pour la demande n°: " . $this->demande->code.". envoyer par : ". $this->sender->name,
            "title_ar" => "الملف متنافر",
            "subtitle_ar" => " الملف متنافر للطلب : " . $this->demande->code.". أرسله : ". $this->sender->name,
            "avatarIcon" => "traffic-cone",
            "avatarAlt" => "Dossier",
            "avatarText" => "Dossier",
            "avatarColor" => "warning",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
