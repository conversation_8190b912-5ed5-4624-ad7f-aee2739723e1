<?php

namespace App\Jobs;

use App\Models\Mandate;
use App\Models\OrdonnanceFile;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use NumberToWords\NumberToWords;
use \Mpdf\Mpdf as PDF;


class GenerateOrdonnancePDF implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;
    public $id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data, $id)
    {
        $this->data = $data;
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            ini_set('memory_limit', '-1');

            $request = $this->data;

            $this->ordonance_collective($request['date_payment']);
        } catch (\Exception $e) {
            $this->failed($e);
        }

    }

    public function failed(\Exception $exception)
    {
        OrdonnanceFile::findOrFail($this->id)->update([
            'etat' => 2
        ]);
    }

    function ordonance_collective($date_payement){
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');
        $formatted_date = Carbon::createFromFormat('d/m/Y', $date_payement)->format('Y-m-d');

    $mpdf->SetHTMLHeader("Ordonnance Collective du Payement du ".$formatted_date."<hr>");
        $mpdf->SetHTMLFooter('<div style="text-align:center">-- {PAGENO}/{nb} --</div>');
        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>Ordonnance Collective du Payement du ".$formatted_date."</P>");

    $mpdf->WriteHTML("<table align=center border=1 style='width:75%;border-collapse:collapse;'>");
        $mpdf->WriteHTML("<thead><tr><th>N° Emission</th><th>N° CIN</th><th>Nom/Prénom</th><th>Montant</th></tr></thead>");
        $total_mnt=0;

    Mandate::whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)
        ->orderBy(DB::raw('CAST(num_emission AS INTEGER)'),'asc')
        ->chunk(1000, function ($mandates) use ($mpdf, &$total_mnt) {
            foreach ($mandates as $key => $row) {
                $total_mnt+=$row->net_a_payer;
                $mpdf->WriteHTML("<tr><td align=center>".$row->num_emission."</td><td align=center>".$row->cin."</td><td>".$row->nom."</td><td align=right>".number_format($row->net_a_payer, 3, ',', ' ')."</td></tr>");
            }
    });


    $mpdf->WriteHTML("<tr><td colspan=3 align=center>Total</td><td align=right>".number_format($total_mnt, 3, ',', ' ')."</td></tr>");
         $mpdf->WriteHTML("</table>");

    $mpdf->AddPage();

    $numberToWords = new NumberToWords();
    $words = $numberToWords->getNumberTransformer('fr')->toWords($total_mnt);
        $mpdf->WriteHTML("Arrêter la présente état à la somme de: ".$words." Dinars<br><div style=' text-align:justify;'>Vu, Approuvée et Liquides la présente Depense à la Somme de <b>".$words." Dinars</b><br>Sousse le ...........................</div>");


    $filename = 'ordonnances/Ordonnance_Collective_'.$formatted_date.'_'.time().'.pdf';

    Storage::put($filename, $mpdf->Output($filename, "S"));
    OrdonnanceFile::findOrFail($this->id)->update([
        'etat' => 1,
        'path' => $filename
    ]);
    }

}
