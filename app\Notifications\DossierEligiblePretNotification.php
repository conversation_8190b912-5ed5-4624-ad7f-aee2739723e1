<?php

namespace App\Notifications;

use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class DossierEligiblePretNotification extends Notification
{
    use Queueable;

    private Demande $demande;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande)
    {
        $this->demande = $demande;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Favorable folder for a loan",
            "subtitle" => "Favorable folder for a university loan for demand n°: ". $this->demande->code,
            "title_fr" => "Dossier favorable pour un prêt universitaire",
            "subtitle_fr" => "Dossier favorable pour un prêt universitaire pour la demande n°: " . $this->demande->code,
            "title_ar" => "الملف مناسب للحصول على قرض ",
            "subtitle_ar" => " الملف مناسب للحصول على قرض جامعي للطلب : " . $this->demande->code,
            "avatarIcon" => "report-money",
            "avatarAlt" => "Dossier",
            "avatarText" => "Dossier",
            "avatarColor" => "primary",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
