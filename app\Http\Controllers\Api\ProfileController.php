<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use App\Models\Admin;
use App\Models\Role;
use Carbon\Carbon;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    public function change_password(Request $request): JsonResponse
    {
        $user=$request->user();
        $validator = Validator::make($request->all(), [
            'currentPassword' => ['required', function ($attribute, $value, $fail) use ($user) {
                if (!Hash::check($value, $user->password)) {
                    return $fail(__('validation.current_password'));
                }
                return null;
            }],
            'password'=>'required|min:8|max:15',
            'confirmPassword'=>'required|same:password'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        $user->update([
            'password'=>Hash::make($request->password)
        ]);
        return response()->json([
            'message'=>'Password successfully updated',
        ],200);


    }

    public function update_profile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'firstName'=>'required|min:5|max:40',
            'name'=>'required|min:5|max:40',
            'email'=>'required|email',
            'profile_photo'=>'nullable|image|mimes:jpg,bmp,png'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        $user=$request->user();

        if($request->hasFile('profile_photo')){
            if($user->profile_photo){
                $old_path=public_path().'/uploads/profile_images/'.$user->profile_photo;
                if(File::exists($old_path)){
                    File::delete($old_path);
                }
            }

            $image_name='profile-image-'.time().'.'.$request->profile_photo->extension();
            $request->profile_photo->move(public_path('/uploads/profile_images'),$image_name);
        }else{
            $image_name=$user->profile_photo;
        }


        $user->update([
            'firstName' => $request->firstName,
            'name' => $request->name,
            'username' => $request->email,
            'email'=>$request->email,
            'profile_photo'=>$image_name
        ]);

        return response()->json([
            'message'=>'Profile successfully updated'
        ],200);


    }

    public function update_users_profile( Request $request,Admin $user ): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name'=>'required|min:2|max:40',
            'password' => [
                'sometimes',
                Password::min(8)
                    ->letters()
                    ->symbols(),
            ],
            'confirmPassword'=>'required_if:password,filled|same:password',
            'email'=>'required|email',
            'roles' => 'nullable|sometimes',
            'roles.*.roleName' => 'sometimes|nullable',
            'roles.*.start_at' => 'sometimes|nullable',
            'roles.*.end_at' => 'sometimes|nullable',
            'office_id' => 'required|numeric'
        ]);


        // 'role' => 'required',
        $validator->sometimes('confirmPassword', 'required', function($input)
        {
            return $input->password !== $input->confirmPassword;
        });

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        if($request->hasFile('profile_photo')){
            if($user->profile_photo){
                $old_path=public_path().'/uploads/profile_images/'.$user->profile_photo;
                if(File::exists($old_path)){
                    File::delete($old_path);
                }
            }

            $image_name='profile-image-'.time().'.'.$request->profile_photo->extension();
            $request->profile_photo->move(public_path('/uploads/profile_images'),$image_name);
        }else{
            $image_name=$user->profile_photo;
        }

        $request['password'] = $request['password'] ? bcrypt($request['password']) : $user->password;
        $user->update([
            'name' => $request->name,
            'email'=>$request->email,
            'profile_photo'=>$image_name,
            'password'=>$request->password,
            'office_id' => $request->office_id
        ]);


        $user->syncRoles([]);
        $roleList = $request->roles ?? [];

        foreach($roleList as $role){

            $startDate = $role['start_at'] ? Carbon::parse($role['start_at'])->tz('Europe/Paris') : null;
            $endDate = $role['end_at'] ? Carbon::parse($role['end_at'])->tz('Europe/Paris') : null;
            $role = Role::where('name', $role['roleName'])->first();

            $user->roles()->attach($role, [
                'start_at' => $startDate,
                'end_at' => $endDate,
            ]);

        }

        return response()->json($user,200);
    }

    public function update_users_status(Request $request, Admin $user): JsonResponse
    {
        $user->update([
            'status'=>$request->status
        ]);

        return response()->json($user,200);
    }

}
