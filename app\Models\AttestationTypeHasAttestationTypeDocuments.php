<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class AttestationTypeHasAttestationTypeDocuments extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $table = "attest_type_has_doc_attest";
    protected $fillable = ['attestation_types_id', 'documents_attestations_id'];

    public function documentsAttestations() : BelongsTo
    {
        return $this->belongsTo(DocumentsAttestation::class);
    }

}
