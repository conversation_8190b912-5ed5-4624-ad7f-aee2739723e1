<?php

namespace App\Models\Scopes;

use App\Models\Demande;
use App\Models\DemandeType;
use App\Models\DemandeTypeHasRole;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DemandeTypeScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $user = Auth::user();
        if($user && !($user->id == 1))
        {
            // if user works in a direction regionale, filter which demandeTypes he can see
            if($user->office && $user->office->parent_id !== null) {
                // some users should be able to manage demande types, but not see demandes that have that type, exp: admin that works in a direction_regionale

                $ids = DemandeTypeHasRole::whereIn('role_id', $user->roles->pluck('id'))->pluck('demande_type_id');
                $builder->whereIn('id', $ids)->where('visibility', 'direction');
            }
            if ($user->office && $user->office->parent_id == null) {
                # code...
                $ids = DemandeTypeHasRole::whereIn('role_id', $user->roles->pluck('id'))->pluck('demande_type_id');
                $builder->whereIn('id', $ids);

            }

        }
    }
}
