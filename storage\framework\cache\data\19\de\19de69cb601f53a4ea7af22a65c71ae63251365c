1748264817O:58:"Illuminate\Http\Resources\Json\AnonymousResourceCollection":8:{s:8:"resource";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:45:{i:0;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:1;s:5:"label";s:27:"score_revenu_total_inf_smig";s:8:"label_fr";s:27:"score_revenu_total_inf_smig";s:8:"label_ar";s:27:"score_revenu_total_inf_smig";s:4:"code";s:27:"score_revenu_total_inf_smig";s:5:"value";s:2:"30";s:10:"created_at";s:19:"2024-01-18 09:20:49";s:10:"updated_at";s:19:"2024-01-18 09:35:51";s:5:"field";s:25:"compare_revenu_total_smig";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:1;s:5:"label";s:27:"score_revenu_total_inf_smig";s:8:"label_fr";s:27:"score_revenu_total_inf_smig";s:8:"label_ar";s:27:"score_revenu_total_inf_smig";s:4:"code";s:27:"score_revenu_total_inf_smig";s:5:"value";s:2:"30";s:10:"created_at";s:19:"2024-01-18 09:20:49";s:10:"updated_at";s:19:"2024-01-18 09:35:51";s:5:"field";s:25:"compare_revenu_total_smig";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:1;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:2;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_revenu_total_between_smig";s:5:"value";s:2:"15";s:10:"created_at";s:19:"2024-01-18 09:37:40";s:10:"updated_at";s:19:"2024-01-18 09:37:40";s:5:"field";s:25:"compare_revenu_total_smig";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:2;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_revenu_total_between_smig";s:5:"value";s:2:"15";s:10:"created_at";s:19:"2024-01-18 09:37:40";s:10:"updated_at";s:19:"2024-01-18 09:37:40";s:5:"field";s:25:"compare_revenu_total_smig";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:2;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:3;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:27:"score_revenu_total_sup_smig";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:37:52";s:10:"updated_at";s:19:"2024-01-18 09:37:52";s:5:"field";s:25:"compare_revenu_total_smig";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:3;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:27:"score_revenu_total_sup_smig";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:37:52";s:10:"updated_at";s:19:"2024-01-18 09:37:52";s:5:"field";s:25:"compare_revenu_total_smig";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:3;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:4;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_familiale_dece0";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:38:25";s:10:"updated_at";s:19:"2024-01-18 09:51:33";s:5:"field";s:24:"situation_familiale_dece";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:4;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_familiale_dece0";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:38:25";s:10:"updated_at";s:19:"2024-01-18 09:51:33";s:5:"field";s:24:"situation_familiale_dece";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:4;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:5;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_familiale_dece1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:39:45";s:10:"updated_at";s:19:"2024-01-18 09:51:42";s:5:"field";s:24:"situation_familiale_dece";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:5;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_familiale_dece1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:39:45";s:10:"updated_at";s:19:"2024-01-18 09:51:42";s:5:"field";s:24:"situation_familiale_dece";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:5;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:6;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_familiale_dece2";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:40:05";s:10:"updated_at";s:19:"2024-01-18 09:51:50";s:5:"field";s:24:"situation_familiale_dece";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:6;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_familiale_dece2";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:40:05";s:10:"updated_at";s:19:"2024-01-18 09:51:50";s:5:"field";s:24:"situation_familiale_dece";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:6;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:7;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_familiale_divorce0";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:40:18";s:10:"updated_at";s:19:"2024-01-18 09:52:04";s:5:"field";s:27:"situation_familiale_divorce";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:7;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_familiale_divorce0";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:40:18";s:10:"updated_at";s:19:"2024-01-18 09:52:04";s:5:"field";s:27:"situation_familiale_divorce";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:7;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:8;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_familiale_divorce1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:43:39";s:10:"updated_at";s:19:"2024-01-18 09:52:14";s:5:"field";s:27:"situation_familiale_divorce";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:8;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_familiale_divorce1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:43:39";s:10:"updated_at";s:19:"2024-01-18 09:52:14";s:5:"field";s:27:"situation_familiale_divorce";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:8;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:9;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:35:"score_situation_familiale_handicap0";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:52:27";s:10:"updated_at";s:19:"2024-01-18 09:52:27";s:5:"field";s:28:"situation_familiale_handicap";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:9;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:35:"score_situation_familiale_handicap0";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:52:27";s:10:"updated_at";s:19:"2024-01-18 09:52:27";s:5:"field";s:28:"situation_familiale_handicap";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:9;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:10;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:35:"score_situation_familiale_handicap1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:52:37";s:10:"updated_at";s:19:"2024-01-18 09:52:37";s:5:"field";s:28:"situation_familiale_handicap";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:10;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:35:"score_situation_familiale_handicap1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:52:37";s:10:"updated_at";s:19:"2024-01-18 09:52:37";s:5:"field";s:28:"situation_familiale_handicap";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:10;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:11;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:35:"score_situation_familiale_handicap2";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:52:44";s:10:"updated_at";s:19:"2024-01-18 09:52:44";s:5:"field";s:28:"situation_familiale_handicap";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:11;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:35:"score_situation_familiale_handicap2";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:52:44";s:10:"updated_at";s:19:"2024-01-18 09:52:44";s:5:"field";s:28:"situation_familiale_handicap";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:11;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:12;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:27:"score_situation_frere_aucun";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:53:08";s:10:"updated_at";s:19:"2024-01-18 09:53:08";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:12;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:27:"score_situation_frere_aucun";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:53:08";s:10:"updated_at";s:19:"2024-01-18 09:53:08";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:12;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:13;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:30:"score_situation_frere_etudiant";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:53:38";s:10:"updated_at";s:19:"2024-01-18 09:53:38";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:13;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:30:"score_situation_frere_etudiant";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:53:38";s:10:"updated_at";s:19:"2024-01-18 09:53:38";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:13;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:14;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:30:"score_situation_frere_handicap";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:53:47";s:10:"updated_at";s:19:"2024-01-18 09:53:47";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:14;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:30:"score_situation_frere_handicap";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:53:47";s:10:"updated_at";s:19:"2024-01-18 09:53:47";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:14;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:15;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_frere_en_charge";s:5:"value";s:1:"3";s:10:"created_at";s:19:"2024-01-18 09:53:59";s:10:"updated_at";s:19:"2024-01-18 09:53:59";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:15;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_situation_frere_en_charge";s:5:"value";s:1:"3";s:10:"created_at";s:19:"2024-01-18 09:53:59";s:10:"updated_at";s:19:"2024-01-18 09:53:59";s:5:"field";s:15:"situation_frere";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:15;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:16;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:30:"score_situation_etudiant_aucun";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:54:26";s:10:"updated_at";s:19:"2024-01-18 09:54:26";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:16;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:30:"score_situation_etudiant_aucun";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:54:26";s:10:"updated_at";s:19:"2024-01-18 09:54:26";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:16;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:17;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_etudiant_handicap1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:54:39";s:10:"updated_at";s:19:"2024-01-18 09:54:39";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:17;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_etudiant_handicap1";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:54:39";s:10:"updated_at";s:19:"2024-01-18 09:54:39";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:17;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:18;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_etudiant_handicap2";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:54:48";s:10:"updated_at";s:19:"2024-01-18 09:54:48";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:18;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_etudiant_handicap2";s:5:"value";s:2:"10";s:10:"created_at";s:19:"2024-01-18 09:54:48";s:10:"updated_at";s:19:"2024-01-18 09:54:48";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:18;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:19;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_etudiant_handicap3";s:5:"value";s:2:"20";s:10:"created_at";s:19:"2024-01-18 09:54:57";s:10:"updated_at";s:19:"2024-01-18 09:54:57";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:19;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:34:"score_situation_etudiant_handicap3";s:5:"value";s:2:"20";s:10:"created_at";s:19:"2024-01-18 09:54:57";s:10:"updated_at";s:19:"2024-01-18 09:54:57";s:5:"field";s:18:"situation_etudiant";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:19;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:20;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_distance_inf_distance_min";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:55:19";s:10:"updated_at";s:19:"2024-01-18 09:55:19";s:5:"field";s:16:"compare_distance";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:20;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_distance_inf_distance_min";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:55:19";s:10:"updated_at";s:19:"2024-01-18 09:55:19";s:5:"field";s:16:"compare_distance";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:20;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:21;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_distance_sup_distance_min";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:55:57";s:10:"updated_at";s:19:"2024-01-18 09:55:57";s:5:"field";s:16:"compare_distance";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:21;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:31:"score_distance_sup_distance_min";s:5:"value";s:1:"5";s:10:"created_at";s:19:"2024-01-18 09:55:57";s:10:"updated_at";s:19:"2024-01-18 09:55:57";s:5:"field";s:16:"compare_distance";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:21;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:22;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:20:"score_resultat_admis";s:5:"value";s:1:"1";s:10:"created_at";s:19:"2024-01-18 09:56:28";s:10:"updated_at";s:19:"2024-01-18 09:56:28";s:5:"field";s:16:"compare_resultat";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:22;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:20:"score_resultat_admis";s:5:"value";s:1:"1";s:10:"created_at";s:19:"2024-01-18 09:56:28";s:10:"updated_at";s:19:"2024-01-18 09:56:28";s:5:"field";s:16:"compare_resultat";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:22;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:23;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:25:"score_resultat_redoublant";s:5:"value";s:1:"1";s:10:"created_at";s:19:"2024-01-18 09:57:25";s:10:"updated_at";s:19:"2024-01-18 09:57:25";s:5:"field";s:16:"compare_resultat";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:23;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:25:"score_resultat_redoublant";s:5:"value";s:1:"1";s:10:"created_at";s:19:"2024-01-18 09:57:25";s:10:"updated_at";s:19:"2024-01-18 09:57:25";s:5:"field";s:16:"compare_resultat";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:23;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:24;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:20:"score_resultat_autre";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:57:38";s:10:"updated_at";s:19:"2024-01-18 09:57:38";s:5:"field";s:16:"compare_resultat";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:24;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:20:"score_resultat_autre";s:5:"value";s:1:"0";s:10:"created_at";s:19:"2024-01-18 09:57:38";s:10:"updated_at";s:19:"2024-01-18 09:57:38";s:5:"field";s:16:"compare_resultat";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:24;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:25;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:4:"smig";s:5:"value";s:3:"400";s:10:"created_at";s:19:"2024-01-18 09:58:45";s:10:"updated_at";s:19:"2024-02-26 08:53:53";s:5:"field";s:6:"global";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:25;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:4:"smig";s:5:"value";s:3:"400";s:10:"created_at";s:19:"2024-01-18 09:58:45";s:10:"updated_at";s:19:"2024-02-26 08:53:53";s:5:"field";s:6:"global";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:25;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:26;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:12:"distance_min";s:5:"value";s:2:"50";s:10:"created_at";s:19:"2024-01-18 09:59:00";s:10:"updated_at";s:19:"2024-02-26 08:52:48";s:5:"field";s:6:"global";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:26;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:12:"distance_min";s:5:"value";s:2:"50";s:10:"created_at";s:19:"2024-01-18 09:59:00";s:10:"updated_at";s:19:"2024-02-26 08:52:48";s:5:"field";s:6:"global";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:26;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:27;s:5:"label";s:16:"accord de bourse";s:8:"label_fr";s:16:"accord de bourse";s:8:"label_ar";s:16:"accord de bourse";s:4:"code";s:1:"B";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:33";s:10:"updated_at";s:19:"2024-01-28 09:12:33";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:27;s:5:"label";s:16:"accord de bourse";s:8:"label_fr";s:16:"accord de bourse";s:8:"label_ar";s:16:"accord de bourse";s:4:"code";s:1:"B";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:33";s:10:"updated_at";s:19:"2024-01-28 09:12:33";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:27;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:28;s:5:"label";s:27:"80% 1ère tranche de bourse";s:8:"label_fr";s:27:"80% 1ère tranche de bourse";s:8:"label_ar";s:27:"80% 1ère tranche de bourse";s:4:"code";s:1:"C";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:55";s:10:"updated_at";s:19:"2024-01-28 09:12:55";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:28;s:5:"label";s:27:"80% 1ère tranche de bourse";s:8:"label_fr";s:27:"80% 1ère tranche de bourse";s:8:"label_ar";s:27:"80% 1ère tranche de bourse";s:4:"code";s:1:"C";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:55";s:10:"updated_at";s:19:"2024-01-28 09:12:55";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:28;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:29;s:5:"label";s:16:"revenu suffisant";s:8:"label_fr";s:16:"revenu suffisant";s:8:"label_ar";s:16:"revenu suffisant";s:4:"code";s:1:"K";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:11";s:10:"updated_at";s:19:"2024-01-28 09:13:11";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:29;s:5:"label";s:16:"revenu suffisant";s:8:"label_fr";s:16:"revenu suffisant";s:8:"label_ar";s:16:"revenu suffisant";s:4:"code";s:1:"K";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:11";s:10:"updated_at";s:19:"2024-01-28 09:13:11";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:29;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:30;s:5:"label";s:21:"tranche déja obtenue";s:8:"label_fr";s:21:"tranche déja obtenue";s:8:"label_ar";s:21:"tranche déja obtenue";s:4:"code";s:1:"T";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:27";s:10:"updated_at";s:19:"2024-01-28 09:13:27";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:30;s:5:"label";s:21:"tranche déja obtenue";s:8:"label_fr";s:21:"tranche déja obtenue";s:8:"label_ar";s:21:"tranche déja obtenue";s:4:"code";s:1:"T";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:27";s:10:"updated_at";s:19:"2024-01-28 09:13:27";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:30;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:31;s:5:"label";s:7:"refusé";s:8:"label_fr";s:7:"refusé";s:8:"label_ar";s:7:"refusé";s:4:"code";s:1:"R";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:37";s:10:"updated_at";s:19:"2024-01-28 09:13:37";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:31;s:5:"label";s:7:"refusé";s:8:"label_fr";s:7:"refusé";s:8:"label_ar";s:7:"refusé";s:4:"code";s:1:"R";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:37";s:10:"updated_at";s:19:"2024-01-28 09:13:37";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:31;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:32;s:5:"label";s:24:"attestation non boursier";s:8:"label_fr";s:24:"attestation non boursier";s:8:"label_ar";s:24:"attestation non boursier";s:4:"code";s:1:"X";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:54";s:10:"updated_at";s:19:"2024-01-28 09:13:54";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:32;s:5:"label";s:24:"attestation non boursier";s:8:"label_fr";s:24:"attestation non boursier";s:8:"label_ar";s:24:"attestation non boursier";s:4:"code";s:1:"X";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:54";s:10:"updated_at";s:19:"2024-01-28 09:13:54";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:32;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:33;s:5:"label";s:23:"modifiée ou rectifiée";s:8:"label_fr";s:23:"modifiée ou rectifiée";s:8:"label_ar";s:23:"modifiée ou rectifiée";s:4:"code";s:1:"M";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:02";s:10:"updated_at";s:19:"2024-01-28 09:14:02";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:33;s:5:"label";s:23:"modifiée ou rectifiée";s:8:"label_fr";s:23:"modifiée ou rectifiée";s:8:"label_ar";s:23:"modifiée ou rectifiée";s:4:"code";s:1:"M";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:02";s:10:"updated_at";s:19:"2024-01-28 09:14:02";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:33;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:34;s:5:"label";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_fr";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_ar";s:38:"payé dans autre catégorie AS ou Pret";s:4:"code";s:1:"Q";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:15";s:10:"updated_at";s:19:"2024-01-28 09:14:15";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:34;s:5:"label";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_fr";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_ar";s:38:"payé dans autre catégorie AS ou Pret";s:4:"code";s:1:"Q";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:15";s:10:"updated_at";s:19:"2024-01-28 09:14:15";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:34;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:35;s:5:"label";s:28:"faute grave arrêt de bourse";s:8:"label_fr";s:28:"faute grave arrêt de bourse";s:8:"label_ar";s:28:"faute grave arrêt de bourse";s:4:"code";s:1:"F";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:38";s:10:"updated_at";s:19:"2024-01-28 09:14:38";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:35;s:5:"label";s:28:"faute grave arrêt de bourse";s:8:"label_fr";s:28:"faute grave arrêt de bourse";s:8:"label_ar";s:28:"faute grave arrêt de bourse";s:4:"code";s:1:"F";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:38";s:10:"updated_at";s:19:"2024-01-28 09:14:38";s:5:"field";s:6:"natdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:35;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:36;s:5:"label";s:16:"accord de bourse";s:8:"label_fr";s:16:"accord de bourse";s:8:"label_ar";s:16:"accord de bourse";s:4:"code";s:13:"accord_bourse";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:33";s:10:"updated_at";s:19:"2024-01-28 09:12:33";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:36;s:5:"label";s:16:"accord de bourse";s:8:"label_fr";s:16:"accord de bourse";s:8:"label_ar";s:16:"accord de bourse";s:4:"code";s:13:"accord_bourse";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:33";s:10:"updated_at";s:19:"2024-01-28 09:12:33";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:36;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:37;s:5:"label";s:27:"80% 1ère tranche de bourse";s:8:"label_fr";s:27:"80% 1ère tranche de bourse";s:8:"label_ar";s:27:"80% 1ère tranche de bourse";s:4:"code";s:13:"80_1er_tranch";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:55";s:10:"updated_at";s:19:"2024-01-28 09:12:55";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:37;s:5:"label";s:27:"80% 1ère tranche de bourse";s:8:"label_fr";s:27:"80% 1ère tranche de bourse";s:8:"label_ar";s:27:"80% 1ère tranche de bourse";s:4:"code";s:13:"80_1er_tranch";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:12:55";s:10:"updated_at";s:19:"2024-01-28 09:12:55";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:37;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:38;s:5:"label";s:16:"revenu suffisant";s:8:"label_fr";s:16:"revenu suffisant";s:8:"label_ar";s:16:"revenu suffisant";s:4:"code";s:16:"revenu_suffisant";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:11";s:10:"updated_at";s:19:"2024-01-28 09:13:11";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:38;s:5:"label";s:16:"revenu suffisant";s:8:"label_fr";s:16:"revenu suffisant";s:8:"label_ar";s:16:"revenu suffisant";s:4:"code";s:16:"revenu_suffisant";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:11";s:10:"updated_at";s:19:"2024-01-28 09:13:11";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:38;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:39;s:5:"label";s:21:"tranche déja obtenue";s:8:"label_fr";s:21:"tranche déja obtenue";s:8:"label_ar";s:21:"tranche déja obtenue";s:4:"code";s:19:"tranche_deja_obtenu";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:27";s:10:"updated_at";s:19:"2024-01-28 09:13:27";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:39;s:5:"label";s:21:"tranche déja obtenue";s:8:"label_fr";s:21:"tranche déja obtenue";s:8:"label_ar";s:21:"tranche déja obtenue";s:4:"code";s:19:"tranche_deja_obtenu";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:27";s:10:"updated_at";s:19:"2024-01-28 09:13:27";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:39;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:40;s:5:"label";s:7:"refusé";s:8:"label_fr";s:7:"refusé";s:8:"label_ar";s:6:"رفض";s:4:"code";s:5:"refue";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:37";s:10:"updated_at";s:19:"2024-05-10 09:53:32";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:40;s:5:"label";s:7:"refusé";s:8:"label_fr";s:7:"refusé";s:8:"label_ar";s:6:"رفض";s:4:"code";s:5:"refue";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:37";s:10:"updated_at";s:19:"2024-05-10 09:53:32";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:40;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:41;s:5:"label";s:24:"attestation non boursier";s:8:"label_fr";s:24:"attestation non boursier";s:8:"label_ar";s:64:"شهادة في عدم الحصول على منحة جامعية";s:4:"code";s:24:"attestation_non_boursier";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:54";s:10:"updated_at";s:19:"2024-04-16 22:37:15";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:41;s:5:"label";s:24:"attestation non boursier";s:8:"label_fr";s:24:"attestation non boursier";s:8:"label_ar";s:64:"شهادة في عدم الحصول على منحة جامعية";s:4:"code";s:24:"attestation_non_boursier";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:13:54";s:10:"updated_at";s:19:"2024-04-16 22:37:15";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:41;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:42;s:5:"label";s:23:"modifiée ou rectifiée";s:8:"label_fr";s:23:"modifiée ou rectifiée";s:8:"label_ar";s:23:"modifiée ou rectifiée";s:4:"code";s:18:"modifier_rectifiee";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:02";s:10:"updated_at";s:19:"2024-01-28 09:14:02";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:42;s:5:"label";s:23:"modifiée ou rectifiée";s:8:"label_fr";s:23:"modifiée ou rectifiée";s:8:"label_ar";s:23:"modifiée ou rectifiée";s:4:"code";s:18:"modifier_rectifiee";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:02";s:10:"updated_at";s:19:"2024-01-28 09:14:02";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:42;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:43;s:5:"label";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_fr";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_ar";s:38:"payé dans autre catégorie AS ou Pret";s:4:"code";s:15:"paye_dans_autre";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:15";s:10:"updated_at";s:19:"2024-01-28 09:14:15";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:43;s:5:"label";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_fr";s:38:"payé dans autre catégorie AS ou Pret";s:8:"label_ar";s:38:"payé dans autre catégorie AS ou Pret";s:4:"code";s:15:"paye_dans_autre";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:15";s:10:"updated_at";s:19:"2024-01-28 09:14:15";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:43;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:44;s:5:"label";s:28:"faute grave arrêt de bourse";s:8:"label_fr";s:28:"faute grave arrêt de bourse";s:8:"label_ar";s:28:"faute grave arrêt de bourse";s:4:"code";s:24:"faute_grave_arret_bourse";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:38";s:10:"updated_at";s:19:"2024-01-28 09:14:38";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:44;s:5:"label";s:28:"faute grave arrêt de bourse";s:8:"label_fr";s:28:"faute grave arrêt de bourse";s:8:"label_ar";s:28:"faute grave arrêt de bourse";s:4:"code";s:24:"faute_grave_arret_bourse";s:5:"value";N;s:10:"created_at";s:19:"2024-01-28 09:14:38";s:10:"updated_at";s:19:"2024-01-28 09:14:38";s:5:"field";s:6:"msgdec";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:44;O:35:"App\Http\Resources\VariableResource":4:{s:8:"resource";O:19:"App\Models\Variable":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:18:"bpasBack.variables";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:45;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:17:"filigrane_demande";s:5:"value";s:12:"18-3.1.24-10";s:10:"created_at";s:19:"2024-07-04 06:28:50";s:10:"updated_at";s:19:"2024-07-04 06:28:50";s:5:"field";s:6:"global";s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:45;s:5:"label";N;s:8:"label_fr";N;s:8:"label_ar";N;s:4:"code";s:17:"filigrane_demande";s:5:"value";s:12:"18-3.1.24-10";s:10:"created_at";s:19:"2024-07-04 06:28:50";s:10:"updated_at";s:19:"2024-07-04 06:28:50";s:5:"field";s:6:"global";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:5:"label";i:1;s:8:"label_fr";i:2;s:8:"label_ar";i:3;s:4:"code";i:4;s:5:"value";i:5;s:5:"field";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:8:"collects";s:35:"App\Http\Resources\VariableResource";s:10:"collection";r:2;s:29:" * preserveAllQueryParameters";b:0;s:18:" * queryParameters";N;s:12:"preserveKeys";b:1;}