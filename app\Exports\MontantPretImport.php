<?php
namespace App\Exports;

use App\Models\MontantPret;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class MontantPretImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'code_diplome' => 'required',
                'resultat' => 'required',
                'annee_etude' => 'required',
                'montant' => 'required',
            ],
            [],
            [
                'code_diplome' => '(code_diplome)',
                'resultat' => '(resultat)',
                'annee_etude' => '(annee_etude)',
                'montant' => '(montant)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.code_diplome' => 'required|string',
            '*.resultat' => 'required|integer',
            '*.annee_etude' => 'required|integer',
            '*.montant' => 'required|integer',
        ])->validate();

        foreach ($rows as $row) {
            $gov = MontantPret::where([
                ['code_diplome', '=', $row['code_diplome']],
                ['resultat', '=', (bool)$row['resultat']],
                ['annee_etude', '=', $row['annee_etude']],
            ])->first();
            if ($gov){
                $gov->update([
                    'montant' => $row['montant'],
                ]);
            } else {
                MontantPret::create([
                    'code_diplome' => $row['code_diplome'],
                    'resultat' => $row['resultat'],
                    'annee_etude' => $row['annee_etude'],
                    'montant' => $row['montant'],
                ]);
            }
        }
    }
}
