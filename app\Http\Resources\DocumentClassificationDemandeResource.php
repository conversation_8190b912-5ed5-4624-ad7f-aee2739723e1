<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class DocumentClassificationDemandeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'document_file' => $this->document_file,
//            'document_file_url' => $this->document_file_url,
            'etat' => $this->etat,
            'dans_complement' => $this->dans_complement,
            'document_classification_id' => $this->document_classification_id,
            'document_classification' => $this->documentClassification,
            'demande_id' => $this->demande_id,
            'demande' => $this->demande,
            'created_at' => $this->created_at->format('Y-m-d'),
        ];
    }
}
