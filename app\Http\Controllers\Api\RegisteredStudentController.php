<?php

namespace App\Http\Controllers\Api;

use App\Exports\RegisteredStudentExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreRegisteredStudentRequest;
use App\Http\Requests\UpdateRegisteredStudentRequest;
use App\Http\Resources\RegisteredStudentMicroResource;
use App\Http\Resources\RegisteredStudentResource;
use App\Models\AnneeUniversitaire;
use App\Models\Etablissement;
use App\Models\EtudiantAnneeUniversitaire;
use App\Models\Resultat;
use App\Models\Universite;
use App\Models\User;
use App\Rules\ArrayAtLeastOneRequired;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class RegisteredStudentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $sort = $request->query('sort', 'asc');
        $page = $request->query('page', 0);
        $pageSize = $request->query('pageSize', 10);
        $sortColumn = $request->query('sortColumn', 'created_at');
        $annee_bac = $request->query('annee_bac', null);
        $annee_universitaire = $request->query('annee_universitaire', null);
        $etablissement_id = $request->query('etablissement_id', null);
        $university_id = $request->query('university_id', null);
        $type = $request->query('student_type', 'tunisien');


        $query =  User::with(['nationality', 'etudiantAnneeUniversitaires']);
        if ($type){
            $query->where('type', $type);
        }
        if ($annee_bac){
            $query->where('annee_bac', $annee_bac);
        }
        if ($annee_universitaire){
            $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($annee_universitaire) {
                return $query->where('annee_universitaire_id', $annee_universitaire);
            });
        }
        if ($university_id){
            if ($etablissement_id){
                $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($etablissement_id) {
                    return $query->where('code_etab', $etablissement_id);
                });
            } else {
                $etabIds = Etablissement::where('code_univ', $university_id)->pluck('code');
                $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($etabIds) {
                    $query->whereIn('code_etab', $etabIds);
                });
            }
        }
        else {
            if ($etablissement_id){
                $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($etablissement_id) {
                    return $query->where('code_etab', $etablissement_id);
                });
            }
        }
        if ($request->q != ""){
            $query->when($request->q, function($q)use($request){
                $q->where('name', 'like', '%'.$request->q.'%')
                    ->orWhere('email', 'like', '%'.$request->q.'%')
                    ->orWhere('name_ar', 'like', '%'.$request->q.'%')
                    ->orWhere('firstName', 'like', '%'.$request->q.'%')
                    ->orWhere('cin', 'like', '%'.$request->q.'%')
                    ->orWhere('num_bac', 'like', '%'.$request->q.'%')
                    ->orWhere('matricule', 'like', '%'.$request->q.'%')
                    ->orWhere('num_passport', 'like', '%'.$request->q.'%');
            });
        }


        $recordsTotal = $query->count();

        $query->sortable([$sortColumn => $sort])->paginate($pageSize, ['*'], 'page', $page);


        $internationalStudent = $query->get();

        return response()->json([
            "params"=> $request->all(),
            "allData"=> [],
            "data"=> RegisteredStudentMicroResource::collection($internationalStudent),
            "total"=> $recordsTotal
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @param User $registered_student
     * @return RegisteredStudentResource
     */
    public function show( User $registered_student): RegisteredStudentResource
    {
        return new RegisteredStudentResource($registered_student->load([
            'etudiantAnneeUniversitaires',
            'etudiantAnneeUniversitaires.etablissement',
            'etudiantAnneeUniversitaires.resultat',
            'etudiantAnneeUniversitaires.etablissement.universite',
            'country',
            'gouvernorat',
//            'historiqueDecisions'
        ]));
    }

    /**
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function edit(Request $request, int $id): JsonResponse
    {
        $student = User::find($id);
        $secondConnection = config('database.secondConnection');
//        dd($student);
        //    branch tunisien
        if ($request->type === 'tunisien') {
            $validator = Validator::make($request->all(), [
                'cin' => 'required|digits:8|unique:'.$secondConnection.'.users,cin,'.$student->id,
                'num_bac' => 'required|digits:6',
                'email' => 'required|email',
                'name' => 'required|max:50',
                'name_ar' => 'sometimes|nullable|max:50',
                'code_postal' => 'required',
                'nationality_id' => 'required',
                'code_gouv' => 'required',
                'email_perso' => 'nullable|email',
                'phoneNumber' => 'required|digits:8',
                'phoneNumber2' => 'nullable|min:8',
                'address' => 'required',
                'date_naissance' => 'nullable',
                'pere' => 'nullable',
                'mere' => 'nullable',
            ]);
        }
        //    branch etranger
        else {
            $validator = Validator::make($request->all(), [
                'num_passport' => 'required',
                'matricule' => 'required|max:8|min:8|unique:'.$secondConnection.'.users,matricule,'.$student->id,
                'email' => 'required|email',
                'name' => 'required|max:40',
                'firstName' => 'sometimes|nullable|max:40',
                'code_postal' => 'required',
                'nationality_id' => 'required',
                'code_gouv' => 'sometimes|nullable',
                'email_perso' => 'nullable|email',
                'phoneNumber' => 'required|digits:8',
                'phoneNumber2' => 'nullable|min:8',
                'address' => 'required',
                'date_naissance' => 'nullable',
                'pere' => 'nullable',
                'mere' => 'nullable',
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        if ($request->type === 'tunisien') {
            $student->update([
                'cin' => $request->cin,
                'num_bac' => $request->num_bac,
                'annee_bac' => (int)$request->annee_bac,
                'name' => $request->name,
                'name_ar' => $request->name_ar,
                'email'=>$request->email,
                'username' => $request->email,
                'sex'=>$request->sex,
                'date_naissance' =>  date('Y-m-d', strtotime($request->date_naissance)),
                'pere'=>$request->pere,
                'mere'=>$request->mere,
                'country_id'=>(int)$request->nationality_id,
                'nationality_id'=>(int)$request->nationality_id,
                'phoneNumber'=>$request->phoneNumber,
                'phoneNumber2'=>$request->phoneNumber2,
                'address'=>$request->address,
                'code_postal'=>$request->code_postal,
                'code_gouv'=>$request->code_gouv,
                'email_perso'=>$request->email_perso,
            ]);
        } else {
            $student->update([
                'num_passport' => $request->num_passport,
                'matricule' => $request->matricule,
                'annee_bac' => (int)$request->annee_bac,
                'firstName' => $request->firstName,
                'name' => $request->name,
                'email'=>$request->email,
                'username' => $request->email,
                'sex'=>$request->sex,
                'date_naissance' =>  date('Y-m-d', strtotime($request->date_naissance)),
                'pere'=>$request->pere,
                'mere'=>$request->mere,
                'country_id'=>(int)$request->nationality_id,
                'nationality_id'=>(int)$request->nationality_id,
                'phoneNumber'=>$request->phoneNumber,
                'phoneNumber2'=>$request->phoneNumber2,
                'address'=>$request->address,
                'code_postal'=>$request->code_postal,
                'code_gouv'=>$request->code_gouv,
                'email_perso'=>$request->email_perso,
            ]);
        }



        return response()->json([
            'message'=>trans('Profile successfully updated')
        ],200);


    }



    public function update_annee_etude(Request $request, int $id): JsonResponse
    {

        $user = User::find($id);

        $isCreditIds = Resultat::where('is_credit', 1)->pluck('id')->toArray();
        $isCreditIds = implode(',', $isCreditIds);
        $isSuccessIds = Resultat::where('success', 1)->pluck('id')->toArray();
        $isSuccessIds = implode(',', $isSuccessIds);
        $currentAnneeId = AnneeUniversitaire::where('start', '<=', Carbon::today())->where('end', '>=', Carbon::today())->first()?->id;
        $validator = Validator::make($request->all(), [
            'annee_bac' => 'required',
            'study_years' => ['array', new ArrayAtLeastOneRequired ],
            'study_years.*.filiere_id' => 'nullable',
            'study_years.*.code_etab' => 'required',
            'study_years.*.annee_etude' => 'required',
            'study_years.*.code_diplome' => 'required',
            'study_years.*.resultat_id' => [
                'exclude_if:study_years.*.annee_universitaire_id,' . $currentAnneeId,
                'sometimes',
                'required',
            ],
            'study_years.*.moyenne' => [
                'exclude_if:study_years.*.annee_universitaire_id,' . $currentAnneeId,
                'sometimes',
                'nullable',
                'numeric',
                'min:0',
                'max:20.00',
                'required_if:study_years.*.resultat_id,' . $isSuccessIds
            ],
            'study_years.*.credit' => [
                'exclude_if:study_years.*.annee_universitaire_id,' . $currentAnneeId,
                'sometimes',
                'nullable',
                'numeric',
                'min:0',
                'max:60.00',
                'required_if:study_years.*.resultat_id,' . $isCreditIds
            ],
        ]);


        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }
        if ($request->study_years && count($request->study_years) && $request->current_annee_universitaire_id ){
//            $previousAnneeUniversitaireId = AnneeUniversitaire::latest('start')->get()->get(1)?->id;
            $currentAnneeUniversitaire = AnneeUniversitaire::find($request->current_annee_universitaire_id);
            $isNewBachlor = $currentAnneeUniversitaire->annee_bac == $request->annee_bac;
            $currentYearExist = false;
            $previousYearExist = false;
            $ids = [];
            foreach ($request->study_years as $study_year) {
                if ($study_year['annee_universitaire_id'] == $request->current_annee_universitaire_id){
                    $currentYearExist = true;
                }
//                if ($study_year['annee_universitaire_id'] == $previousAnneeUniversitaireId){
                if ($study_year['annee_universitaire_id'] !== $request->current_annee_universitaire_id){
                    $previousYearExist = true;
                }
                $ids[] = $study_year['id'];
            }
            if ($currentYearExist) {
                if ($isNewBachlor || $previousYearExist) {
                    foreach ($user->etudiantAnneeUniversitaires as $etudiantAnneeUniversitaire) {
                        if (!in_array($etudiantAnneeUniversitaire->id, $ids)) {
                            $etudiantAnneeUniversitaire->delete();
                        }
                    }
                    foreach ($request->study_years as $study_year) {

                        $annUniversitaire = AnneeUniversitaire::find($study_year['annee_universitaire_id']);
                        $isAfterBachlor = $annUniversitaire->annee_bac == $request->annee_bac;
                        $etudiantAnneeUniversitaire = EtudiantAnneeUniversitaire::find($study_year['id']);
                        if ($etudiantAnneeUniversitaire) {
                            $etudiantAnneeUniversitaire->update([
                                'annee_universitaire_id' => $study_year['annee_universitaire_id'],
                                'filiere_id' => $study_year['filiere_id'],
                                'code_etab' => $study_year['code_etab'],
                                'code_diplome' => $study_year['code_diplome'],
                                'annee_etude' => $study_year['annee_etude'],
                                'resultat_id' => $study_year['resultat_id'],
                                'moyenne' => $study_year['moyenne'],
                                'credit' => $study_year['credit'],
                                'is_nouveau_bachelier' => $isAfterBachlor,
                            ]);
                        } else {
                            EtudiantAnneeUniversitaire::create([
                                'annee_universitaire_id' => $study_year['annee_universitaire_id'],
                                'filiere_id' => $study_year['filiere_id'],
                                'code_etab' => $study_year['code_etab'],
                                'code_diplome' => $study_year['code_diplome'],
                                'annee_etude' => $study_year['annee_etude'],
                                'user_id' => $user->id,
                                'resultat_id' => $study_year['resultat_id'],
                                'moyenne' => $study_year['moyenne'],
                                'credit' => $study_year['credit'],
                                'is_nouveau_bachelier' => $isAfterBachlor,
                            ]);
                        }
                    }

                    return response()->json([
                        'message'=>trans('Profile successfully updated')
                    ],200);
                }
                return response()->json([
                    'message'=>'Validations fails',
                    'errors'=>[
                        'current_annee_universitaire_exist'=>[
                            trans('validation.array_previous_years_selected')
                        ]
                    ]
                ],422);
            }

            return response()->json([
                'message'=>'Validations fails',
                'errors'=>[
                    'current_annee_universitaire_exist'=>[
                        trans('validation.array_current_years_selected')
                    ]
                ]
            ],422);

        }

        return response()->json([
            'message'=>'Validations fails',
            'errors'=>[
                'current_annee_universitaire_exist'=>[
                    trans('validation.array_at_least_one_years_selected')
                ]
            ]
        ],422);



    }
    /**
     * Store a newly created resource in storage.
     *
     * @param StoreRegisteredStudentRequest $request
     * @return Response
     */
    public function store(StoreRegisteredStudentRequest $request): Response
    {
        $data = $request->validated();
        $r = User::create($data);

        return response(new RegisteredStudentResource($r) , 201);
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $students = User::find($id);
        $students->attestations()->delete();
        $students->reclamations()->delete();
        $students->retraitInscriptions()->delete();
        foreach ($students->demandes as $demande) {
            $demande->demandeDocumentsClassifications()->delete();
            $demande->demandeAnneeEtudes()->delete();
            $demande->delete();
        }
        $students->etudiantAnneeUniversitaires()->delete();
        $students->delete();
        return response()->json([
            "result"=> "success"
        ],200);
    }

    public function exportExcel(Request $request): BinaryFileResponse
    {
        $export = new RegisteredStudentExport($request->annee_bac, $request->type);
        return Excel::download($export, 'etudiants.xlsx');
    }

    /**
     * Activate student account
     *
     * @param int $registered_student
     * @return JsonResponse
     */
    public function activateAccount(int $registered_student): JsonResponse
    {
        $student = User::find($registered_student);

        if (!$student) {
            return response()->json([
                'message' => 'Student not found'
            ], 404);
        }

        // Update status to active
        $student->update([
            'status' => true
        ]);

        return response()->json([
            'message' => trans('Account successfully activated')
        ], 200);
    }

    /**
     * Deactivate student account and revoke all tokens
     *
     * @param int $registered_student
     * @return JsonResponse
     */
    public function deactivateAccount(int $registered_student): JsonResponse
    {
        $student = User::find($registered_student);

        if (!$student) {
            return response()->json([
                'message' => 'Student not found'
            ], 404);
        }

        // Update status to inactive
        $student->update([
            'status' => false
        ]);

        return response()->json([
            'message' => trans('Account successfully deactivated')
        ], 200);
    }
}
