<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreOfficeRequest;
use App\Http\Requests\UpdateOfficeRequest;
use App\Http\Resources\OfficeResource;
use App\Models\Office;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class OfficeController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Office', $this->cache_seconds, function () {
            return OfficeResource::collection(Office::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreOfficeRequest $request
     * @return Response
     */
    public function store(StoreOfficeRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Office');
        Helpers::clearCacheIdp();

        $g = Office::create($data);

        return response(new OfficeResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Office $office
     * @return OfficeResource
     */
    #[Pure] public function show(Office $office): OfficeResource
    {
        return new OfficeResource($office);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateOfficeRequest $request
     * @param Office $office
     * @return OfficeResource
     */
    public function edit(UpdateOfficeRequest $request, Office $office): OfficeResource
    {
        $data = $request->validated();

        Cache::forget('Office');
        Helpers::clearCacheIdp();

        $office->update($data);

        return new OfficeResource($office);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Office $office
     * @return Response
     */
    public function destroy(Office $office): Response
    {
//        if($office->delegations->count()){
//            throw ValidationException::withMessages(["Cet office est utilisé par d'autres tables"]);
//        }

        Cache::forget('Office');
        Helpers::clearCacheIdp();

        $office->delete();

        return response("", 204);
    }
}
