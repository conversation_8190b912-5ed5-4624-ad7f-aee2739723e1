<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attestations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('attestation_types_id');
            $table->unsignedBigInteger('student_id');
            $table->text('detail')->nullable();
            $table->string('status')->nullable();
            //$table->text('response')->nullable();
            //$table->string('etat')->nullable();
            //$table->string('document_file')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('attestation_types_id')
                ->references('id')
                ->on('attestation_types');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attestations');
    }
};
