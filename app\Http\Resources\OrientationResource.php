<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class OrientationResource extends JsonResource
{
    public static $wrap = false;
    public bool $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code_filiere' => $this->code_filiere,
            'filiere' => $this->filiere,
            'student_id' => $this->student_id,
            'tour' => $this->tour,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
