<?php

namespace App\Jobs;

use App\Mail\ConfirmedStudentMail;
use App\Models\Admin;
use App\Models\Demande;
use App\Models\EtudiantAnneeUniversitaire;
use App\Models\InternationalStudent;
use App\Models\StudentFromMes;
use App\Models\User;
use App\Models\WaitingUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ProcessCreatePreparation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private int $prepId;
    private array $demandesIds;
    private string $type_preparation;
    private Admin $user;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct( $prepId,$demandesIds,$type_preparation,Admin $user)
    {
        $this->prepId = $prepId;
        $this->demandesIds = $demandesIds;
        $this->type_preparation = $type_preparation;
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
//        $demandes = Demande::whereIn('id', $this->demandesIds)->get();
        if ($this->type_preparation === 'bourse') {
            Demande::whereIn('id', $this->demandesIds)->chunk(500, function (Collection $demandes) {
                foreach ($demandes as $demande) {
                    $demande->preparation_bourse_id = $this->prepId;
                    $demande->etat_bourse = Demande::ETAT_BOURSE['SUIVI_PAIEMENT'];
                    $demande->save();
                }
            });

        } elseif ($this->type_preparation === 'insertion') {
            Demande::whereIn('id', $this->demandesIds)->chunk(500, function (Collection $demandes) {
                foreach ($demandes as $demande) {
                    $demande->preparation_insertion_id = $this->prepId;
                    $demande->etat_bourse_insertion = Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'];
                    $demande->save();
                }
            });

        } elseif ($this->type_preparation === 'pret') {
            Demande::whereIn('id', $this->demandesIds)->chunk(500, function (Collection $demandes) {
                foreach ($demandes as $demande) {
                    $demande->preparation_pret_id = $this->prepId;
                    $demande->etat_pret = Demande::ETAT_PRET['SUIVI_PAIEMENT'];
                    $demande->save();
                }
            });

        } elseif ($this->type_preparation === 'aide_sociale') {
            Demande::whereIn('id', $this->demandesIds)->chunk(500, function (Collection $demandes) {
                foreach ($demandes as $demande) {
                    $demande->preparation_aide_sociale_id = $this->prepId;
                    $demande->etat_aide_sociale = Demande::ETAT_AIDE_SOCIALE['SUIVI_PAIEMENT'];
                    $demande->save();
                }
            });

        } elseif ($this->type_preparation === 'stage') {
            Demande::whereIn('id', $this->demandesIds)->chunk(500, function (Collection $demandes) {
                foreach ($demandes as $demande) {
                    $demande->preparation_stage_id = $this->prepId;
                    $demande->etat_bourse_stage = Demande::ETAT_BOURSE_STAGE['SUIVI_PAIEMENT'];
                    $demande->save();
                }
            });
        }
        dispatch(new NotifyUserOfCompletedCreatePreparation($this->user, $this->prepId));
    }
}
