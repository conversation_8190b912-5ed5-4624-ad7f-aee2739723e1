<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReclamationTypeResource;
use App\Models\ReclamationType;
use App\Http\Requests\StoreReclamationTypeRequest;
use App\Http\Requests\UpdateReclamationTypeRequest;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class ReclamationTypeController extends Controller
{
    protected $cache_seconds = 900;

    public function __construct()
    {
//        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('ReclamationType', $this->cache_seconds, function () {
            return ReclamationTypeResource::collection(
                ReclamationType::query()->with('parent')->get()
            );
        });

    }


    /**
     * Store a newly created resource in storage.
     *
     * @param StoreReclamationTypeRequest $request
     * @return Response
     */
    public function store(StoreReclamationTypeRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('ReclamationType');
        Helpers::clearCacheIdp(['ReclamationType']);

        $reclamationType = ReclamationType::create($data);

        return response(new ReclamationTypeResource($reclamationType) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateReclamationTypeRequest $request
     * @param ReclamationType $reclamationType
     * @return Response
     */
    public function edit(UpdateReclamationTypeRequest $request,ReclamationType $reclamationType): Response
    {
        $data = $request->validated();

        Cache::forget('ReclamationType');
        Helpers::clearCacheIdp(['ReclamationType']);

        $reclamationType->update($data);

        return response(new ReclamationTypeResource($reclamationType) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ReclamationType $reclamation_type
     * @return Response
     */
    public function destroy(ReclamationType $reclamation_type)
    {
        Cache::forget('ReclamationType');
        Helpers::clearCacheIdp(['ReclamationType']);

        $reclamation_type->delete();
        return response("ok", 204);
    }
}
