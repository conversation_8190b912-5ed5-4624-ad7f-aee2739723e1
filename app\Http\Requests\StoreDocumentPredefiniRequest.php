<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDocumentPredefiniRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string|unique:document_predefinis,code',
            'title' => 'required|string',
            'title_fr' => 'required|string',
            'title_ar' => 'required|string',
            'boursier' => 'nullable|string|sometimes',
            'resultat' => 'nullable|integer|sometimes',
        ];
    }
}
