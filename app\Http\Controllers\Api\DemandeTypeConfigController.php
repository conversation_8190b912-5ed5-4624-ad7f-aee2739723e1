<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDemandeTypeRequest;
use App\Http\Requests\UpdateDemandeTypeRequest;
use App\Http\Resources\DemandeTypeMicroResource;
use App\Http\Resources\DemandeTypeResource;
use App\Models\ConfigDemandeType;
use App\Models\DemandeType;
use App\Models\Diplome;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class DemandeTypeConfigController extends Controller
{
    function __construct()
    {
//        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $type = null;
        if ($request->demande_type_id){
            $type = DemandeType::find($request->demande_type_id);
        }
        $demandeTypes = DemandeType::query()->with(['parent', 'diplomesDemandeTypes','configs'])->withCount('fils');
        if ($type) {
            $dfs = DemandeType::query()->has('fils')->where('parent_id', $type->id)->pluck('id');
            $dfss = DemandeType::query()->has('fils')->whereIn('parent_id', $dfs)->pluck('id');
            $dfsss = DemandeType::query()->has('fils')->whereIn('parent_id', $dfss)->pluck('id');


            $demandeTypes = $demandeTypes
                ->orWhereIn('parent_id', array_merge($dfs->toArray(), $dfss->toArray(), $dfsss->toArray(),[$type->id]))
                ->orWhere('id', $type->id);
        }
        return response()->json([
            "data"=> DemandeTypeMicroResource::collection($demandeTypes->get()),
        ],200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDemandeTypeRequest $request
     * @return Response
     */
    public function store(StoreDemandeTypeRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('DemandeTypeArbreFils');
        Cache::forget('DemandeTypeTree');
        Helpers::clearCacheIdp();

        $demandeType = DemandeType::create($data);

        try {
            //code...
            $demandeType->syncPivotAndLog($data, [
                'diplomes' => ['display' => 'name'],
            ]);


        } catch (\Throwable $th) {
//            Log::error("Auditing error occured");
//            Log::error($th->getMessage());
            $demandeType->diplomes()->sync($data['diplomes']);

        }

        return response(new DemandeTypeResource($demandeType) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $demandeType = DemandeType::where('id',$id)->with(['parent', 'diplomes'])->first();
        return response()->json( $demandeType );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateDemandeTypeRequest $request
     * @param DemandeType $demandeType
     * @return Response
     */
    public function edit(UpdateDemandeTypeRequest $request, DemandeType $demandeType): Response
    {
        $data = $request->validated();

        Cache::forget('DemandeTypeArbreFils');
        Cache::forget('DemandeTypeTree');
        Helpers::clearCacheIdp();

        if ($request->has('diplomes')){
            try {
                //code...
                $other_diplomes = $data['diplomes'];
                $diplomes = Diplome::whereIn('code', $data['diplomes'])->get()->toArray();
                $data['diplomes'] = $diplomes;

                $demandeType->syncPivotAndLog($data, [
                    'diplomes' => ['display' => 'code'],
                ], 'code');


            } catch (\Throwable $th) {
//                Log::error("Auditing error occured");
//                Log::error($th->getMessage());
                $data['diplomes'] = $other_diplomes;
                $demandeType->diplomes()->sync($data['diplomes']);

            }
        }

        $demandeType->update($data);
        if ($request->has('config') or $request->has('logic')) {
            ConfigDemandeType::create(
                ['config' => $request->config , 'logic' => $request->logic, 'demande_type_id' => $demandeType->id]
            );
        }

        return response(new DemandeTypeResource($demandeType) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param DemandeType $demandeType
     * @return Response
     */
    public function destroy(DemandeType $demandeType): Response
    {
        if(
            $demandeType->diplomes->count() or
            $demandeType->demandes->count() or
            $demandeType->configs->count() or
            $demandeType->classifications->count() or
            $demandeType->fils->count()
        ){
            throw ValidationException::withMessages(["Cette demande type est utilisé par d'autres tables"]);
        }

        Cache::forget('DemandeTypeArbreFils');
        Cache::forget('DemandeTypeTree');
        Helpers::clearCacheIdp();

        $demandeType->diplomes()->detach();
        $demandeType->delete();

        return response("", 204);
    }
}
