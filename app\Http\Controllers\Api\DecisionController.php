<?php

namespace App\Http\Controllers\Api;

use App\Exports\ExportDecision;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreHistoriqueRequest;
use App\Http\Requests\UpdateDecisionRequest;
use App\Models\Decision;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use Maatwebsite\Excel\Facades\Excel;

use SplFileObject;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DecisionController extends Controller
{
    public function index(Request $request)
    {
        $historique =  Decision::when(
            $request->has('cin'),
            function ($query) use ($request) {
                return $query->where('cin',  'like', '%' . $request->cin . '%');
            }
        )
            ->when(
                $request->has('nom'),
                function ($query) use ($request) {
                    return $query->where('nom',  'like', '%' . $request->nom . '%');
                }
            )
            ->when(
                $request->has('type'),
                function ($query) use ($request) {
                    return $query->where('type',  'like', '%' . $request->type . '%');
                }
            )
            ->when(
                $request->annee_id,
                function ($query) use ($request) {
                    return $query->where('annee_id',  $request->annee_id);
                }
            )
            ->when(
                $request->university,
                function ($query) use ($request) {
                    return $query->where('univ',  $request->university);
                }
            )
            ->when(
                $request->etablissement,
                function ($query) use ($request) {
                    return $query->where('fac',  $request->etablissement);
                }
            )
            ->when(
                $request->q != "",
                function ($query) use ($request) {
                    if ($request->q == "Invalid date") {
                        $query->whereRaw(
                            '( SUBSTRING(datnais, 1, 2) NOT BETWEEN ? AND ? OR SUBSTRING(datnais, 3, 2) NOT BETWEEN ? AND ? OR LENGTH(datnais) != ? OR STR_TO_DATE(datnais, ?) IS NULL )',
                            ['01', '31', '01', '12', 6, '%d%m%y']
                        );
                    } else {
                        $query->where('cin', 'like', '%' . $request->q . '%')
                            ->orWhere('type', 'like', '%' . $request->q . '%')
                            ->orWhere('ndec', 'like', '%' . $request->q . '%')
                            ->orWhere('fac', 'like', '%' . $request->q . '%')
                            ->orWhere('nom', 'like', '%' . $request->q . '%')
                            ->orWhere('datnais', 'like', '%' . $request->q . '%')
                            ->orWhere('discip', 'like', '%' . $request->q . '%');
                    }
                }
            )
            ->orderBy('id', 'desc')
            ->with('etablissement')
            ->with('annee_universitaire')
            ->select([
                'id', 'cin', 'catb', 'lot', 'nom', 'datnais', 'gouv', 'sexe', 'profp', 'anet', 'discip',
                'fac', 'univ', 'inf', 'sup', 'enf', 'revp', 'revm', 'avis', 'res', 'moy', 'natdec',
                'situa', 'mbs', 'nmb', 'mf', 'ndec', 'dat', 'montanttotal', 'pourcentage', 'office', 'type', 'annee_id'
            ])
            ->paginate(
                $request->input('perPage') ?? config('constants.pagination'),
            );

        return response()->json($historique, 200);
    }

    public function historiqueEtudiant($cin)
    {
        $historique =  Decision::where('cin', $cin)
            ->with('etablissement')
            ->with('annee_universitaire')
            ->join('annee_universitaires', 'annee_universitaires.id', '=', 'historiques.annee_id')
            ->orderBy('annee_universitaires.start', 'desc')
            ->get();
        return response()->json($historique, 200);
    }

    public function uploadHistoriqueFile(StoreHistoriqueRequest $request)
    {

        $data = $request->validated();

        $filePath = $request->file('file')->getRealPath();

        // Créer une instance de SplFileObject
        $file = new SplFileObject($filePath);

        // Définir le mode de lecture comme CSV
        $file->setFlags(SplFileObject::READ_CSV);

        // Récupérer la première ligne du fichier CSV
        $firstRow = $file->fgetcsv();

        $enteteType = [
            'cin', 'catb', 'lot', 'nom', 'datnais', 'gouv', 'sexe', 'profp', 'anet', 'discip',
            'fac', 'univ', 'inf', 'sup', 'enf', 'revp', 'revm', 'avis', 'res', 'moy', 'natdec',
            'situa', 'mbs', 'nmb', 'mf', 'ndec', 'dat', 'montanttotal', 'pourcentage', 'office'
        ];

        $firstRowLower = array_map('strtolower', $firstRow);

        // Vérifier si les deux tableaux sont égaux
        if ($firstRowLower === $enteteType) {
            // $csvFilePath = str_replace('\\', '/', $csvFilePath);

            try {

                $file = $request->file('file');

                $fileName = time().$file->getClientOriginalName();

//                $file->move(storage_path('uploads/historique_decisions'), $fileName);
//
                $path = $file->storeAs(
                    'uploads/historique_decisions', time().$file->getClientOriginalName()
                );
                $filePath = str_replace('\\', '/', storage_path('app') . '/' . $path);

                DB::table('historiques')
                ->where('annee_id', $request->input('annee_universitaire_id'))
                ->where('type', $request->input('type'))
                ->delete();

                DB::statement("
                LOAD DATA LOCAL INFILE '$filePath'
                INTO TABLE historiques
                FIELDS TERMINATED BY ','
                LINES TERMINATED BY '\\n'
                IGNORE 1 ROWS
                (@CIN, @CATB, @LOT, @NOM, @DATNAIS, @GOUV, @SEXE, @PROFP, @ANET, @DISCIP, @FAC, @UNIV, @INF, @SUP, @ENF, @REVP, @REVM, @AVIS, @RES, @MOY, @NATDEC, @SITUA, @MBS, @NMB, @MF, @NDEC, @DAT, @MontantTotal, @POURCENTAGE, @office)
                SET
                    cin = @CIN,
                    catb = @CATB,
                    lot = @LOT,
                    nom = @NOM,
                    datnais = CASE WHEN LENGTH(@DATNAIS) < 5 THEN NULL
                                   WHEN @DATNAIS IS NOT NULL THEN LPAD(@DATNAIS, 6, '0')
                                   ELSE NULL END,
                    gouv = @GOUV,
                    sexe = @SEXE,
                    profp = @PROFP,
                    anet = @ANET,
                    discip = @DISCIP,
                    fac = @FAC,
                    univ = @UNIV,
                    inf = @INF,
                    sup = @SUP,
                    enf = @ENF,
                    revp = @REVP,
                    revm = @REVM,
                    avis = @AVIS,
                    res = @RES,
                    moy = @MOY,
                    natdec = @NATDEC,
                    situa = @SITUA,
                    mbs = @MBS,
                    nmb = @NMB,
                    mf = @MF,
                    ndec = @NDEC,
                    dat = @DAT,
                    montant = @MontantTotal,
                    montanttotal = (cast(@MontantTotal AS DOUBLE) + cast(@MF AS DOUBLE)),
                    pourcentage = @POURCENTAGE,
                    office = @office,
                    type = '{$request->input('type')}',
                    annee_id = '{$request->input('annee_universitaire_id')}'
            ");

//                unlink($filePath);

            } catch (\Exception $e) {
                return response()->json([
                    'message' => "Le fichier importé n'est pas valide",
                    'errors' => $e
                ], 500);
            }

            return response()->json("success", 200);
        } else {
            return response()->json([
                'message' => "L'entête du fichier n'est pas valide",
                'errors' => [
                    "file" => ["L'entête du fichier n'est pas valide"],
                    "firstRowLower" => $firstRowLower,
                ]
            ], 422);
        }

        return response()->json('error', 500);
    }

    public function getDistinctNDEC(Request $request)
    {
        $distinctNdecValues = Decision::whereRaw('UPPER(type) = UPPER(?)', [$request->type])
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_id',  $request->annee_universitaire_id);
                }
            )->distinct('ndec')->pluck('ndec');
        return response()->json($distinctNdecValues, 200);
    }

    public function getDistinctNDECGroupedByAnnee()
    {
        $distinctNdecValues = Decision::select(['annee_id','ndec'])->groupBy('annee_id')->groupBy('ndec')->get()->makeHidden('cin_prefix');
        return response()->json($distinctNdecValues, 200);
    }

    private function convertXlsxToCsv($xlsxFilePath)
    {
        try {
            $reader = new Xlsx();
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($xlsxFilePath);

            $csvFilePath = storage_path('uploads') . '/' . uniqid('', true) . '_converted.csv';

            $writer = (new Csv($spreadsheet))
                ->setEnclosure('')
                ->setLineEnding("\n")
                ->setDelimiter(',');
            $writer->setSheetIndex(0);
            $writer->save($csvFilePath);

            return $csvFilePath;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function exportExcel(Request $request): BinaryFileResponse
    {
        $export = new ExportDecision($request);
        return Excel::download($export, 'etudiants.xlsx');
    }

    public function edit(UpdateDecisionRequest $request, Decision $decision)
    {
        $data = $request->validated();

        $decision->update($data);

        return response()->json('success', 200);
    }
}
