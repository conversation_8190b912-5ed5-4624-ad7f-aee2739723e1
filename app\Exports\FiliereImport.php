<?php
namespace App\Exports;

use App\Models\Filiere;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class FiliereImport implements ToCollection, WithHeadingRow
{

    private int $annee_universitaire;

    public function __construct(int $annee_universitaire)
    {
        $this->annee_universitaire = $annee_universitaire;
    }



    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'lib_filiere_fr' => 'required',
                'lib_filiere_ar' => 'required',
                'code_etab' => 'required',
                'code_filiere' => 'required',
                'code_diplome' => 'required',
            ],
            [],
            [
                'lib_filiere_fr' => '(lib_filiere_fr)',
                'lib_filiere_ar' => '(lib_filiere_ar)',
                'code_etab' => '(code_etab)',
                'code_filiere' => '(code_filiere)',
                'code_diplome' => '(code_diplome)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.lib_filiere_fr' => 'required|string',
            '*.lib_filiere_ar' => 'required|string',
            '*.code_etab' => 'required|string',
            '*.code_filiere' => 'required|numeric',
            '*.code_diplome' => 'required|string',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Filiere::where('code', $row['code_filiere'])
                ->where('annee_universitaire', $this->annee_universitaire)
                ->first();
            if ($gov){
                $gov->update([
                    'name' => $row['lib_filiere_fr'],
                    'name_fr' => $row['lib_filiere_fr'],
                    'name_ar' => $row['lib_filiere_ar'],
                    'code_etab' => $row['code_etab'],
                    'code_diplome' => $row['code_diplome'],
                ]);
            } else {
                Filiere::create([
                    'name' => $row['lib_filiere_fr'],
                    'name_fr' => $row['lib_filiere_fr'],
                    'name_ar' => $row['lib_filiere_ar'],
                    'code' => $row['code_filiere'],
                    'code_etab' => $row['code_etab'],
                    'code_diplome' => $row['code_diplome'],
                    'annee_universitaire' => $this->annee_universitaire,
                ]);
            }
        }
    }
}
