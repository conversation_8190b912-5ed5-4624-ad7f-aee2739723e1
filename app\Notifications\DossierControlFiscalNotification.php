<?php

namespace App\Notifications;

use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class DossierControlFiscalNotification extends Notification
{
    use Queueable;

    private Demande $demande;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande)
    {
        $this->demande = $demande;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Folder on tax audit",
            "subtitle" => "Folder on tax audit for demand n°: ". $this->demande->code,
            "title_fr" => "Dossier en control fiscal",
            "subtitle_fr" => "Dossier en control fiscal pour la demande n°: " . $this->demande->code,
            "title_ar" => "الملف تحت المراقبة الضريبية",
            "subtitle_ar" => " الملف تحت المراقبة الضريبية للطلب : " . $this->demande->code,
            "avatarIcon" => "alert-octagon",
            "avatarAlt" => "Dossier",
            "avatarText" => "Dossier",
            "avatarColor" => "warning",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
