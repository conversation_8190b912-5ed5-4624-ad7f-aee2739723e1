<?php

use App\Http\Controllers\AbsenceController;
use App\Http\Controllers\Api\AnneeBacController;
use App\Http\Controllers\Api\AnneeUniversitaireController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CatbController;
use App\Http\Controllers\Api\ChampPredefiniController;
use App\Http\Controllers\Api\ClassificationConfigController;
use App\Http\Controllers\Api\ClassificationController;
use App\Http\Controllers\Api\CountryController;
use App\Http\Controllers\Api\DemandeController;
use App\Http\Controllers\Api\DemandeTypeConfigController;
use App\Http\Controllers\Api\DemandeTypeController;
use App\Http\Controllers\Api\DiplomeController;
use App\Http\Controllers\Api\DocumentClassificationController;
use App\Http\Controllers\Api\DocumentDemandeController;
use App\Http\Controllers\Api\DocumentPredefiniController;
use App\Http\Controllers\Api\EtablissementController;
use App\Http\Controllers\Api\ExportedFileController;
use App\Http\Controllers\Api\FiliereController;
use App\Http\Controllers\Api\GouvernoratController;
use App\Http\Controllers\Api\InternationalStudentController;
use App\Http\Controllers\Api\LyceeController;
use App\Http\Controllers\Api\MessagePredefiniController;
use App\Http\Controllers\Api\MontantPretController;
use App\Http\Controllers\Api\OfficeController;
use App\Http\Controllers\Api\PreparationController;
use App\Http\Controllers\Api\ProfessionController;
use App\Http\Controllers\Api\ProfessionTypeController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\ReclamationController;
use App\Http\Controllers\Api\ReclamationTypeController;
use App\Http\Controllers\Api\RegisteredStudentController;
use App\Http\Controllers\Api\ResultatController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\StudentFromMesController;
use App\Http\Controllers\Api\UniversiteController;
use App\Http\Controllers\Api\UploadedFileController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\DecisionsFilesController;
use App\Http\Controllers\Api\DecisionController;
use App\Http\Controllers\Api\IntervalTaxeController;
use App\Http\Controllers\Api\MandateController;
use App\Http\Controllers\Api\MandateFileController;
use App\Http\Controllers\Api\UploadFileController;
use App\Http\Controllers\Api\VariableController;
use App\Http\Controllers\Api\WaitingStudentController;
use App\Models\UploadedFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\OrdonnanceFileController;
use App\Http\Controllers\Api\StatController;
use App\Http\Controllers\Api\StatGroupController;
use App\Http\Controllers\AttestationController;
use App\Http\Controllers\AttestationTypeController;
use App\Http\Controllers\AuditController;
use App\Http\Controllers\BourseAlternanceController;
use App\Http\Controllers\CodeOrganismeController;
use App\Http\Controllers\DemandeRecouvrementController;
use App\Http\Controllers\DocumentsAttestationController;
use App\Http\Controllers\FicheOrganismeController;
use App\Http\Controllers\MotifRetraitInscriptionController;
use App\Http\Controllers\RectificatifController;
use App\Http\Controllers\RectificatifNumeroController;
use App\Http\Controllers\RetraitInscriptionController;
use App\Http\Controllers\StatistiqueController;
use App\Models\DocumentsAttestationUpload;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', function (Request $request) {
        //            return app()->getLocale();
        return auth()->user();
    });

    Route::post('/profile/change-password', [ProfileController::class, 'change_password']);

    Route::post('/profile/update-profile', [ProfileController::class, 'update_profile']);
    Route::post('/profile/update-users-profile/{user}', [ProfileController::class, 'update_users_profile']);
    Route::post('/profile/update-users-status/{user}', [ProfileController::class, 'update_users_status']);

    // users
    Route::apiResource('/users', AdminController::class);

    // demande types
    Route::apiResource('/demande_types', DemandeTypeController::class);
    Route::get('/arbre_demande_types', [DemandeTypeController::class, 'arbre']);
    Route::get('/arbre_fils_demande_types', [DemandeTypeController::class, 'indexArbreFils']);
    Route::get('/demande_type_by_parent_code/{code}', [DemandeTypeController::class, 'indexByParent']);

    // demande types config
    Route::apiResource('/demande_types_config', DemandeTypeConfigController::class);
    Route::post('/demande_types_config/edit/{demande_type}', [DemandeTypeConfigController::class, 'edit']);

    // classifications
    Route::apiResource('/classifications', ClassificationController::class);
    Route::post('/classifications/edit/{classification}', [ClassificationController::class, 'edit']);
    Route::post('/classifications/document_classifications/edit/{classification}', [ClassificationController::class, 'editDocuments']);
    Route::get('/arbre_classifications', [ClassificationController::class, 'arbre']);

    // classifications config
    Route::apiResource('/classifications_config', ClassificationConfigController::class);
    Route::post('/classifications_config/edit/{classification}', [ClassificationConfigController::class, 'edit']);

    // document classifications
    Route::apiResource('/document_classifications', DocumentClassificationController::class);
    Route::get('/document_classifications/by_classification/{classification}', [DocumentClassificationController::class, 'byClassification']);
    Route::post('/document_classifications/edit/{document_classification}', [DocumentClassificationController::class, 'edit']);
    Route::post('/getDocumentClassification ', [DocumentClassificationController::class, 'getDocumentClassification']);

    // creation demandes
    Route::post('/demandes/create/{user}', [DemandeController::class, 'createDemande']);
    // modifier demandes incoherent
    Route::post('/demandes/incoherent/{demande}', [DemandeController::class, 'incoherentDemande']);

    // listage des demandes
    Route::post('/Bourses-de-stage', [DemandeController::class, 'index']);
    Route::post('/demandes/edit_etat_dossier_calcule/{demande}', [DemandeController::class, 'editEtatDossierCalcule']);
    Route::post('/demandes/dossier_arrive/{demande}', [DemandeController::class, 'dossierArrive']);
    Route::post('/demandes/restore_to_decision/{demande}', [DemandeController::class, 'restoreToDecision']);
    Route::post('/demandes/dossier_search_validate/{code}', [DemandeController::class, 'dossierArriveSearchAndValidate']);
    Route::post('/demandes/decision/{demande}', [DemandeController::class, 'decision']);
    Route::get('/demandes/historique_etudiant/{user_id}', [DemandeController::class, 'historique_etudiant']);
    Route::get('/demandes/exportExcel', [DemandeController::class, 'exportExcel']);
    Route::get('/demandes/restore/{demande}', [DemandeController::class, 'restore']);
    Route::apiResource('/demandes', DemandeController::class);

    Route::post('/demandes/create-preparation', [PreparationController::class, 'createPreparation']);
    Route::post('/demandes/verifier-preparation', [PreparationController::class, 'verifyPreparation']);
    Route::post('/demandes/restore-preparation/{preparation}', [PreparationController::class, 'restore']);
    Route::post('/demandes/restore-demande/{preparation}/{demande}', [PreparationController::class, 'restoreDemande']);
    Route::post('/demandes/synchronize-preparation/{preparation}', [PreparationController::class, 'synchronize']);

    Route::get('/preparations/exportExcel/{type}', [PreparationController::class, 'exportExcel']);

    Route::get('/preparations/{type}', [PreparationController::class, 'index']);
    Route::get('/preparations/demandes/{preparation}', [PreparationController::class, 'generateExcel']);
    Route::get('/preparation/{preparation}', [PreparationController::class, 'show']);


    // document demande
    Route::post('/document_demandes/edit/{document}', [DocumentDemandeController::class, 'edit']);
    Route::get('/document_demandes/by_demande/{demande}', [DocumentDemandeController::class, 'documentsByDemande']);
    Route::post('/getDemandeDocument ', [DocumentDemandeController::class, 'getDemandeDocument']);

    // documents_predefinis
    Route::apiResource('/documents_predefinis', DocumentPredefiniController::class);
    Route::post('/documents_predefinis/edit/{documentPredefini}', [DocumentPredefiniController::class, 'edit']);

    // documents_predefinis
    Route::apiResource('/champ_predefinis', ChampPredefiniController::class);
    Route::post('/champ_predefinis/edit/{champPredefini}', [ChampPredefiniController::class, 'edit']);

    // student_from_mes
    Route::apiResource('/student_from_mes', StudentFromMesController::class);
    Route::post('/student_from_mes/edit/{studentFromMes}', [StudentFromMesController::class, 'edit']);


    // international_students
    Route::apiResource('/international_students', InternationalStudentController::class);
    Route::post('/international_students/edit/{international_students}', [InternationalStudentController::class, 'edit']);

    // registred_students
    Route::apiResource('/registered_students', RegisteredStudentController::class);
    Route::post('/registered_students/edit/{registered_student}', [RegisteredStudentController::class, 'edit']);
    Route::post('/registered_students/update-annee/{id}', [RegisteredStudentController::class, 'update_annee_etude']);
    Route::post('/registered_students/exportExcel', [RegisteredStudentController::class, 'exportExcel']);
    Route::post('/registered_students/activate-account/{registered_student}', [RegisteredStudentController::class, 'activateAccount']);
    Route::post('/registered_students/deactivate-account/{registered_student}', [RegisteredStudentController::class, 'deactivateAccount']);

    // waiting_students
    Route::apiResource('/waiting_students', WaitingStudentController::class);
    Route::post('/waiting_students/edit/{waiting_student}', [WaitingStudentController::class, 'edit']);
    Route::post('/waiting_students/refuse', [WaitingStudentController::class, 'refuse']);


    // uploaded_files
    Route::apiResource('/uploaded_files', UploadedFileController::class);
    Route::post('/uploaded_files/edit/{uploadedFile}', [UploadedFileController::class, 'edit']);
    Route::post('/getUploadedFileDocument', [UploadedFileController::class, 'getUploadedFileDocument']);

    // Exported File
    Route::apiResource('/exported_files', ExportedFileController::class);
    Route::post('/getExportedFile', [ExportedFileController::class, 'getExportedFile']);

    // users
    // Route::apiResource('/users', UserController::class);

    // Role
    Route::apiResource('/roles', RoleController::class);
    Route::post('/roles/edit', [RoleController::class, 'edit']);

    // gouvernorats
    Route::apiResource('/gouvernorats', GouvernoratController::class);
    Route::post('/gouvernorats/edit/{gouvernorat}', [GouvernoratController::class, 'edit']);

    // offices
    Route::apiResource('/offices', OfficeController::class);
    Route::post('/offices/edit/{office}', [OfficeController::class, 'edit']);

    // diplomes
    Route::apiResource('/diplomes', DiplomeController::class);
    Route::post('/diplomes/edit/{diplome}', [DiplomeController::class, 'edit']);

    // catbs
    Route::apiResource('/catbs', CatbController::class);
    Route::post('/catbs/edit/{catb}', [CatbController::class, 'edit']);

    // MontantPret
    Route::apiResource('/montant_prets', MontantPretController::class);
    Route::post('/montant_prets/edit/{montantPret}', [MontantPretController::class, 'edit']);

    // universites
    Route::apiResource('/universites', UniversiteController::class);
    Route::post('/universites/edit/{universite}', [UniversiteController::class, 'edit']);

    // etablissements
    Route::apiResource('/etablissements', EtablissementController::class);
    Route::post('/etablissements/edit/{etablissement}', [EtablissementController::class, 'edit']);

    // filieres
    Route::apiResource('/filieres', FiliereController::class);
    Route::post('/filieres/edit/{filiere}', [FiliereController::class, 'edit']);

    // lycees
    Route::apiResource('/lycees', LyceeController::class);
    Route::post('/lycees/edit/{lycee}', [LyceeController::class, 'edit']);
    Route::get('/lycees/exportExcel', [LyceeController::class, 'exportExcel']);

    // professions
    Route::apiResource('/professions', ProfessionController::class);
    Route::post('/professions/edit/{profession}', [ProfessionController::class, 'edit']);

    // profession_types
    Route::apiResource('/profession_types', ProfessionTypeController::class);
    Route::post('/profession_types/edit/{professionType}', [ProfessionTypeController::class, 'edit']);

    // messagePredefinis
    Route::apiResource('/message_predefinis', MessagePredefiniController::class);
    Route::post('/message_predefinis/edit/{messagePredefini}', [MessagePredefiniController::class, 'edit']);

    // reclamations
    Route::apiResource('/reclamations', ReclamationController::class);
    Route::post('/reclamations/edit/{reclamation}', [ReclamationController::class, 'edit']);
    Route::post('/getReclamationDocument', [ReclamationController::class, 'getReclamationDocument']);
    Route::get('/reclamations_exportExcel', [ReclamationController::class, 'exportExcel']);

    // reclamation_types
    Route::apiResource('/reclamation_types', ReclamationTypeController::class);
    Route::post('/reclamation_types/edit/{reclamationType}', [ReclamationTypeController::class, 'edit']);

    // variables
    Route::apiResource('/variables', VariableController::class);
    Route::post('/variables/edit/{variable}', [VariableController::class, 'edit']);

    // anneeUniversitaires
    Route::apiResource('/annee_universitaires', AnneeUniversitaireController::class);
    Route::post('/annee_universitaires/edit/{anneeUniversitaire}', [AnneeUniversitaireController::class, 'edit']);
    Route::get('/annee_universitaires/current/annee', [AnneeUniversitaireController::class, 'currentAnnee']);
    Route::get('/annee_universitaires/last/annee', [AnneeUniversitaireController::class, 'lastAnnee']);

    // anneeBacs
    Route::apiResource('/annee_bacs', AnneeBacController::class);
    Route::post('/annee_bacs/edit/{anneeBac}', [AnneeBacController::class, 'edit']);

    // countries
    Route::apiResource('/countries', CountryController::class);
    Route::post('/countries/edit/{country}', [CountryController::class, 'edit']);

    // notifications
    Route::apiResource('/notifications', NotificationController::class);
    Route::post('/notifications/read_all', [NotificationController::class, 'readAll']);
    Route::post('/notifications/read', [NotificationController::class, 'markAsRead']);

    Route::apiResource('/resultats', ResultatController::class);

    // décisions
    Route::get('/historiques/historique_etudiant/{cin}', [DecisionController::class, 'historiqueEtudiant']);
    Route::post('/historiques/upload_historique_file', [DecisionController::class, 'uploadHistoriqueFile']);
    Route::get('/historiques/getDistinctNDEC/{type}', [DecisionController::class, 'getDistinctNDEC']);
    Route::get('/historiques/getDistinctNDECGroupedByAnnee', [DecisionController::class, 'getDistinctNDECGroupedByAnnee']);
    Route::get('/historiques/exportExcel', [DecisionController::class, 'exportExcel']);
    Route::apiResource('/historiques', DecisionController::class);
    Route::post('/historiques/edit/{decision}', [DecisionController::class, 'edit']);


    // décisions files
    Route::post('/decisions_files/generatePDF', [DecisionsFilesController::class, 'generatePDF']);
    Route::get('/decisions_files/{type}', [DecisionsFilesController::class, 'index']);
    Route::post('/decisions_files/downloadExistingPDF', [DecisionsFilesController::class, 'downloadExistingPDF']);

    // attestation_types
    Route::apiResource('/attestation_types', AttestationTypeController::class);
    Route::post('/attestation_types/edit/{id}', [AttestationTypeController::class, 'edit']);
    Route::get('/getDocumentAttestations', [AttestationTypeController::class, 'getDocumentAttestations']);

    // attestation_types
    Route::apiResource('/documents_attestations', DocumentsAttestationController::class);
    Route::post('/documents_attestations/edit/{attestationType}', [DocumentsAttestationController::class, 'edit']);

    // attestations
    Route::apiResource('/attestations', AttestationController::class);
    Route::post('/attestations/edit/{attestation}', [AttestationController::class, 'edit']);
    Route::post('/generateAttestation', [AttestationController::class, 'generateAttestation']);
    Route::get('/attestations_exportExcel', [AttestationController::class, 'exportExcel']);

    // historique mandats
    Route::get('/mandates', [MandateController::class, 'index']);
    Route::post('/mandates/upload_mandate_file', [MandateController::class, 'uploadMandateFile']);
    Route::get('/mandates/getDistinctNDEC/{type}', [MandateController::class, 'getDistinctNDEC']);
    Route::get('/mandates/getDistinctNDECByAnnee', [MandateController::class, 'getDistinctNDECByAnnee']);
    Route::get('/mandates/get_all_ndec', [MandateController::class, 'getAllNDEC']);
    Route::get('/mandates/historique_etudiant/{cin}', [MandateController::class, 'historiqueEtudiant']);
    Route::get('/mandates/export', [MandateController::class, 'mandatesToExport']);
    Route::post('/mandates/change_state/{id}/{state}', [MandateController::class, 'changeState']);
    Route::post('/mandates/notifier_etudiants', [MandateController::class, 'notifierEtudiants']);
    Route::get('/mandates/get_distinct_date_payment', [MandateController::class, 'getDistinctDatePayment']);
    Route::get('/mandates/count_notified_students', [MandateController::class, 'countNotifiedStudents']);



    // mandats files
    Route::get('/mandates_files/{type}', [MandateFileController::class, 'index']);
    Route::post('/mandates_files/generatePDF', [MandateFileController::class, 'generatePDF']);
    Route::post('/mandates_files/downloadExistingPDF', [MandateFileController::class, 'downloadExistingPDF']);

    // ordonnances files
    Route::get('/ordonnances_files', [OrdonnanceFileController::class, 'index']);
    Route::post('/ordonnances_files/generatePDF', [OrdonnanceFileController::class, 'generatePDF']);

    // interval taxes
    Route::apiResource('/interval_taxes', IntervalTaxeController::class);
    Route::post('/interval_taxes/edit/{intervalTaxe}', [IntervalTaxeController::class, 'edit']);

    // stat group
    Route::apiResource('/stat_groups', StatGroupController::class);
    Route::post('/stat_groups/edit/{statGroup}', [StatGroupController::class, 'edit']);

    // stat
    Route::apiResource('/stats', StatController::class);
    Route::post('/stats/downloadExistingPDF', [StatController::class, 'downloadExistingPDF']);

    // historique ( audit )
    Route::get('/historique', [AuditController::class, 'index']);
    Route::get('/historique/types', [AuditController::class, 'getTypes']);
    Route::get('/historique/audits', [AuditController::class, 'show']);


    // bourse_alternance
    Route::apiResource('/bourse_alternance', BourseAlternanceController::class);
    Route::post('/bourse_alternance/edit/{boursealternance}', [BourseAlternanceController::class, 'edit']);

    // generateAttestationNoChanges
    Route::post('/generateAttestationNoChanges', [AttestationController::class, 'generateAttestationNoChanges']);

    // absence
    Route::apiResource('/absence', AbsenceController::class);
    Route::post('/absence/edit/{absence}', [AbsenceController::class, 'edit']);


    /**
     * FOURAT NEW
     */
    //Route::get('/getStatBourseUniv', [StatistiqueController::class, 'getStatBourseUniv']);
    Route::get('/getStatRes', [StatistiqueController::class, 'getStatRes']);
    Route::get('/getStatGraphTotalByYear', [StatistiqueController::class, 'getStatGraphTotalByYear']);
    Route::get('/getStatTotalByFacSexNationality', [StatistiqueController::class, 'getStatTotalByFacSexNationality']);

    // retrait_inscription
    Route::apiResource('/retrait_inscription', RetraitInscriptionController::class);
    Route::post('/retrait_inscription/edit/{retraitInscription}', [RetraitInscriptionController::class, 'edit']);

    // motif_retrait_inscription
    Route::get('/motif_retrait_inscription/active', [MotifRetraitInscriptionController::class, 'active']);
    Route::apiResource('/motif_retrait_inscription', MotifRetraitInscriptionController::class);
    Route::post('/motif_retrait_inscription/edit/{motifRetraitInscription}', [MotifRetraitInscriptionController::class, 'edit']);

    //fiche_organisme
    Route::apiResource('/fiche_organisme', FicheOrganismeController::class);
    Route::post('/fiche_organisme/edit/{fiche_organisme}', [FicheOrganismeController::class, 'edit']);
    Route::get('/fiche_organisme/files/{filename}', function ($filename) {
        //$filePath = 'uploads/ficheOrganisme/' . $filename;
        $file = Storage::get($filename);
        return response($file, 200)->header('Content-Type', 'application/pdf');
    })->where('filename', '.*');

    // demande recouvrement

    Route::apiResource('/demandeRecouvrement', DemandeRecouvrementController::class);
    Route::post('/demandeRecouvrementPdf', [DemandeRecouvrementController::class, 'generatePdf']);
    Route::post('/demandeRecouvrement/edit/{demandeRecouvrement}', [DemandeRecouvrementController::class, 'edit']);

    Route::post('/checkStudent', [WaitingStudentController::class, 'checkStudent']);
    Route::post('/checkAndCreateStudent', [WaitingStudentController::class, 'checkAndCreateStudent']);
    Route::post('/signup-new-student', [WaitingStudentController::class, 'signupNewStudent']);
    Route::get('/checkAllAndCreateStudent', [WaitingStudentController::class, 'checkAllAndCreateStudent']);

    Route::get('demandeAll', [DemandeController::class, 'all']);

    Route::post('homeStats', [StatistiqueController::class, 'homeStats']);


    // rectificatif numero
    Route::apiResource('/rectificatifNumero', RectificatifNumeroController::class);
    Route::post('/rectificatifNumero/edit/{rectif}', [RectificatifNumeroController::class, 'edit']);

    // rectificatif
    Route::apiResource('/rectificatif', RectificatifController::class);
    Route::post('/rectificatif/edit/{rectif}', [RectificatifController::class, 'edit']);
    Route::get('/rectificatifs/exportExcel', [RectificatifController::class, 'exportExcel']);

    // code organisme
    Route::apiResource('/codeOrganisme', CodeOrganismeController::class);
    Route::post('/codeOrganisme/edit/{code}', [CodeOrganismeController::class, 'edit']);

    //Route::post('/generateStatDiplome', [StatistiqueController::class, 'generateStatDiplome']);
    Route::post('/statAttestation', [StatistiqueController::class, 'statAttestation']);

    Route::get('/exportEtrangersBoursiersParPays', [StatistiqueController::class, 'exportEtrangersBoursiersParPays']);

    /**
     * END FOURAT
     */
// todo remove and secure
    Route::get('/getStatBourseUniv', [StatistiqueController::class, 'getStatBourseUniv']);

    Route::get('/generateStatDiplome', [StatistiqueController::class, 'generateStatDiplome']);
    Route::get('/exportCustomTable', [StatistiqueController::class, 'exportCustomTable']);
    Route::get('/exportCustomTablePage2', [StatistiqueController::class, 'exportCustomTablePage2']);

    Route::get('/exportStatParUniversiteEtAnnee', [StatistiqueController::class, 'exportStatParUniversiteEtAnnee']);
    Route::get('/exportRevenuSuffisantStat', [StatistiqueController::class, 'exportRevenuSuffisantStat']);
    Route::get('/exportFilsParentMeducMesrsParAnnee', [StatistiqueController::class, 'exportFilsParentMeducMesrsParAnnee']);

    Route::get('/generateStatAttestation', [StatistiqueController::class, 'generateStatAttestation']);
    Route::get('/exportExcelAttestationStat', [StatistiqueController::class, 'exportExcelAttestationStat']);
    Route::get('/statAttestationCards', [AttestationController::class, 'indexStatByType']);
//    Route::get('/statAttestationCards', [StatistiqueController::class, 'statAttestationCards']);
    Route::get('/statListEtudiantsEtrangersBoursiers', [StatistiqueController::class, 'statListEtudiantsEtrangersBoursiers']);
    Route::get('/exportListEtudiantsEtrangersBoursiers', [StatistiqueController::class, 'exportListEtudiantsEtrangersBoursiers']);
    Route::get('/exportFicheOrganisme', [StatistiqueController::class, 'exportFicheOrganisme']);

    Route::get('/getConfigDemandeTypeAndPrevious/{id}', [AuditController::class, 'getConfigDemandeTypeAndPrevious'])
        ->where('id', '[0-9]+');

    Route::get('/generateAttestation/{id}', [AttestationController::class, 'generateAttestation'])
        ->where('id', '[0-9]+');
    Route::post('/getAttestationDocument', [AttestationController::class, 'getAttestationDocument']);

    Route::post('/getModeleFile', function (Request $request) {
        $request->validate([
            'name' => 'required',
        ]);

        $filename = $request->name;
        $path= '/exemple/'.$request->name;

        if (!Storage::disk('sftp_public')->exists($path)) {
            return response()->json(Storage::disk('sftp_public')->path($path), 404);
        }
        return Storage::disk('sftp_public')->download($path, $filename);
    });

    Route::get('/storage/{id}', function ($id) {
        $filename = DocumentsAttestationUpload::findOrFail($id)->attached_file;
        //dd($filename);
        //$path = storage_path('app/uploads/'. $filename);
        $path = public_path('uploads/attestations/'.$id.'/' . $filename);
        //dd($path);
        if (!File::exists($path)) {
            abort(404);
        }

        $file = File::get($path);
        $type = File::mimeType($path);

        $response = Response::make($file, 200);
        $response->header("Content-Type", $type);

        return $response;
    });


    Route::post('/getExempleDocument', function (Request $request) {
        $request->validate([
            'document' => 'required',
        ]);

        $filename = $request->document;
        $path= '/exemple/'. $filename;


        if (!Storage::exists($path)) {
            return response()->json(Storage::path($path), 404);
        }
        return Storage::download($path, $filename);
    });
});

Route::get('/annee_bacs', [AnneeBacController::class, 'index']);
Route::get('/gouvernorats', [GouvernoratController::class, 'index']);
Route::get('/countries', [CountryController::class, 'index']);

Route::post('/signupVerification', [AuthController::class, 'signupVerification']);
Route::post('/signup', [AuthController::class, 'signup']);
Route::post('/login', [AuthController::class, 'login']);


Route::post('grantDemandeTypesAccess', [RoleController::class, 'grantDemandeTypesAccess']);


//Route::get('/test', function (Request $request) {
//    $demande = \App\Models\Demande::query()->first();
//    dd($demande->demandeAnneeEtudes()->orderBy('anneeUniversitaire.title','desc')->skip(1)->take(1)->first());
//});

//Route::get('/testgenerateAttestation/{id}', [AttestationController::class, 'testgenerateAttestation'])->where('id', '[0-9]+');

Route::post('/uploadFile', [UploadFileController::class, 'store']);
Route::post('/getFile', [UploadFileController::class, 'getFile']);
