<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bourse_alternances', function (Blueprint $table) {
            $table->unsignedBigInteger('etablissement_id')->nullable();

            $table->foreign('etablissement_id')
                ->references('id')
                ->on('etablissements');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bourse_alternances', function (Blueprint $table) {
            $table->dropForeign(['etablissement_id']);
        });
    }
};
