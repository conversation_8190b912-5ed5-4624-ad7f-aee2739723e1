<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEtablissementRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'code_univ' => 'required|integer',
            'code_gouv' => 'required|integer',
            'code_office' => 'required|string',
            'code_dir_reg' => 'nullable|string',
            'code_ministere' => 'nullable',
            'code' => 'required|string|unique:etablissements,code',
            'password' => 'required|string',
            'diplomesEtablissements' => 'array',
            ];
    }
}
