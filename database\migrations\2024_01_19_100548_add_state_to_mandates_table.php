<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mandates', function (Blueprint $table) {
            $table->integer('state')->nullable()->default(1)->after('annee_universitaire_id');
        
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mandates', function (Blueprint $table) {
            $table->dropColumn('state');
        });
    }
};
