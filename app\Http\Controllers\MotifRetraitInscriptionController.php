<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Resources\MotifRetraitInscriptionResource;
use App\Models\MotifRetraitInscription;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class MotifRetraitInscriptionController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index() //: AnonymousResourceCollection
    {
        return Cache::remember('MotifRetraitInscription', $this->cache_seconds, function () {
            return MotifRetraitInscriptionResource::collection(MotifRetraitInscription::get());
        });
    }


    public function active()
    {
        return Cache::remember('MotifRetraitInscriptionActive', $this->cache_seconds, function () {
            return MotifRetraitInscriptionResource::collection(MotifRetraitInscription::where('active', "1")->get());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request): Response
    {
            $request->validate([
                'code' => 'required|string',
                'label' => 'required|string',
                'active' => 'required|string',
            ]);

        Cache::forget('MotifRetraitInscription');
        Cache::forget('MotifRetraitInscriptionActive');


            $motifRetraitInscription = MotifRetraitInscription::create([
                'code' => $request->code,
                'label' => $request->label,
                'active' => $request->active == "true" ? 1 : 0,
            ]);

        return response(new MotifRetraitInscriptionResource($motifRetraitInscription), 201);
    }

    /**
     * Display the specified resource.
     *
     * @param MotifRetraitInscription $motifRetraitInscription
     * @return MotifRetraitInscriptionResource
     */
    public function show(MotifRetraitInscription $motifRetraitInscription): MotifRetraitInscriptionResource
    {
        return new MotifRetraitInscriptionResource($motifRetraitInscription);
    }

    public function edit(Request $request): Response
    {

            $request->validate([
                'id' => 'required|numeric',
                'code' => 'required|string',
                'label' => 'required|string',
                'active' => 'required|string',
            ]);

            Cache::forget('MotifRetraitInscription');
            Cache::forget('MotifRetraitInscriptionActive');

            $motifRetraitInscription = MotifRetraitInscription::findOrFail($request->id)->update([
                'id' => $request->id,
                'code' => $request->code,
                'label' => $request->label,
                'active' => $request->active == "true" ? 1 : 0,
            ]);

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param MotifRetraitInscription $motifRetraitInscription
     * @return Response
     */
    public function destroy(MotifRetraitInscription $motifRetraitInscription): Response
    {
        Cache::forget('MotifRetraitInscription');
        Cache::forget('MotifRetraitInscriptionActive');

        $motifRetraitInscription->delete();

        return response("", 204);
    }


}
