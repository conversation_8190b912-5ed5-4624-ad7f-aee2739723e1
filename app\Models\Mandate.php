<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Mandate extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    const ETAT_STRING = [
        1 => 'EN_COURS',
        2 => 'EN_INSTANCE',
        3 => 'NOTIFIE',
        4 => 'BLOQUE'
    ];
    const ETAT = [
        'EN_COURS'=> 1  ,
        'EN_INSTANCE'=> 2  ,
        'NOTIFIE'=> 3 ,
        'BLOQUE'=> 4
    ];

    protected $fillable = [
        'cin',
        'nom',
        'fac',
        'univ',
        'annee',
        'num_dec',
        'nbre_mois',
        'net_a_payer',
        'num_emission',
        'date_payement',
        'date_fin_validite',
        'type',
        'annee_universitaire_id',
        'state'
    ];

    public function etablissement() : BelongsTo
    {
        return $this->belongsTo(Etablissement::class,"fac","code");
    }

    public function annee_universitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,"annee_universitaire_id","id");
    }

    public function student() : BelongsTo
    {
        return $this->setConnection( config('database.secondConnection') )->belongsTo(User::class, 'cin', 'cin');
    }
}
