<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Models\ExportedFile;
use App\Notifications\ExportCompletedNotification;
use App\Notifications\SyncCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedSyncPreparation
{
    use Queueable, SerializesModels;

    public $user;
    public $syncCount;
    public $ndec_code;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(?Admin $user, $syncCount, $ndec_code)
    {
        $this->user = $user;
        $this->syncCount = $syncCount;
        $this->ndec_code = $ndec_code;
    }

    public function handle()
    {
        $this->user->notify(new SyncCompletedNotification($this->syncCount, $this->ndec_code));
    }
}
