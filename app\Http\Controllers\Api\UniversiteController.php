<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUniversiteRequest;
use App\Http\Requests\UpdateUniversiteRequest;
use App\Http\Resources\UniversiteResource;
use App\Models\Universite;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class UniversiteController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Universite', $this->cache_seconds, function () {
            return UniversiteResource::collection(Universite::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreUniversiteRequest $request
     * @return Response
     */
    public function store(StoreUniversiteRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Universite');
        Helpers::clearCacheIdp();

        $g = Universite::create($data);

        return response(new UniversiteResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Universite $universite
     * @return UniversiteResource
     */
    #[Pure] public function show(Universite $universite): UniversiteResource
    {
        return new UniversiteResource($universite);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateUniversiteRequest $request
     * @param Universite $universite
     * @return UniversiteResource
     */
    public function edit(UpdateUniversiteRequest $request, Universite $universite): UniversiteResource
    {
        $data = $request->validated();

        Cache::forget('Universite');
        Helpers::clearCacheIdp();

        $universite->update($data);

        return new UniversiteResource($universite);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Universite $universite
     * @return Response
     */
    public function destroy(Universite $universite): Response
    {
        if($universite->etablissements->count()){
            throw ValidationException::withMessages(["Cette universite est utilisé par d'autres tables"]);
        }
        Cache::forget('Universite');
        Helpers::clearCacheIdp();

        $universite->delete();

        return response("", 204);
    }
}
