<?php

namespace App\Jobs;

use App\Mail\ConfirmedStudentMail;
use App\Models\Admin;
use App\Models\EtudiantAnneeUniversitaire;
use App\Models\InternationalStudent;
use App\Models\StudentFromMes;
use App\Models\User;
use App\Models\WaitingUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ProcessCheckAllAndCreateStudent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Admin $user;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Admin $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $waitingStudents = WaitingUser::all();
        $nbAdded = 0;
        foreach ($waitingStudents as $waitingStudent){
            if($waitingStudent->type === 'tunisien'){
                $student_exist_in_minister_list = StudentFromMes::where('CIN', $waitingStudent->cin)
                    ->where('NBAC', $waitingStudent->num_bac)
                    ->where('CIN', '<>', '99999999')->first();
                $alredy_exist = User::where('cin', $waitingStudent->cin)->first();
                if($alredy_exist){
//                    Log::info('user already exist : '.$waitingStudent->cin);
                    $waitingStudent->delete();
                    continue;
                }
                $alredy_exist = User::where('email', $waitingStudent->email)->first();
                if($alredy_exist){
//                    Log::info('user already exist : '.$waitingStudent->email);
                    $waitingStudent->delete();
                    continue;
                }
                if( $student_exist_in_minister_list){
                    //existe dans la liste de ministère

                    $user = User::create([
                        "cin" => $waitingStudent->cin,
                        "num_bac" => $waitingStudent->num_bac,
                        "type" => $waitingStudent->type,
                        "name" => $waitingStudent->name,
                        "name_ar" => $waitingStudent->name_ar,
                        "firstName" => $waitingStudent->firstName,
                        "username" => $waitingStudent->email,
                        "email" => $waitingStudent->email,
                        "email_perso" => $waitingStudent->email_perso,
                        "password" => $waitingStudent->password,
                        "phoneNumber" => $waitingStudent->phoneNumber,
                        "phoneNumber2" => $waitingStudent->phoneNumber2,
                        "address" => $waitingStudent->address,
                        "annee_bac" => $waitingStudent->annee_bac,
                        "code_postal" => $waitingStudent->code_postal,
                        "country_id" => $waitingStudent->nationality_id,
                        "nationality_id" => $waitingStudent->nationality_id,
                        "code_gouv" => $waitingStudent->code_gouv,
                        "date_naissance" =>  date('Y-m-d', strtotime($waitingStudent->date_naissance)),
                        "role" => "client",
                        "student_from_mes_id" => $student_exist_in_minister_list->id,
                        "pere" => $waitingStudent->pere,
                        "mere" => $waitingStudent->mere,
                        "sex" => $waitingStudent->sex,
                    ]);


                    EtudiantAnneeUniversitaire::where('user_id', $waitingStudent->id)->first()?->update([
                        "user_id" => $user->id,
                        "waiting_user_id" => null,
                    ]);

                    Mail::to($waitingStudent->email)->send(new ConfirmedStudentMail($waitingStudent));
                    $nbAdded++;
                    WaitingUser::where('cin', $waitingStudent->cin)->first()->delete();
//                    dump($user);
//                    $waitingStudent->delete();

                }
            }
            else {
                $international_student_exist_in_minister_list = InternationalStudent::where('matricule', $waitingStudent->matricule)
                    ->where('num_passport', $waitingStudent->num_passport)
                    ->where('annee_bac', $waitingStudent->annee_bac)
                    ->where('matricule', '<>', '99999999')->first();
                $alredy_exist = User::where('matricule', $waitingStudent->matricule)->first();
                if($alredy_exist){
//                    Log::info('user already exist : '.$waitingStudent->matricule);
                    $waitingStudent->first()->delete();
                    continue;
                }
                //existe dans la liste de ministère
                if( $international_student_exist_in_minister_list){

                    $user = User::create([
                        "matricule" => $waitingStudent->matricule,
                        "num_passport" => $waitingStudent->num_passport,
                        "type" => $waitingStudent->type,
                        "name" => $waitingStudent->name,
                        "name_ar" => $waitingStudent->name_ar,
                        "firstName" => $waitingStudent->firstName,
                        "username" => $waitingStudent->email,
                        "email" => $waitingStudent->email,
                        "email_perso" => $waitingStudent->email_perso,
                        "password" => $waitingStudent->password,
                        "phoneNumber" => $waitingStudent->phoneNumber,
                        "phoneNumber2" => $waitingStudent->phoneNumber2,
                        "address" => $waitingStudent->address,
                        "annee_bac" => $waitingStudent->annee_bac,
                        "code_postal" => $waitingStudent->code_postal,
                        "country_id" => $waitingStudent->nationality_id,
                        "nationality_id" => $waitingStudent->nationality_id,
                        "code_gouv" => $waitingStudent->gouvernorat,
                        "date_naissance" =>  date('Y-m-d', strtotime($waitingStudent->date_naissance)),
                        "role" => "client",
                        "international_student_id" => $international_student_exist_in_minister_list->id,
                        "pere" => $waitingStudent->pere,
                        "mere" => $waitingStudent->mere,
                        "sex" => $waitingStudent->sex,
                    ]);

                    EtudiantAnneeUniversitaire::where('user_id', $waitingStudent->id)->first()?->update([
                        "user_id" => $user->id,
                        "waiting_user_id" => null,
                    ]);

                    Mail::to($waitingStudent->email)->send(new ConfirmedStudentMail($waitingStudent));
                    $nbAdded++;
                    WaitingUser::where('matricule', $waitingStudent->matricule)->first()->delete();
//                    dump($user);
                }
            }
        }
        dispatch(new NotifyUserOfCompletedVerificationAndCreatStudent($this->user, $nbAdded));
    }
}
