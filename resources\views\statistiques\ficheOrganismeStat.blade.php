<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>stat_nbre_demandes</title>

</head>

<body>


    <!--    START HEADER -->
    <table dir="rtl" style="margin-left: auto; margin-right: 0;">
        <thead>
            <tr>
                <th style="text-align: center;font-size: 18px;" valign="center" colspan="3" rowspan="2">
                    الجمهورية التونسية <br>
                    وزارة التعليم العالي والبحث العلمي

                </th>

                @if ($year_export)
                <th colspan="5"></th>
                <th style="text-align: center;font-size: 18px" colspan="2">
                    السنة الجامعية
                </th>
                <th style="text-align: center;font-size: 16px" colspan="2">
                    {{ @$year_export }}
                </th>
                @endif

            </tr>
            <tr>
                <th colspan="5"></th>
                @if ($date_export)
                <th colspan="2">
                </th>
                <th style="text-align: center;font-size: 16px" colspan="2">
                    {{ explode(" ",$date_export)[0]  }} <br>
                    {{ explode(" ",$date_export)[1]  }}
                </th>
                @endif
            </tr>
            <tr>
                <th></th>
            </tr>
            <tr>
                <th style="text-align: center;font-size: 20px" colspan="3">
                <b>
                        @switch($office)
                            @case("C")
                                ديوان الخدمات الجامعية للوسط
                                @break
                            @case("N")
                                ديوان الخدمات الجامعية للشمال
                                @break
                            @case("S")
                                ديوان الخدمات الجامعية للجنوب
                                @break
                            @default
                                @break

                        @endswitch
                    </b>
                </th>
            </tr>

            <tr></tr>
            <tr>
                <th></th>
                <th style="text-align: center;font-size: 16px" colspan="7">
                :عدد الطلبة و الاعتمادات خلال السنوات الجامعية {{ $yearStart }} الى {{ $yearEnd }}
            </th>
            </tr>

            <tr>
                <th rowspan="10000"></th>
            </tr>


        </thead>
    </table>
    <!--    END HEADER -->

    <table dir="rtl" style="margin-left: auto; margin-right: 0;" border="1">
        <thead>
            <tr>
                <th width="18" rowspan="2" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center"> السنة الجامعية <br> الصنف </th>
                @foreach($data->unique('title') as $item)
                    <th width="18" colspan="2" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">{{ $item->title }}</th>
                @endforeach
            </tr>
            <tr>
                @foreach($data->unique('title') as $item)
                    <th width="18" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">عدد الطلبة</th>
                    <th width="18" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">الاعتمادات</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach($data->unique('type') as $type)
            <tr>
                <td style="font-weight: bold;border: 1px solid #000;">{{ $type->type }}</td>
                @foreach($data->unique('title') as $year)
                    @php
                        $filteredData = $data->where('title', $year->title)
                                            ->where('type', $type->type);
                    @endphp
                    <td style="border: 1px solid #000;">{{ $filteredData->isEmpty() ? '0' : $filteredData->first()->col_total }}</td>
                    <td style="border: 1px solid #000;">{{ $filteredData->isEmpty() ? '0' : $filteredData->first()->col_montant }}</td>
                @endforeach
            </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr>
                <td style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">المجموع</td>
                @foreach($totals as $total)
                    <td style="border: 1px solid #000;">{{ $total['col_total'] }}</td>
                    <td style="border: 1px solid #000;">{{ $total['col_montant'] }}</td>
                @endforeach
            </tr>
            <tr></tr>
            <tr>
                <td></td>
                <td colspan="6" style="text-align: right; font-size: 13px">
                 تسند إلى الطلبة الجدد الدارسبن سنة أولى تعايم عالي والمنتمين لعائلات متوسطة الدخل *
                </td>
            </tr>
        </tfoot>
    </table>

</body>
</html>
