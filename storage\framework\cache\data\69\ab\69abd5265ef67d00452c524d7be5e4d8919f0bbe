1748264809O:58:"Illuminate\Http\Resources\Json\AnonymousResourceCollection":8:{s:8:"resource";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:49:{i:0;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:51;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:51;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:1;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:52;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:52;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:2;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:53;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:53;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:3;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:54;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:54;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:4;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:55;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:55;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:5;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:56;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:56;s:12:"code_diplome";s:4:"LICE";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:6;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:57;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:57;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:7;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:58;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:58;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:8;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:59;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:59;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:9;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:60;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:60;s:12:"code_diplome";s:4:"PREP";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:10;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:61;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:61;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:11;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:62;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:62;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:12;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:63;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:63;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:13;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:64;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:64;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:14;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:65;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:65;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:15;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:66;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:66;s:12:"code_diplome";s:4:"INGE";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:16;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:67;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:67;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:17;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:68;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:68;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:18;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:69;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:69;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:19;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:70;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:70;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:20;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:71;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:71;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:21;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:72;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:72;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:22;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:73;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:4;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:73;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:4;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:23;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:74;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:5;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:74;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:5;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:24;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:75;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:4;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:75;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:4;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:25;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:76;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:5;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:76;s:12:"code_diplome";s:4:"MEDC";s:11:"annee_etude";i:5;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:26;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:77;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:77;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:27;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:78;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:78;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:28;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:79;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:79;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:29;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:80;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:80;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:30;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:81;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:81;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:31;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:82;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:82;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:32;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:83;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:4;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:83;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:4;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:33;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:84;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:5;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:84;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:5;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:34;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:85;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:4;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:85;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:4;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:35;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:86;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:5;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:86;s:12:"code_diplome";s:4:"MDEN";s:11:"annee_etude";i:5;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:36;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:87;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:87;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:37;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:88;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:88;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:38;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:89;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:89;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:3;s:8:"resultat";i:1;s:7:"montant";i:600;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:39;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:90;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:90;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:40;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:91;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:91;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:2;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:41;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:92;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:92;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:3;s:8:"resultat";i:0;s:7:"montant";i:480;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:42;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:93;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:4;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:93;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:4;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:43;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:94;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:5;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:94;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:5;s:8:"resultat";i:1;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:44;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:95;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:4;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:95;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:4;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:45;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:96;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:5;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:96;s:12:"code_diplome";s:4:"PHAR";s:11:"annee_etude";i:5;s:8:"resultat";i:0;s:7:"montant";i:640;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:46;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:97;s:12:"code_diplome";s:4:"MSTR";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:1000;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:97;s:12:"code_diplome";s:4:"MSTR";s:11:"annee_etude";i:1;s:8:"resultat";i:1;s:7:"montant";i:1000;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:47;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:98;s:12:"code_diplome";s:4:"MSTR";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-12-06 09:16:04";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:98;s:12:"code_diplome";s:4:"MSTR";s:11:"annee_etude";i:1;s:8:"resultat";i:0;s:7:"montant";i:800;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-12-06 09:16:04";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:48;O:38:"App\Http\Resources\MontantPretResource":4:{s:8:"resource";O:22:"App\Models\MontantPret":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"bpasBack.montant_prets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:99;s:12:"code_diplome";s:4:"MSTR";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:1400;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:99;s:12:"code_diplome";s:4:"MSTR";s:11:"annee_etude";i:2;s:8:"resultat";i:1;s:7:"montant";i:1400;s:10:"created_at";s:19:"2023-11-28 08:15:17";s:10:"updated_at";s:19:"2023-11-28 08:15:17";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"resultat";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:12:"code_diplome";i:1;s:11:"annee_etude";i:2;s:8:"resultat";i:3;s:7:"montant";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:8:"collects";s:38:"App\Http\Resources\MontantPretResource";s:10:"collection";r:2;s:29:" * preserveAllQueryParameters";b:0;s:18:" * queryParameters";N;s:12:"preserveKeys";b:1;}