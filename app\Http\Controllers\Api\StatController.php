<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StatRequest;
use App\Http\Resources\StatResource;
use App\Models\AnneeUniversitaire;
use App\Models\Decision;
use App\Models\IntervalTaxe;
use App\Models\Mandate;
use App\Models\Stat;
use App\Models\StatGroup;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use \Mpdf\Mpdf as PDF;

class StatController extends Controller
{
    public function index()
    {
        $stats = Stat::with('anneeUniversitaire')->orderBy('created_at', 'desc')->get();

        return StatResource::collection($stats);
    }

    public function store(StatRequest $request)
    {
        $filename = $this->do_stat($request->annee_universitaire_id, $request->date_payment, $request->num_dec, $request->tranche, $request->nbr_mois, $request->title, $request->mnt);

        $stat = Stat::create([
            'num_dec' => $request->num_dec,
            'date_payment' => $request->date_payment,
            'annee_universitaire_id' => $request->annee_universitaire_id,
            'tranche' => $request->tranche,
            'nbr_mois' => $request->nbr_mois,
            'title' => $request->title,
            'mnt' => $request->mnt,
            'path' => $filename
        ]);


        return new StatResource($stat);
    }

    public function downloadExistingPDF(Request $request)
    {
        return response()->download(storage_path('app/' . $request->fileName));
    }

    function do_stat($annee_univ, $date_payement, $num_decision, $num_tranche, $nbr_mois, $titre, $mnt)
    {
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');

        $t = ($num_tranche == 1) ? 'ère' : 'ème';

        $annee_univ_title = AnneeUniversitaire::findOrFail($annee_univ)->title;
        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<br><br><P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>Statistique Payement: " . $date_payement . "<br>" . $num_tranche . $t . " Tranche (" . $nbr_mois . " mois+Fourniture) " . $annee_univ_title . "</P>");

        $table1 = "<table align=center border=1 style='width:80%;'>"
            . "<tr><td>Type Bourse</td><td>Mt Mensuel</td><td>Mt Fourniture</td><td>Nbr etudiants</td><td>Total</td></tr>"
            . "<tr><td colspan=5 >Admis</td></tr>";

        $totalEtd = 0;
        $totaldecs = 0;
        $groups = StatGroup::where('res', 1)->with('conditions')->orderBy('order')->get();

        foreach ($groups as $key => $group) {
            $totalPerGroup = 0;
            $query = Decision::where('annee_id', $annee_univ)->where('ndec', $num_decision)
                ->where('mbs', $group->mtm)
                ->where('situa', 'P')
                ->where('mf', $group->mtf)
                ->where('office','like', '%C%');
            if (count($group->conditions) > 0) {
                foreach ($group->conditions as $condition) {
                    $countPerCondition = $query->clone()
                        ->when($condition['discip'], function ($query) use ($condition) {
                            return $query->whereIn('discip', $condition['discip']);
                        })
                        ->when($condition['anet'], function ($query) use ($condition) {
                            return $query->whereIn('anet', $condition['anet']);
                        })->count();
                    $totalPerGroup += $countPerCondition;
                }
            } else {
                $totalPerGroup += $query->clone()->count();
            }
            $totalEtd += $totalPerGroup;
            //dans le cas où le diplôme est doctorat et le nombre de mois = 10 il faut multiplier par 12 et non par 10
            if (strtolower($group->title) == 'doctorat' && $nbr_mois == 10) {
                $totaldecs += ($totalPerGroup * $group->mtm * 12) + ($totalPerGroup * $group->mtf);
                $table1 = $table1 . "<tr><td>" . $group->title . "</td><td>" . $group->mtm . "</td><td>" . $group->mtf . "</td><td align=right>" . $totalPerGroup . "</td><td align=right>" . number_format((($totalPerGroup * $group->mtm * 12) + ($totalPerGroup * $group->mtf)), 3, ',', ' ') . "</td></tr>";
            } else {
                $totaldecs += ($totalPerGroup * $group->mtm * $nbr_mois) + ($totalPerGroup * $group->mtf);
                $table1 = $table1 . "<tr><td>" . $group->title . "</td><td>" . $group->mtm . "</td><td>" . $group->mtf . "</td><td align=right>" . $totalPerGroup . "</td><td align=right>" . number_format((($totalPerGroup * $group->mtm * $nbr_mois) + ($totalPerGroup * $group->mtf)), 3, ',', ' ') . "</td></tr>";
            }

        }

        $table2 = "<tr><td colspan=5 >Redoublants</td></tr>";

        $groups = StatGroup::where('res', 0)->with('conditions')->orderBy('order')->get();

        foreach ($groups as $key => $group) {
            $totalPerGroup = 0;
            $query = Decision::where('annee_id', $annee_univ)
                ->where('ndec', $num_decision)
                ->where('situa', 'P')
                ->where('mbs', $group->mtm)
                ->where('mf', $group->mtf)
                ->where('office','like', '%C%');
            if (count($group->conditions) > 0) {
                foreach ($group->conditions as $condition) {
                    $countPerCondition = $query->clone()
                        ->when($condition['discip'], function ($query) use ($condition) {
                            return $query->whereIn('discip', $condition['discip']);
                        })
                        ->when($condition['anet'], function ($query) use ($condition) {
                            return $query->whereIn('anet', $condition['anet']);
                        })->count();
                    $totalPerGroup += $countPerCondition;
                }
            } else {
                $totalPerGroup += $query->clone()->count();
            }
            $totalEtd += $totalPerGroup;
            $totaldecs += ($totalPerGroup * $group->mtm * $nbr_mois) + ($totalPerGroup * $group->mtf);
            $table2 = $table2 . "<tr><td>" . $group->title . "</td><td>" . $group->mtm . "</td><td>" . $group->mtf . "</td><td align=right>" . $totalPerGroup . "</td><td align=right>" . number_format((($totalPerGroup * $group->mtm * $nbr_mois) + ($totalPerGroup * $group->mtf)), 3, ',', ' ') . "</td></tr>";
        }


        $mpdf->WriteHTML($table1 . $table2);
        $mpdf->WriteHTML("<tr><td colspan=3>Total</td><td align='right'>" . $totalEtd . "</td><td align='right'>" . number_format($totaldecs, 3, ',', ' ') . "</td></tr>");

        $mpdf->WriteHTML("</table>");


        if ($mnt != 0) {
            $mpdf->WriteHTML("<br>- Montant de " . $titre . ": " . number_format($mnt, 3, ',', ' ') . "<br>");
            $mpdf->WriteHTML("- Total= " . number_format($totaldecs, 3, ',', ' ') . " + " . number_format($mnt, 3, ',', ' ') . " = " . number_format($totaldecs + $mnt, 3, ',', ' ') . " DT");
            $totaldecs += $mnt;
        }


        $mpdf->WriteHTML("<h4>Tableau des Taxes</h4>");

        $intervals = IntervalTaxe::orderBy('min')->get();

        $total_taxes = 0;
        $total_nbr_mandats = 0;

        $mpdf->WriteHTML("<table align=center border=1 style='width:100%'><tr ><td>Interval mandat</td><td>Taxe</td><td  width='20%'>Nbr des mandats</td><td>Taxes</td></tr>");
        $formatted_date = Carbon::createFromFormat('d/m/Y', $date_payement)->format('Y-m-d');
        foreach ($intervals as $key => $interval) {
            $nbr_mandats = Mandate::where('annee_universitaire_id', $annee_univ)
                ->whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)
//                ->where('net_a_payer', '>', $interval->min * 1000)
//                ->where('net_a_payer', '<=', $interval->max * 1000)
                ->whereRaw('CONVERT(`net_a_payer`, INT) > ? ', $interval->min)
                ->whereRaw('CONVERT(`net_a_payer`, INT) <= ?', $interval->max)
//                ->whereRaw('CONVERT(`net_a_payer`, INT) BETWEEN ? AND ?', [$interval->min , $interval->max ])
                ->count();
            $mpdf->WriteHTML("<tr><td width=25% align=center>"  . " >". $interval->min . " <=" . $interval->max . "</td><td width=30% align=center>" . $interval->taxe . "</td><td width=15% align=right>" . $nbr_mandats . "</td><td width=30% align=right>" . number_format($nbr_mandats * $interval->taxe, 3, ',', ' ') . "</td></tr>");
            $total_taxes += $nbr_mandats * $interval->taxe;
            $total_nbr_mandats += $nbr_mandats;
        }

        $mpdf->WriteHTML("<tr><td colspan=2 >Total</td><td align='right'>" . $total_nbr_mandats . "</td><td align='right'>" . number_format($total_taxes, 3, ',', ' ') . "</td></tr>");

        $mpdf->WriteHTML("</table>");


        $mpdf->WriteHTML("<h4>Total Payement = " . number_format($totaldecs + $total_taxes, 3, ',', ' ') . " DT</h4>");


        $filename = 'stat/stat_decision_' . $formatted_date . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        return $filename;
    }
}
