<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stats', function (Blueprint $table) {
            $table->id();
            $table->string('num_dec');
            $table->string('date_payment');
            $table->unsignedBigInteger('annee_universitaire_id');
            $table->string('tranche');
            $table->string('nbr_mois');
            $table->string('title')->nullable();
            $table->string('mnt')->nullable();
            $table->string('path');
            $table->timestamps();
            $table->foreign('annee_universitaire_id','stats_annee_universitaire_id_foreign')
            ->references('id')
            ->on('annee_universitaires');
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stats');
    }
};
