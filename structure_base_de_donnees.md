# Structure de la Base de Données

Ce document décrit la structure de la base de données du système BPAS (Bourse, Prêts et Aides Sociales), y compris toutes les tables, leurs champs et relations.

## Tables Principales

### 1. Admins
Table qui stocke les informations des administrateurs du système.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| name | string | Nom de l'administrateur |
| firstName | string | Prénom de l'administrateur |
| role | string | Rôle de l'administrateur |
| username | string | Nom d'utilisateur (unique) |
| email | string | Adresse email (unique) |
| email_verified_at | timestamp | Date de vérification de l'email |
| password | string | Mot de passe crypté |
| profile_photo | string | Chemin vers la photo de profil (optionnel) |
| status | boolean | Statut de l'administrateur (actif/inactif) |
| remember_token | string | Token pour la fonction "Se souvenir de moi" |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 2. Annee_universitaires
Table qui stocke les informations des années universitaires.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| title | string | Titre de l'année universitaire |
| start | date | Date de début |
| end | date | Date de fin |
| annee_bac | integer | Année du baccalauréat (défaut: 1) |
| smig | integer | Salaire minimum interprofessionnel garanti (défaut: 5512) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 3. Demandes
Table qui stocke les demandes de bourses, prêts et aides sociales.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| code | string | Code de la demande |
| etat | string | État de la demande |
| config | json | Configuration de la demande (optionnel) |
| user_id | bigint | ID de l'utilisateur |
| annee_universitaire_id | bigint | ID de l'année universitaire (clé étrangère) |
| demande_type_id | bigint | ID du type de demande (clé étrangère) |
| classification_id | bigint | ID de la classification (clé étrangère) |
| contrat_file | string | Fichier du contrat (optionnel) |
| etat_dossier | string | État du dossier (optionnel) |
| comment_incomplete | text | Commentaire si dossier incomplet (optionnel) |
| comment_incoherent | text | Commentaire si dossier incohérent (optionnel) |
| etat_contrat | string | État du contrat (optionnel) |
| comment_contrat_incomplet | text | Commentaire si contrat incomplet (optionnel) |
| control_fiscal_date | date | Date de contrôle fiscal (optionnel) |
| etat_bourse_stage | integer | État de la bourse de stage (optionnel) |
| etat_aide_sociale | integer | État de l'aide sociale (optionnel) |
| comment_refus_aide_sociale | text | Commentaire si refus d'aide sociale (optionnel) |
| comment_refus_bourse_stage | text | Commentaire si refus de bourse de stage (optionnel) |
| comment_refus_pret | text | Commentaire si refus de prêt (optionnel) |
| comment_refus_bourse_insertion | text | Commentaire si refus de bourse d'insertion (optionnel) |
| comment_refus_bourse | text | Commentaire si refus de bourse (optionnel) |
| bs_nb_jour | integer | Nombre de jours pour bourse de stage (optionnel) |
| bs_start | date | Date de début de bourse de stage (optionnel) |
| bs_end | date | Date de fin de bourse de stage (optionnel) |
| ndec | string | Numéro de décision (optionnel) |
| preparation_bourse_id | integer | ID de préparation de bourse (optionnel) |
| preparation_insertion_id | integer | ID de préparation d'insertion (optionnel) |
| preparation_pret_id | integer | ID de préparation de prêt (optionnel) |
| preparation_aide_sociale_id | integer | ID de préparation d'aide sociale (optionnel) |
| preparation_stage_id | integer | ID de préparation de stage (optionnel) |
| num_bordereau | string | Numéro de bordereau (optionnel) |
| centre_control_fiscal | string | Centre de contrôle fiscal (optionnel) |
| lot | string | Numéro de lot (indexé) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 4. Demande_types
Table qui stocke les types de demandes.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| date_dossier_end | date | Date limite de dépôt du dossier (optionnel) |
| date_complement_end | date | Date limite de complément du dossier (optionnel) |
| date_contrat_pret_end | date | Date limite du contrat de prêt (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 5. Etablissements
Table qui stocke les informations des établissements.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| code | string | Code de l'établissement |
| code_ministre | integer | Code du ministre (optionnel) |
| code_dir_reg | integer | Code de la direction régionale (optionnel) |
| annee_universitaire | integer | Année universitaire (optionnel) |
| code_office | string | Code de l'office (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 6. Historiques
Table qui stocke l'historique des actions et des données historiques des étudiants.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| cin | string | Carte d'identité nationale (optionnel) |
| catb | string | Catégorie de bourse (optionnel) |
| lot | string | Numéro de lot (indexé, optionnel) |
| nom | string | Nom de l'étudiant (optionnel) |
| datnais | string | Date de naissance (optionnel) |
| gouv | string | Gouvernorat (optionnel) |
| sexe | string | Sexe (optionnel) |
| profp | string | Profession du père (optionnel) |
| anet | string | Année d'étude (optionnel) |
| discip | string | Discipline (optionnel) |
| fac | string | Faculté (optionnel) |
| univ | string | Université (optionnel) |
| inf | string | Information inférieure (optionnel) |
| sup | string | Information supérieure (optionnel) |
| enf | string | Nombre d'enfants (optionnel) |
| revp | string | Revenu du père (optionnel) |
| revm | string | Revenu de la mère (optionnel) |
| avis | string | Avis (optionnel) |
| res | string | Résultat (optionnel) |
| moy | string | Moyenne (optionnel) |
| natdec | string | Nature de la décision (optionnel) |
| situa | string | Situation (indexé, optionnel) |
| mbs | string | Montant de la bourse (optionnel) |
| nmb | string | Nombre de mois (optionnel) |
| mf | string | Montant final (optionnel) |
| ndec | string | Numéro de décision (optionnel) |
| dat | string | Date (optionnel) |
| montanttotal | string | Montant total (optionnel) |
| montant | string | Montant (optionnel) |
| pourcentage | string | Pourcentage (optionnel) |
| office | string | Office (optionnel) |
| type | string | Type (optionnel) |
| annee_id | bigint | ID de l'année universitaire (clé étrangère, optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 7. Mandates
Table qui stocke les informations des mandats.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| date_payement | string | Date de paiement (indexé) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 8. Attestations
Table qui stocke les informations des attestations.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| etablissement_id | bigint | ID de l'établissement (clé étrangère) |
| year | string | Année (optionnel) |
| date_enregistrement_bureau_ordre | date | Date d'enregistrement au bureau d'ordre (optionnel) |
| reference_bureau_ordre | string | Référence du bureau d'ordre (optionnel) |
| date_mandat | date | Date du mandat (optionnel) |
| raison_non_retrait | string | Raison de non-retrait (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 9. Attestation_types
Table qui stocke les types d'attestations.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| document_file | string | Fichier du document (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 10. Student_from_mes
Table qui stocke les informations des étudiants provenant du Ministère de l'Enseignement Supérieur.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| NBAC | string | Numéro du baccalauréat (optionnel) |
| CIN | string | Carte d'identité nationale (optionnel) |
| NOM_A | string | Nom en arabe (optionnel) |
| NOM_L | string | Nom en latin (optionnel) |
| JJ | string | Jour de naissance (optionnel) |
| MM | string | Mois de naissance (optionnel) |
| AA | string | Année de naissance (optionnel) |
| CD_LYC | string | Code du lycée (optionnel) |
| CD_GOUV | string | Code du gouvernorat (optionnel) |
| SEX | string | Sexe (optionnel) |
| PROF | string | Profession (optionnel) |
| CODE_FILIERE | string | Code de la filière (optionnel) |
| TOUR | string | Tour (optionnel) |
| annee_bac | integer | Année du baccalauréat (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 11. Diplomes
Table qui stocke les informations des diplômes.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| code | string | Code du diplôme (unique) |
| active | boolean | Statut actif/inactif (défaut: true) |
| name | string | Nom du diplôme |
| name_fr | string | Nom du diplôme en français |
| name_ar | string | Nom du diplôme en arabe |
| nbr_annee_etude | string | Nombre d'années d'étude (optionnel) |
| cycle | string | Cycle (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

## Tables de Fichiers

### 1. Uploaded_files
Table qui stocke les informations des fichiers téléchargés.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| type | string | Type du fichier |
| name | string | Nom du fichier |
| document_file | string | Fichier du document (optionnel) |
| annee_universitaire | integer | Année universitaire (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 2. Mandates_files
Table qui stocke les informations des fichiers de mandats.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| num_dec | string | Numéro de décision |
| annee_gestion | string | Année de gestion |
| path | string | Chemin du fichier |
| date_payment | string | Date de paiement |
| type | string | Type du fichier |
| etat | integer | État (0: en cours, 1: succès, 2: erreur) |
| annee_universitaire_id | bigint | ID de l'année universitaire (clé étrangère) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 3. Decisions_files
Table qui stocke les informations des fichiers de décisions.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| ndec | string | Numéro de décision |
| annee_gestion | string | Année de gestion |
| path | string | Chemin du fichier (peut être null après modification) |
| type | string | Type du fichier |
| etat | integer | État (0: en cours, 1: succès, 2: erreur) |
| annee_universitaire_id | bigint | ID de l'année universitaire (clé étrangère) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 4. Ordonnance_files
Table qui stocke les informations des fichiers d'ordonnance.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| num_dec | string | Numéro de décision |
| annee_gestion | string | Année de gestion |
| path | string | Chemin du fichier (optionnel) |
| date_payment | string | Date de paiement |
| etat | integer | État (0: en cours, 1: succès, 2: erreur) |
| annee_universitaire_id | bigint | ID de l'année universitaire (clé étrangère) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 5. Exported_files
Table qui stocke les informations des fichiers exportés.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| type | string | Type du fichier (optionnel) |
| attached_file | string | Fichier attaché (optionnel) |
| vue | string | Vue (optionnel) |
| etat | string | État (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

## Tables Spécifiques

### 1. Document_predefinis
Table qui stocke les documents prédéfinis.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| code | string | Code du document (unique) |
| type | string | Type du document (optionnel) |
| title | string | Titre du document |
| title_fr | string | Titre du document en français (optionnel) |
| title_ar | string | Titre du document en arabe (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 2. Bourse_alternances
Table qui stocke les informations des bourses en alternance.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| etablissement_id | bigint | ID de l'établissement (clé étrangère) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 3. Fiche_organismes
Table qui stocke les informations des fiches d'organismes.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| organisme | string | Nom de l'organisme |
| code_organisme_id | bigint | ID du code de l'organisme |
| type | string | Type de l'organisme |
| date_emission | date | Date d'émission |
| nb_total | bigint | Nombre total |
| montant_total | float | Montant total |
| tax_total | float | Total des taxes |
| premier | bigint | Premier |
| dernier | bigint | Dernier |
| montant_ttc | float | Montant TTC |
| fichier | string | Fichier (optionnel) |
| annee_universitaire_id | bigint | ID de l'année universitaire (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 4. Stats
Table qui stocke les statistiques.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| num_dec | string | Numéro de décision |
| date_payment | string | Date de paiement |
| annee_universitaire_id | bigint | ID de l'année universitaire (clé étrangère) |
| tranche | string | Tranche |
| nbr_mois | string | Nombre de mois |
| title | string | Titre (optionnel) |
| mnt | string | Montant (optionnel) |
| path | string | Chemin du fichier |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

### 5. Demande_recouvrements
Table qui stocke les informations des demandes de recouvrement.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| type | string | Type de recouvrement |
| student_id | bigint | ID de l'étudiant |
| annee_universitaire | string | Année universitaire |
| status | string | Statut (défaut: "en_cours") |
| montant | integer | Montant (défaut: 0) |
| commentaire | string | Commentaire (optionnel) |
| num_quittance | string | Numéro de quittance (optionnel) |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |
| deleted_at | timestamp | Date de suppression (soft delete) |

## Tables de Gestion des Permissions

### 1. Permissions
Table qui stocke les permissions.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| name | string | Nom de la permission |
| guard_name | string | Nom du guard |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |

### 2. Roles
Table qui stocke les rôles.

| Champ | Type | Description |
|-------|------|-------------|
| id | bigint | Identifiant unique (clé primaire) |
| name | string | Nom du rôle |
| guard_name | string | Nom du guard |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière mise à jour |

### 3. Model_has_permissions
Table pivot qui associe les modèles aux permissions.

| Champ | Type | Description |
|-------|------|-------------|
| permission_id | bigint | ID de la permission |
| model_type | string | Type du modèle |
| model_id | bigint | ID du modèle |

### 4. Model_has_roles
Table pivot qui associe les modèles aux rôles.

| Champ | Type | Description |
|-------|------|-------------|
| role_id | bigint | ID du rôle |
| model_type | string | Type du modèle |
| model_id | bigint | ID du modèle |

### 5. Role_has_permissions
Table pivot qui associe les rôles aux permissions.

| Champ | Type | Description |
|-------|------|-------------|
| permission_id | bigint | ID de la permission |
| role_id | bigint | ID du rôle |

## Tables Système

### 1. Password_resets
Table qui stocke les informations de réinitialisation de mot de passe.

| Champ | Type | Description |
|-------|------|-------------|
| email | string | Adresse email (indexé) |
| token | string | Token de réinitialisation |
| created_at | timestamp | Date de création (optionnel) |

### 2. Telescope_entries
Table qui stocke les entrées de Telescope (outil de débogage).

| Champ | Type | Description |
|-------|------|-------------|
| uuid | string | Identifiant unique (clé primaire) |
| batch_id | string | ID du lot |
| family_hash | string | Hash de la famille (optionnel) |
| should_display_on_index | boolean | Afficher sur l'index |
| type | string | Type d'entrée |
| content | text | Contenu |
| created_at | timestamp | Date de création (optionnel) |

### 3. Telescope_entries_tags
Table pivot qui associe les entrées de Telescope aux tags.

| Champ | Type | Description |
|-------|------|-------------|
| entry_uuid | string | UUID de l'entrée (clé étrangère) |
| tag | string | Tag |

### 4. Telescope_monitoring
Table qui stocke les informations de surveillance de Telescope.

| Champ | Type | Description |
|-------|------|-------------|
| tag | string | Tag (clé primaire) |
