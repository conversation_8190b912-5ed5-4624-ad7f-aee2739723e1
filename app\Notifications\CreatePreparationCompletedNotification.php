<?php

namespace App\Notifications;

use App\Models\ExportedFile;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CreatePreparationCompletedNotification extends Notification
{
    use Queueable;

    public $prepCode;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(string $prepCode)
    {
        $this->prepCode = $prepCode;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
            Log::error($e->getMessage());
        }

        return [
            "title" => "Creation completed",
            "subtitle" => " Creation completed for Preparation code = ". $this->prepCode . " , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Creation Complete ",
            "subtitle_fr" => "Creation de la preparation effectuée code = ". $this->prepCode . " , à : "  .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => " تم إنشاء ",
            "subtitle_ar" => " تم إنشاء الإعداد لكود : " . $this->prepCode . " في  " . Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "thumb-up",
            "avatarAlt" => "Creation",
            "avatarText" => "Creation",
            "avatarColor" => "info",
            "type" => "Verification",
            "target_id" => "",
            "target" => "",
            "model" => "Preparation",
            "url" => '',

        ];
    }
}
