<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreStudentFromMesRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'NBAC' => 'nullable',
            'CIN' => 'nullable',
            'NOM_A' => 'nullable',
            'NOM_L' => 'nullable',
            'JJ' => 'nullable',
            'MM' => 'nullable',
            'AA' => 'nullable',
            'CD_LYC' => 'nullable',
            'CD_GOUV' => 'nullable',
            'SEX' => 'nullable',
            'PROF' => 'nullable',
            'CODE_FILIERE' => 'nullable',
            'TOUR' => 'nullable',
        ];
    }
}
