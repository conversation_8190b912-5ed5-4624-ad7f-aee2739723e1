<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAttestationTypeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string',
            'title' => 'required|string',
            'title_fr' => 'required|string',
            'title_ar' => 'required|string',
            'filigrane' => 'required|string',
            'active' => 'nullable',
            'non_boursier' => 'required',
            'non_pret' => 'required',
            ];
    }
    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
//            'active' => $this->toBoolean($this->active),
            'non_boursier' => $this->toBoolean($this->non_boursier),
            'non_pret' => $this->toBoolean($this->non_pret),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }

}
