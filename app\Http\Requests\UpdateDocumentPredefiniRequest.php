<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDocumentPredefiniRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $methode = $this->method();
        if ($methode === 'PUT') {
            return [
                'code' => 'required|string|unique:document_predefinis,code,'.$this->id,
                'title' => 'required|string',
                'title_fr' => 'required|string',
                'title_ar' => 'required|string',
                'boursier' => 'nullable|string|sometimes',
                'resultat' => 'nullable|integer|sometimes',
            ];
        }

        return [
            'code' => 'required|string|unique:document_predefinis,code,'.$this->id,
            'title' => 'sometimes|required|string',
            'title_fr' => 'sometimes|nullable|string',
            'title_ar' => 'sometimes|nullable|string',
            'boursier' => 'nullable|string|sometimes',
            'resultat' => 'nullable|integer|sometimes',
        ];
    }
}
