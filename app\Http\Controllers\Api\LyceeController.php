<?php

namespace App\Http\Controllers\Api;

use App\Exports\LyceeExport;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreLyceeRequest;
use App\Http\Requests\UpdateLyceeRequest;
use App\Http\Resources\LyceeResource;
use App\Http\Resources\StudentFromMesResource;
use App\Models\Lycee;
use App\Models\StudentFromMes;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;
use Maatwebsite\Excel\Facades\Excel;

class LyceeController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $sort = $request->query('sort', 'asc');
        $page = $request->query('page', 0);
        $pageSize = $request->query('pageSize', 10);
        $sortColumn = $request->query('sortColumn', 'code');

        $searshWord = $request->q;

        $query =  Lycee::with(['gouvernorat']);
        if ($searshWord != ""){
            $query->where('name', 'like', '%'.$searshWord.'%')
                ->orWhere('code', 'like', '%'.$searshWord.'%')
            ;
            $query->orWhereHas('gouvernorat', function($q) use ($searshWord) {
                $q->where(function($q) use ($searshWord) {
                    $q->where('name_fr', 'LIKE', '%' . $searshWord . '%');
                    $q->orWhere('name_ar', 'LIKE', '%' . $searshWord . '%');
                });
            });
        }


        $recordsTotal = $query->count();

        $query->sortable([$sortColumn => $sort])->paginate($pageSize, ['*'], 'page', $page);


        $datas = $query->get();

        return response()->json([
            "params"=> $request->all(),
            "allData"=> [],
            "data"=> LyceeResource::collection($datas),
            "total"=> $recordsTotal
        ],200);
    }
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index2(): AnonymousResourceCollection
    {
        return Cache::remember('Lycee', $this->cache_seconds, function () {
            return LyceeResource::collection(Lycee::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreLyceeRequest $request
     * @return Response
     */
    public function store(StoreLyceeRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Lycee');
        Helpers::clearCacheIdp();

        $g = Lycee::create($data);

        return response(new LyceeResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Lycee $lycee
     * @return LyceeResource
     */
    #[Pure] public function show(Lycee $lycee): LyceeResource
    {
        return new LyceeResource($lycee);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateLyceeRequest $request
     * @param Lycee $lycee
     * @return LyceeResource
     */
    public function edit(UpdateLyceeRequest $request, Lycee $lycee): LyceeResource
    {
        $data = $request->validated();

        Cache::forget('Lycee');
        Helpers::clearCacheIdp();

        $lycee->update($data);

        return new LyceeResource($lycee);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Lycee $lycee
     * @return Response
     */
    public function destroy(Lycee $lycee): Response
    {
        if ($lycee->studentFromMes->count()){
            throw ValidationException::withMessages(["Ce lycée est utilisé par d'autres tables"]);
        }
        Cache::forget('Lycee');
        Helpers::clearCacheIdp();

        $lycee->delete();

        return response("", 204);
    }

    public function exportExcel(Request $request)
    {
        // cache in export
        $export = new LyceeExport();
        return Excel::download($export, 'lycees.xlsx');
    }
}
