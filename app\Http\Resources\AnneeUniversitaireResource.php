<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class AnneeUniversitaireResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'smig' => $this->smig,
            'start' => $this->start?->format('Y-m-d'),
            'startFrFormat' => $this->start?->format('d/m/Y'),
            'end' => $this->end?->format('Y-m-d'),
            'endFrFormat' => $this->end?->format('d/m/Y'),
            'active' => $this->active,
//            'filieres' => $this->filieres,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'anneeBac' => $this->anneeBac,
            'annee_bac' => $this->annee_bac,
            'order' => $this->order,
        ];
    }
}
