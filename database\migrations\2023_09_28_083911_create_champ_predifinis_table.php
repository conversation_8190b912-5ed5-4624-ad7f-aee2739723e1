<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('champ_predefinis', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('label')->nullable();
            $table->string('label_fr')->nullable();
            $table->string('label_ar')->nullable();
            $table->string('name')->nullable();
            $table->string('type');
            $table->string('help')->nullable();
            $table->string('help_fr')->nullable();
            $table->string('help_ar')->nullable();
            $table->boolean('showLabel')->nullable();
            $table->boolean('required')->nullable();
            $table->boolean('affectClassification')->nullable();
            $table->json('choices')->nullable();

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('champ_predefinis');
    }
};
