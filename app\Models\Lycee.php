<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use OwenIt\Auditing\Contracts\Auditable;

class Lycee extends Model implements Auditable
{
    use Sortable;
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }


    public $sortable = [
        'name',
        'code',
        'code_gouv',
    ];

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'active',
        'code',
        'code_gouv',
    ];


    public function gouvernorat() : BelongsTo
    {
        return $this->belongsTo(Gouvernorat::class,"code_gouv","code");
    }

    public function studentFromMes() : HasMany
    {
        return $this->hasMany(StudentFromMes::class,'CD_LYC','code');
    }
}
