<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demande_types', function (Blueprint $table) {
            $table->date('date_dossier_end')->nullable();
            $table->date('date_complement_end')->nullable();
            $table->date('date_contrat_pret_end')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demande_types', function (Blueprint $table) {
            $table->dropColumn('date_dossier_end');
            $table->dropColumn('date_complement_end');
            $table->dropColumn('date_contrat_pret_end');
        });
    }
};
