<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('decisions_files', function (Blueprint $table) {
            $table->id();
            $table->string('ndec');
            $table->string('annee_gestion');
            $table->string('path');
            $table->string('type');
            $table->integer('etat'); // 0:encours, 1:success, 2:error
            $table->unsignedBigInteger('annee_universitaire_id');
            $table->timestamps();
            $table->foreign('annee_universitaire_id','decisions_files_annee_universitaire_id_foreign')
            ->references('id')
            ->on('annee_universitaires');
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('decisions_files');
    }
};
