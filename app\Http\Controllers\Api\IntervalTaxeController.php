<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\IntervalTaxeRequest;
use App\Http\Resources\IntervalTaxeResource;
use App\Models\IntervalTaxe;
use Illuminate\Http\Response;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Cache;

class IntervalTaxeController extends Controller
{
    protected $cache_seconds = 900;

   /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('IntervalTaxe', $this->cache_seconds, function () {
            return IntervalTaxeResource::collection(IntervalTaxe::orderBy('min')->get());
        });
    }

        /**
     * Store a newly created resource in storage.
     *
     * @param IntervalTaxeRequest $request
     * @return Response
     */
    public function store(IntervalTaxeRequest $request)
    {
        $data = $request->validated();

        $test1 = IntervalTaxe::where('min', '<', $request->input('min'))
        ->where('max', '>', $request->input('min'))
        ->exists();

        $test2 = IntervalTaxe::where('min', '<', $request->input('max'))
        ->where('max', '>', $request->input('max'))
        ->exists();

        if ($test1) {
            return response()->json(['errors' => ['min' => "La valeur de min existe déjà dans une plage existante."]], 422);
        }
        if ($test2) {
            return response()->json(['errors' => ['max' => "La valeur de max existe déjà dans une plage existante."]], 422);
        }

        Cache::forget('IntervalTaxe');
        Helpers::clearCacheIdp();

        $intervalTaxe = IntervalTaxe::create($data);

        return response(new IntervalTaxeResource($intervalTaxe) , 201);
    }

        /**
     * Update the specified resource in storage.
     *
     * @param IntervalTaxeRequest $request
     * @param IntervalTaxe $intervalTaxe
     * @return IntervalTaxeResource
     */
    public function edit(IntervalTaxeRequest $request, IntervalTaxe $intervalTaxe)
    {
        $data = $request->validated();
        $test1 = IntervalTaxe::where('id', '<>', $request->input('id'))
        ->where('min', '<', $request->input('min'))
        ->where('max', '>', $request->input('min'))
        ->exists();

        $test2 = IntervalTaxe::where('id', '<>', $request->input('id'))
        ->where('min', '<', $request->input('max'))
        ->where('max', '>', $request->input('max'))
        ->exists();

        if ($test1) {
            return response()->json(['errors' => ['min' => "La valeur de min existe déjà dans une plage existante."]], 422);
        }
        if ($test2) {
            return response()->json(['errors' => ['max' => "La valeur de max existe déjà dans une plage existante."]], 422);
        }

        Cache::forget('IntervalTaxe');
        Helpers::clearCacheIdp();

        $intervalTaxe->update($data);

        return new IntervalTaxeResource($intervalTaxe);
    }

        /**
     * Remove the specified resource from storage.
     *
     * @param number $id
     * @return Response
     */
    public function destroy($id): Response
    {
        Cache::forget('IntervalTaxe');
        Helpers::clearCacheIdp();

        IntervalTaxe::find($id)->delete();
        return response("success", 204);
    }
}
