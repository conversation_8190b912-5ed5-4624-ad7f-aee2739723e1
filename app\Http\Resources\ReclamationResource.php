<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class ReclamationResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'reclamation_type_id' => $this->reclamation_type_id,
            'reclamationType' => $this->reclamationType,
            'detail' => $this->detail,
            'response' => $this->response,
            'etat' => $this->etat,
            'document_file_url' => $this->document_file_url,
            'document_file' => $this->document_file,
            'document_file_url2' => $this->document_file_url2,
            'document_file2' => $this->document_file2,
            'student_id' => $this->student_id,
            'student' => $this->student,
            'created_at' => $this->created_at->format('Y-m-d'),
            'created_at_fr' => $this->created_at->format('d/m/Y H:i'),
        ];
    }
}
