<?php
namespace App\Exports;

use App\Models\Filiere;
use App\Models\Orientation;
use App\Models\StudentFromMes;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class StudentFromMesImport implements ToCollection, WithHeadingRow
{

    private int $annee_bac;
    private int $annee_universitaire;
    public function __construct($annee_bac){
        $this->annee_bac = $annee_bac;
        $this->annee_universitaire = DB::table('annee_universitaires')->where('annee_bac',$annee_bac)->first()->id;
    }
    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'nbac' => 'required',
                'cin' => 'required',
                'nom_a' => 'required',
                'nom_l' => 'required',
                'jj' => 'required',
                'mm' => 'required',
                'aa' => 'required',
                'cd_lyc' => 'required',
                'cd_gouv' => 'required',
                'sex' => 'required',
                'prof' => 'required',
                'code_filiere' => 'required',
                'tour' => 'required',
            ],
            [],
            [
                'nbac' => '(nbac)',
                'cin' => '(cin)',
                'nom_a' => '(nom_a)',
                'nom_l' => '(nom_l)',
                'jj' => '(jj)',
                'mm' => '(mm)',
                'aa' => '(aa)',
                'cd_lyc' => '(cd_lyc)',
                'cd_gouv' => '(cd_gouv)',
                'sex' => '(sex)',
                'prof' => '(prof)',
                'code_filiere' => '(code_filiere)',
                'tour' => '(tour)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.nbac' => 'required|string',
            '*.cin' => 'required|string',
        ])->validate();

        foreach ($rows as $row) {
            $st = StudentFromMes::updateOrCreate([
                'NBAC'=>$row['nbac'] ,
                'annee_bac'=>$this->annee_bac
            ],[
                'NBAC' => $row['nbac'],
                'CIN' => $row['cin'],
                'NOM_A' => $row['nom_a']??'',
                'NOM_L' => $row['nom_l']??'',
                'JJ' => $row['jj']??'',
                'MM' => $row['mm']??'',
                'AA' => $row['aa']??'',
                'CD_LYC' => $row['cd_lyc']??'',
                'CD_GOUV' => $row['cd_gouv']??'',
                'SEX' => $row['sex']??'',
                'PROF' => $row['prof']??'',
                'CODE_FILIERE' => $row['code_filiere']??'',
                'TOUR' => $row['tour']??'',
                'annee_bac' => $this->annee_bac,
            ]);

            Orientation::updateOrCreate([
                'student_id'=>$st->id,
                'code_filiere' => $row['code_filiere'],
            ],[
                'student_id'=>$st->id,
                'annee_universitaire'=>$this->annee_universitaire,
                'code_filiere' => $row['code_filiere'],
                'tour' => $row['tour']
            ]);
        }
    }
}
