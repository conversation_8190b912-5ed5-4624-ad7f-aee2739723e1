<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class StoreDiplomeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'code' => 'required|string|unique:diplomes,code',
            'active' => 'required|boolean',
            'troisieme_cycle' => 'required|boolean',
            'nbr_annee_etude' => 'nullable|numeric|between:0,10',
            'cycle' => 'nullable|numeric|between:1,5',
            ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
            'troisieme_cycle' => Helpers::toBoolean($this->troisieme_cycle),
        ]);
    }




}
