<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateUniversiteRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'code_gouv' => 'required|integer',
            'active' => 'required|boolean',
            'code' => 'required|integer|unique:universites,code,'.$this->id,
        ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }

}
