<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Universite extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'code_gouv',
        'active',
        'code',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function gouvernorat() : BelongsTo
    {
        return $this->belongsTo(Gouvernorat::class,"code_gouv","code");
    }


    public function etablissements() : HasMany
    {
        return $this->hasMany(Etablissement::class,'code_univ','code');
    }
}
