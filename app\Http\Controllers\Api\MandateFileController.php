<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMandateFileRequest;
use App\Models\AnneeUniversitaire;
use App\Models\Mandate;
use App\Models\MandatesFile;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use \Mpdf\Mpdf as PDF;
use NumberToWords\NumberToWords;

class MandateFileController extends Controller
{
    public function index(Request $request, $type)
    {
        $files =  MandatesFile::where('type', $type)
            ->when(
                $request->has('annee_gestion'),
                function ($query) use ($request) {
                    return $query->where('annee_gestion',  'like', '%' . $request->annee_gestion . '%');
                }
            )
            ->when(
                $request->has('ndec'),
                function ($query) use ($request) {
                    return $query->where('num_dec',  'like', '%' . $request->ndec . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id',  $request->annee_universitaire_id);
                }
            )
            ->orderBy('id', 'desc')
            ->with('annee_universitaire')
            ->paginate(
                $request->input('perPage') ?? config('constants.pagination'),
            );
        return response()->json($files, 200);
    }

    public function generatePDF(StoreMandateFileRequest $request)
    {
        $formatted_date = Carbon::createFromFormat('d/m/Y', $request->date_payment)->format('Y-m-d');
        $nbr = Mandate::whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)
            ->where('num_dec', $request->ndec)
            ->count();
        if($nbr==0){
            return response()->json([
                'message'=>"N'existe pas des mandats avec cette date de paiement",
                'errors'=> [
                    "date_payment" => [
                       "N'existe pas des mandats avec cette date de paiement"
                    ],
                 ]
            ],422);
        }
        $this->etat_recap($request->date_payment, $request->annee_universitaire_id, $request->annee_gestion, $request->date_payment, $request->ndec, $request->type);
        return response()->json("success", 200);
    }

    public function downloadExistingPDF(Request $request)
    {
        $path = $request->fileName;
        return Storage::download($path, $request->filename);
//        return response()->download(storage_path('app/' . $request->fileName));
    }

    function etat_recap($date_payement, $annee_universitaire_id, $annee_gestion, $date_payment, $ndec, $type)
    {
        $annee_universitaire = AnneeUniversitaire::find($annee_universitaire_id)->title;

        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');

        // Format the date before comparing
        $formatted_date = Carbon::createFromFormat('d/m/Y', $date_payement)->format('Y-m-d');

        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<br><br><P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>Etat Récapitulatif des Ordonnances du Payement du " . $formatted_date . "<br>Année universitaire " . $annee_universitaire . "<br>Gestion " . $annee_gestion . "</P>");

        $mpdf->WriteHTML("<br><br><table align=center border=1 style='width:90%;border-collapse:collapse;'><thead>");
        $mpdf->WriteHTML("<tr><th>N° Départ Emission</th><th>N° Fin Emission</th><th>Nbr des Mandats</th><th>Net</th></tr></thead>");

        // Count
        $nbr = Mandate::whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)->count();

        // Minimum NUMEMISSIO
        $min = Mandate::select(DB::raw('MIN(CAST(num_emission AS INTEGER)) as min'))
            ->whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)
            ->value('min');

        // Maximum NUMEMISSIO
        $max = Mandate::select(DB::raw('MAX(CAST(num_emission AS INTEGER)) as max'))
            ->whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)
            ->value('max');

        // Sum NETAPAYER
        $net = Mandate::select(DB::raw('SUM(`net_a_payer`) as net'))
            ->whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)
            ->value('net');

        $mpdf->WriteHTML("<tr><td>" . $min . "</td><td>" . $max . "</td><td>" . $nbr . "</td><td>" . number_format($net, 3, ',', ' ') . "</td></tr></table><br><br>");

        // $f = new \NumberFormatter( locale_get_default(), \NumberFormatter::SPELLOUT );
        $numberToWords = new NumberToWords();
        $words = $numberToWords->getNumberTransformer('fr')->toWords($net );
        $mpdf->WriteHTML("Arrêter la présente état à la somme de: " . $words . " Dinars<br><br><br><p align=center>P/LE MINISTRE</p><br><br><br><br><br><div style='text-align:justify;'>Vu, Approuvée et Liquides la présente Depense à la Somme de <b>" . $words . " Dinars</b><br>Sousse le ...........................</div>");

        // $mpdf->Output('etat_recap' . $date_payement . '.pdf', 'I');

        $filename = 'recaps/etat_recap'  . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        $row = MandatesFile::create([
            'num_dec'  => $ndec,
            'annee_gestion'  => $annee_gestion,
            'type'  => $type,
            'etat' => 0,
            'annee_universitaire_id' => $annee_universitaire_id,
            'date_payment' => $date_payment,
            "path" => $filename
        ]);
    }
}
