<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;


class InternationalExport implements /*FromCollection, WithHeadings, WithMapping,WithCustomStartCell, WithColumnWidths,*/ WithEvents, FromView
{
    protected $data;
    protected $year;
    protected $office;
    protected $date_export;

    public function __construct($data,$year, $office)
    {
        $this->data = $data;
        $this->year = $year;
        $this->office = $office;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');
    }

    /*public function collection()
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'CIN',
            'Nom',
            'Date de naissance',
            'Montant total',
            'Prénom',
            'Titre'
        ];
    }

    public function map($row): array
    {
        return [
            $row->cin,
            $row->nom,
            $row->datnais,
            $row->montanttotal,
            optional($row->user)->prenom,
            optional($row->etablissement)->title
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 25,
            'C' => 25,
            'D' => 25,
            'E' => 25,
            'F' => 25,
        ];
    }

    public function startCell(): string
    {
        return 'A3'; // Start the table at cell A3 (leave room for description)
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->setCellValue('A1', 'Description:'); // Set the description
                $event->sheet->setCellValue('A2', 'This is a description of the exported data.');
            },
        ];
    }*/

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }


    public function view(): View
    {
        return view('statistiques.internationalStat', [
            'international' => $this->data,
            'year' => $this->year,
            'office' => $this->office,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title

        ]);
    }
}

