<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use App\Models\AttestationType;
use App\Models\Demande;
use App\Models\DemandeType;
use App\Models\Preparation;
use App\Models\RetraitInscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Str;

class PreparationExport implements FromCollection, WithHeadings
{
//    use Exportable;

    private mixed $finalResult;
    private mixed $id;
    private mixed $changeStatus;

    public function __construct($finalResult)
    {
        $this->finalResult = $finalResult;
    }


    public function headings(): array
    {
        return [

            'CIN',
            'CATB',
            'LOT',
            'NOM',
            'DAT_NAISS',
            'GOUV_N',
            'SEXE',
            'PROFP',
            'ANET',
            'DIS',
            'FAC',
            'UNIV',
            'REVP',
            'REVM',
            'AVIS',
            'RES',
            'MOY',
            'DECIS',

        ];
    }

    public function collection()
    {
        return Collection::make($this->finalResult);
    }


}
