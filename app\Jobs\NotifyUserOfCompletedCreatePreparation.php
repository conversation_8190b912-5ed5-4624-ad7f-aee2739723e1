<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Models\Preparation;
use App\Notifications\CheckAllAndCreateStudentCompletedNotification;
use App\Notifications\CreatePreparationCompletedNotification;
use App\Notifications\ImportCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedCreatePreparation implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $prepId;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(Admin $user, int $prepId)
    {
        $this->user = $user;
        $this->prepId = $prepId;
    }

    public function handle()
    {
        $prep = Preparation::find($this->prepId);
        $prepCode = $prep->code . sprintf('%03d', $prep->ndec );
        $this->user->notify(new CreatePreparationCompletedNotification($prepCode));
    }
}
