<?php

use App\Http\Controllers\Api\EtablissementController;
use App\Http\Resources\ReclamationResource;
use App\Models\Demande;
use App\Models\DemandeAnneeEtude;
use App\Models\Etablissement;
use App\Models\Reclamation;
use App\Models\Universite;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {

    return view('welcome');
});
Route::get('/set_annee_etude_demande', function () {
  Demande::chunk(500, function ($demandes) {
    foreach ($demandes as $demande) {
      if ($demande->etudiant_annee_universitaire_id !== null) {
        continue;
      }

      $anneeEtude = DemandeAnneeEtude::with('etablissement')
        ->where('demande_id', $demande->id)
        ->where('annee_universitaire_id', $demande->annee_universitaire_id)
        ->first();

      if ($anneeEtude) {
        $demande->etudiant_annee_universitaire_id = $anneeEtude->id;
        $demande->save();
      }
    }
  });
  return view('welcome');
});

Route::get('/set_code_etab_demande', function () {
    $demandes = Demande::all();
    foreach ($demandes as $demande) {
        $demande->current_code_etab = $demande->demande_last_annee_etude->code_etab;
        $demande->save();
    }
//    dd($demandes->pluck('current_code_etab'));
    return view('welcome');
});
Route::get('/set_code_office_universite', function () {
    $demandes = Universite::all();
    foreach ($demandes as $demande) {
//        dd($demande->etablissements[0]->code_office);
        $demande->code_office = count($demande->etablissements) ? $demande->etablissements[0]->code_office : null;
        $demande->save();
    }
    dd($demandes->pluck('code', 'code_office'));
//    return view('welcome');
});
Route::get('/set_code_gouv_etablissements', function () {
    $etablissements = Etablissement::all();
    foreach ($etablissements as $etab) {
//        dd($demande->etablissements[0]->code_office);
        $etab->code_gouv = $etab->universite->code_gouv ?? null;
        $etab->save();
    }
    dd($etablissements);
//    return view('welcome');
});
Route::get('phpmyinfo', function () { phpinfo(); })->name('phpmyinfo');

Route::get('get_boursier_etablissement', [EtablissementController::class, 'get_boursier_etablissements']);
