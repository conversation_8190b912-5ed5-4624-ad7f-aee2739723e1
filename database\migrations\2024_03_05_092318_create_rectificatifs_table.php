<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rectificatifs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('rectificatif_numero_id')->nullable();
            $table->string('cin')->nullable();
            $table->string('catb')->nullable();
            $table->string('lot')->nullable();
            $table->string('nom')->nullable();
            $table->string('datnais')->nullable();
            $table->string('gouv')->nullable();
            $table->string('sexe')->nullable();
            $table->string('profp')->nullable();
            $table->string('anet')->nullable();
            $table->string('discip')->nullable();
            $table->string('fac')->nullable();
            $table->string('univ')->nullable();
            $table->string('inf')->nullable();
            $table->string('sup')->nullable();
            $table->string('enf')->nullable();
            $table->string('revp')->nullable();
            $table->string('revm')->nullable();
            $table->string('avis')->nullable();
            $table->string('res')->nullable();
            $table->string('moy')->nullable();
            $table->string('natdec')->nullable();
            $table->string('situa')->nullable();
            $table->string('mbs')->nullable();
            $table->string('nmb')->nullable();
            $table->string('mf')->nullable();
            $table->string('ndec')->nullable();
            $table->string('dat')->nullable();
            $table->string('montanttotal')->nullable();
            $table->string('pourcentage')->nullable();
            $table->string('office')->nullable();
            $table->string('type')->nullable();
            $table->unsignedBigInteger('annee_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('annee_id')
                ->references('id')
                ->on('annee_universitaires');
            $table->foreign('rectificatif_numero_id')
                ->references('id')
                ->on('rectificatif_numeros');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rectificatifs');
    }
};
