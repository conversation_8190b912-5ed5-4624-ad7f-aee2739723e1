<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class CatbResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'name' => $this->name,
            'tunisien' => $this->tunisien,
            'troisieme_cycle' => $this->troisieme_cycle,
            'active' => $this->active,
            'type_etude' => $this->type_etude,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
//            'demandes' => DemandeResource::collection($this->demandes),
        ];
    }
}
