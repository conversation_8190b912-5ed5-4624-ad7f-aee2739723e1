<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class AttestationStatExport implements FromView, WithEvents
{
    protected $attestations;
    protected $types_labels;
    protected $year;
    protected $date_export;

    public function __construct($attestations,$types_labels, $year)
    {
        $this->attestations = $attestations;
        $this->types_labels = $types_labels;
        $this->year = $year;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');

    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }

    public function view(): View
    {
        return view('statistiques.statAttestation', [
            'attestations' => $this->attestations,
            'types_labels' => $this->types_labels,
            'date_export' => $this->date_export,
            'year' => $this->year,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title
        ]);
    }
}
