<?php

namespace App\Models;

use App\Models\Scopes\AbsenceScope;
use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Absence extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels {
        AuditableTrait::transformAudit as parentTransformAudit;
    }
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }


    protected $fillable = ['student_id','annee_universitaire_id', 'nb_jours','etablissement_id','student_name','student_cin'];

    protected static function booted()
    {
        static::addGlobalScope(new AbsenceScope());
    }

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    public function student() : BelongsTo
    {
        return $this->setConnection( config('database.secondConnection') )->belongsTo(User::class, 'student_id');
    }

    public function annee_universitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,'annee_universitaire_id');
    }

    public function etablissement() : BelongsTo
    {
        return $this->belongsTo(Etablissement::class,'etablissement_id');
    }
}
