<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class FiliereResource extends JsonResource
{
    public static $wrap = false;
    public bool $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_fr' => $this->name_fr,
            'name_ar' => $this->name_ar,
            'code' => $this->code,
            'code_etab' => $this->code_etab,
            'code_diplome' => $this->code_diplome,
//            'etablissement' => $this->etablissement,
            'annee_universitaire' => $this->annee_universitaire,
//            'anneeUniversitaire' => $this->anneeUniversitaire,
//            'diplome' => $this->diplome,
//            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
