<?php

namespace App\Models;

use App\Traits\PivotSyncAndAudit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
class AttestationType extends Model implements Auditable
{
    use HasFactory;
    use \OwenIt\Auditing\Auditable,PivotSyncAndAudit;
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        //$data = $this->parentTransformAudit($data);
        //$data = $this->transformAuditRelatedModels($data);
        return $data;
    }

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    const CODE_LIST = [
        'bourses_universitaires' => 'bourses_universitaires',
        'nouveau_bachelier' => 'nouveau_bachelier',
        'en_cours_d_etude' => 'en_cours_d_etude',
        'renouvellement' => 'renouvellement',
        'doctorat' => 'doctorat',
        'master' => 'master',
        'aide_sociale' => 'aide_sociale',
        'bourses_de_stage' => 'bourses_de_stage',
    ];

    protected $fillable = [
        'code',
        'title',
        'title_fr',
        'title_ar',
        'filigrane',
        'langues',
        'active',
        'non_boursier',
        'non_pret',
        'parent_id',
    ];

    protected $with = [
        'parent',
        'documentsAttestations',
        'offices'
    ];
    protected $casts = [
        //'active' => 'boolean',
        'non_boursier' => 'boolean',
        'non_pret' => 'boolean',
    ];


    public function documentsAttestations(): BelongsToMany
    {
        return $this->belongsToMany(DocumentsAttestation::class/*)->using(AttestationTypeHasAttestationTypeDocuments::class);//*/, 'attest_type_has_doc_attest', 'attestation_types_id', 'documents_attestations_id');
    }

    public function offices() : BelongsToMany
    {
        return $this->belongsToMany(Office::class, 'attestation_type_has_offices', 'attestation_type_id', 'office_id');
    }

    public function attestations(): HasMany
    {
        return $this->hasMany(Attestation::class, 'attestation_types_id');
    }
    public function parent(): BelongsTo
    {
        return $this->belongsTo(__CLASS__, 'parent_id');
    }

    public function fils(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id');
    }
}
