<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAnneeBacRequest;
use App\Http\Requests\UpdateAnneeBacRequest;
use App\Http\Resources\AnneeBacResource;
use App\Models\AnneeBac;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class AnneeBacController extends Controller
{
    protected $cache_seconds = 900;
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('AnneeBac', $this->cache_seconds, function () {
            return AnneeBacResource::collection(AnneeBac::orderBy('title', 'DESC')->get());
        });
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(StoreAnneeBacRequest $request): Response
    {
        Cache::forget('AnneeBac');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $g = AnneeBac::create($data);

        return response(new AnneeBacResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param AnneeBac $anneeBac
     * @return AnneeBacResource
     */
    #[Pure] public function show(AnneeBac $anneeBac): AnneeBacResource
    {
        return new AnneeBacResource($anneeBac);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateAnneeBacRequest $request
     * @param AnneeBac $anneeBac
     * @return AnneeBacResource
     */
    public function edit(UpdateAnneeBacRequest $request, AnneeBac $anneeBac): AnneeBacResource
    {
        Cache::forget('AnneeBac');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $anneeBac->update($data);

        return new AnneeBacResource($anneeBac);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param AnneeBac $anneeBac
     * @return Response
     */
    public function destroy(AnneeBac $anneeBac): Response
    {
        if($anneeBac->studentFromMess->count()){
            throw ValidationException::withMessages(["Cette anneeBac est utilisé par d'autres tables"]);
        }

        if($anneeBac->anneeUniversitaire){
            throw ValidationException::withMessages(["Cette annee de Bac est utilisé par une année universitaire"]);
        }

        Cache::forget('AnneeBac');
        Helpers::clearCacheIdp();

        $anneeBac->delete();
        return response("", 204);
    }
}
