<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('documents_attestation_uploads', function (Blueprint $table) {
            $table->id();

            $table->string('attached_file')->nullable();
            $table->string('etat')->nullable();
            $table->boolean('active')->nullable();
            $table->unsignedBigInteger('attestation_id')->nullable();
            $table->unsignedBigInteger('document_attestation_id')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('attestation_id')
                ->references('id')
                ->on('attestations');

            $table->foreign('document_attestation_id')
                ->references('id')
                ->on('documents_attestations');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('documents_attestation_uploads');
    }
};
