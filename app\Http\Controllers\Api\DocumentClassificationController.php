<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDocumentClassificationRequest;
use App\Http\Requests\UpdateDocumentClassificationRequest;
use App\Http\Resources\DocumentClassificationResource;
use App\Http\Requests\StoreClassificationRequest;
use App\Http\Requests\UpdateClassificationRequest;
use App\Models\Classification;
use App\Models\DocumentClassification;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class DocumentClassificationController extends Controller
{
//    public function __construct()
//    {
////        $this->middleware('permission:product-create', ['only' => ['create','store']]);
//
//        // $this->middleware('role:admin');
//    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return DocumentClassificationResource::collection(DocumentClassification::query()->with('classification')->get());
    }

    /**
     * Display a listing of the resource.
     *
     * @param Classification $classification
     * @return AnonymousResourceCollection
     */
    public function byClassification(Classification $classification): AnonymousResourceCollection
    {
        return DocumentClassificationResource::collection(DocumentClassification::where('classification_id', $classification->id)->with('classification')->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDocumentClassificationRequest $request
     * @return Response
     */
    public function store(StoreDocumentClassificationRequest $request): Response
    {
        $data = $request->validated();
        $file_name = null;
        if($request->hasFile('document_file')){
            $file = $request->file('document_file');
            $file_name='document_'.time().'.'.$request->document_file->extension();
            $file->storeAs('/uploads/document_classification_files/'.$request->classification_id.'/', $file_name);
        }
        $data['document_file'] = $file_name;
        $documentClassification = DocumentClassification::create($data);

        $documentClassification->update([
            'document_file' => $file_name
        ]);

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();

        return response(new DocumentClassificationResource($documentClassification) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateDocumentClassificationRequest $request
     * @param DocumentClassification $documentClassification
     * @return Response
     */
    public function edit(UpdateDocumentClassificationRequest $request,DocumentClassification $documentClassification): Response
    {
        $data = $request->validated();

        if($request->hasFile('document_file')){
            if($documentClassification->document_file){
                $old_path='/uploads/document_classification_files/'.$documentClassification->classification_id.'/'.$documentClassification->document_file;
                if(Storage::exists($old_path)){
                    Storage::delete($old_path);
                }
            }

            $file = $request->file('document_file');
            $file_name='document_'.time().'.'.$request->document_file->extension();

            $file->storeAs('/uploads/document_classification_files/'.$documentClassification->classification_id.'/', $file_name);
        }else{
            $file_name=$documentClassification->document_file;
        }
        $data['document_file'] = $file_name;
        $documentClassification->update($data);

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();

        return response(new DocumentClassificationResource($documentClassification) , 201);

    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateDocumentClassificationRequest $request
     * @param DocumentClassificationResource $documentClassification
     * @return Response
     */
    public function update(UpdateDocumentClassificationRequest $request, DocumentClassificationResource $documentClassification): Response
    {
        $data = $request->validated();

        $documentClassification->update($data);

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();
        return response(new DocumentClassificationResource($documentClassification) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param DocumentClassification $documentClassification
     * @return Response
     */
    public function destroy(DocumentClassification $documentClassification)
    {

//        if($documentClassification->documentsClassificationsDemands->count()){
//            throw ValidationException::withMessages(["Ce document est utilisé par d'autres tables"]);
//        }
        $documentClassification->documentsClassificationsDemands()->delete();
        $documentClassification->delete();
        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();
        return response("ok", 204);
    }

    public function getDocumentClassification(Request $request)
    {
        $request->validate([
            'id' => 'required',
        ]);

        $doc = DocumentClassification::findOrFail($request->id);
        if (!$doc) {
            return response()->json('Document not found',404);
        }
        $filename = $doc->document_file;
        $path = 'uploads/document_classification_files/'.$doc->classification_id.'/'.$filename;
        if (!Storage::exists($path)) {
            return response()->json(Storage::path($path), 404);
        }
        return Storage::download($path, $filename);

    }

}
