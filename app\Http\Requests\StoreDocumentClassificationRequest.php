<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDocumentClassificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string',
            'title' => 'required|string',
            'title_fr' => 'required|string',
            'title_ar' => 'required|string',
            'obligatoire' => 'nullable|boolean',
            'classification_id' => 'required|integer',
            'boursier' => 'nullable|string|sometimes',
            'resultat' => 'nullable|integer|sometimes',
            'document_file'=> 'nullable|sometimes|mimes:png,jpg,jpeg,csv,txt,xlx,xls,xlsx,pdf',

        ];
    }

    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'obligatoire' => $this->toBoolean($this->obligatoire),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
