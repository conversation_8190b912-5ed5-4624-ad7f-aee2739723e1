<?php

namespace App\Models\Scopes;

use App\Models\AttestationType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class AttestationScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $user = Auth::user();
        if($user  && !($user->id == 1))
        {

            // if user works in a direction
            if ($user->office && $user->office->parent_id !== null) {
                # get the attestations visible by the user's office
                $builder->where('office_id', $user->office->id);

            }


        }

    }
}
