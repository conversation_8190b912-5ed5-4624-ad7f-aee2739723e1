<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_predefinis', function (Blueprint $table) {
            $table->id();
            $table->boolean('active')->default(true);
            $table->longText('text');
            $table->longText('text_fr');
            $table->longText('text_ar');
            $table->string('type');
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('message_predefinis');
    }
};
