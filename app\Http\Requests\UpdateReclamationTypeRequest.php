<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateReclamationTypeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $methode = $this->method();
        if ($methode === 'PUT') {
            return [
                'code' => 'required|string|unique:classifications,code,'.$this->id,
                'title' => 'required|string',
                'title_fr' => 'nullable|string',
                'title_ar' => 'nullable|string',
                'parent_id' => 'nullable|integer',
            ];
        }

        return [
            'code' => 'sometimes|required|string|unique:classifications,code,'.$this->id,
            'title' => 'sometimes|required|string',
            'title_fr' => 'sometimes|nullable|string',
            'title_ar' => 'sometimes|nullable|string',
            'parent_id' => 'sometimes|nullable|integer',
        ];
    }
}
