<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mandates', function (Blueprint $table) {
            $table->id();
            $table->string('cin')->nullable();
            $table->string('nom')->nullable();
            $table->string('fac')->nullable();
            $table->string('univ')->nullable();
            $table->string('annee')->nullable();
            $table->string('num_dec')->nullable();
            $table->string('nbre_mois')->nullable();
            $table->string('net_a_payer')->nullable();
            $table->string('num_emission')->nullable();
            $table->string('date_payement')->nullable();
            $table->string('date_fin_validite')->nullable();
            $table->string('type')->nullable();
            $table->unsignedBigInteger('annee_universitaire_id')->nullable();
            $table->timestamps();
            $table->foreign('annee_universitaire_id','mandates_annee_universitaire_id_foreign')
            ->references('id')
            ->on('annee_universitaires');
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mandates');
    }
};
