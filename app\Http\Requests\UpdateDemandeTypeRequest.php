<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDemandeTypeRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => 'sometimes|required|string',
            'group' => 'nullable|string',
            'code' => 'nullable|string',
            'type' => 'required|string',
            'title_fr' => 'sometimes|required|string',
            'title_ar' => 'sometimes|required|string',
            'active' => 'sometimes|nullable|boolean',
            'bourse' => 'sometimes|nullable|boolean',
            'aide_sociale' => 'sometimes|nullable|boolean',
            'bourses_de_stage' => 'sometimes|nullable|boolean',
            'bourse_insertion' => 'sometimes|nullable|boolean',
            'pret' => 'sometimes|nullable|boolean',
            'order' => 'sometimes|nullable|integer',
            'parent_id' => 'sometimes|nullable|integer',
            'start' => 'sometimes|nullable|date_format:Y-m-d',
            'end' => 'sometimes|nullable|date_format:Y-m-d',
            'date_dossier_end' => 'sometimes|nullable|date_format:Y-m-d',
            'date_complement_end' => 'sometimes|nullable|date_format:Y-m-d',
            'date_contrat_pret_end' => 'sometimes|nullable|date_format:Y-m-d',
            'diplomes' => 'sometimes|array',
            'visibility' => 'required|string',
            'is_parent_etranger' => 'nullable|boolean',
        ];
    }

    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_parent_etranger' => $this->toBoolean($this->is_parent_etranger),
            'active' => $this->toBoolean($this->active),
            'bourse_insertion' => $this->toBoolean($this->bourse_insertion),
            'pret' => $this->toBoolean($this->pret),
            'bourse' => $this->toBoolean($this->bourse),
            'aide_sociale' => $this->toBoolean($this->aide_sociale),
            'bourses_de_stage' => $this->toBoolean($this->bourses_de_stage),
            'parent_id' => $this->parent_id ?: null,
//            'config' => json_encode($this->config),
//            'logic' => json_encode($this->logic),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
