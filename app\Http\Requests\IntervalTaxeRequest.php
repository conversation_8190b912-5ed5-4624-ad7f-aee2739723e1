<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IntervalTaxeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'min' => 'required|numeric',
            'max' => 'required|numeric|gt:min',
            'taxe' => 'required|numeric',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'max.gt' => 'La valeur maximale doit être supérieure à la valeur minimale.',
        ];
    }
}
