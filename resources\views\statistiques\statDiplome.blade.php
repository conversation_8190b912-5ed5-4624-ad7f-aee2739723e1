<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>stat_diplome_niveau_etudes</title>
    <style>
        body {
            font-family: XB Riyaz, Deja<PERSON>u Sans;
            direction: rtl;

        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th,
        td {
            border: 1px solid #dddddd;
            text-align: center;
            padding: 8px;
        }

        th {
            background-color: #f2f2f2;
        }

        td[rowspan] {
            vertical-align: middle;
        }
    </style>
</head>

<body>


    <!--    START HEADER -->
    <table dir="rtl" style="margin-left: auto; margin-right: 0;">
        <thead>
            <tr>
                <th style="text-align: center;font-size: 18px;" valign="center" colspan="3" rowspan="2">
                    الجمهورية التونسية <br>
                    وزارة التعليم العالي والبحث العلمي

                </th>

                @if ($year_export)
                <th colspan="5"></th>
                <th style="text-align: center;font-size: 18px" colspan="2">
                    السنة الجامعية
                </th>
                <th style="text-align: center;font-size: 16px" colspan="2">
                    {{ @$year_export }}
                </th>
                @endif

            </tr>
            <tr>
                <th colspan="5"></th>
                @if ($date_export)
                <th colspan="2">
                </th>
                <th style="text-align: center;font-size: 16px" colspan="2">
                    {{ explode(" ",$date_export)[0]  }} <br>
                    {{ explode(" ",$date_export)[1]  }}
                </th>
                @endif
            </tr>
            <tr>
                <th></th>
            </tr>
            <tr>
                <th style="text-align: center;font-size: 20px" colspan="3">
                    <b>
                        @switch($office)
                            @case("C")
                                ديوان الخدمات الجامعية للوسط
                                @break
                            @case("N")
                                ديوان الخدمات الجامعية للشمال
                                @break
                            @case("S")
                                ديوان الخدمات الجامعية للجنوب
                                @break
                            @default
                                @break

                        @endswitch
                    </b>
                </th>
            </tr>

            <tr></tr>
            <tr>
                <th></th>
                <th style="text-align: center;font-size: 16px" colspan="7">
                 عدد المطالب خلال السنة الجامعية {{ $year }}
                </th>
            </tr>

            <tr>
                <th rowspan="10000"></th>
            </tr>


        </thead>
    </table>
    <!--    END HEADER -->


    @foreach ( $data as $keyData => $valueData)

    <table dir="rtl" style="margin-left: auto; margin-right: 0;">
        <thead>
            <tr></tr>
            <tr>
                <th style="text-align: center;font-size: 20px" colspan="5">
                    {{$keyData}}
                </th>
            </tr>
        </thead>
    </table>

    <table dir="rtl" style="margin-left: auto; margin-right: 0;" border="1">
        <thead>
            <tr>
                <th rowspan="2" width="18" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center; width:100px">الشهادة</th>
                <th rowspan="2" width="18" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">مستوى<br> الدراسة</th>
                <th colspan="2" width="18" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">تونسيون</th>
                <th colspan="2" width="18" style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">أجانب</th>
            </tr>
            <tr>
                <th style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">ذكور</th>
                <th style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">إناث</th>
                <th style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">ذكور</th>
                <th style="font-weight: bold; border: 1px solid #000; background: #E7E8E8 ; text-align: center">إناث</th>
            </tr>
        </thead>
        <tbody>
            @php
                $prevDiscip = null;
            @endphp

            @foreach ($valueData as $row)
                <tr>
                    @if ($row->discip != $prevDiscip)
                        <td style="font-weight: bold; border: 1px solid #000; text-align: center"
                            rowspan="{{ collect($valueData)->where('discip', $row->discip)->count() }}" valign="center">
                                {{ $row->discip }}
                        </td>
                    @endif

                    <td style="border: 1px solid #000; text-align: center">{{ $row->anet }}</td>
                    <td style="border: 1px solid #000; text-align: center">{{ $row->males_tn }}</td>
                    <td style="border: 1px solid #000; text-align: center">{{ $row->females_tn }}</td>
                    <td style="border: 1px solid #000; text-align: center">{{ $row->males_etr }}</td>
                    <td style="border: 1px solid #000; text-align: center">{{ $row->females_etr }}</td>
                </tr>

                @php
                    $prevDiscip = $row->discip;
                @endphp
            @endforeach
        </tbody>
    </table>
    @endforeach

</body>

</html>
