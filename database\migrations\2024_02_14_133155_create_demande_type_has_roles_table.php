<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('demande_type_has_roles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('demande_type_id');
            $table->unsignedBigInteger('role_id');

            $table->foreign('demande_type_id')->references('id')->on('demande_types');
            $table->foreign('role_id')->references('id')->on('roles');
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('demande_type_has_roles');
    }
};
