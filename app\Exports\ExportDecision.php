<?php

namespace App\Exports;

use App\Models\Decision;
use Maatwebsite\Excel\Concerns\FromCollection;

class ExportDecision implements FromCollection
{

    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function headings():array{
        return[
            'cin', 'catb', 'lot', 'nom', 'datnais', 'gouv', 'sexe', 'profp', 'anet', 'discip',
            'fac', 'univ', 'inf', 'sup', 'enf', 'revp', 'revm', 'avis', 'res', 'moy', 'natdec',
            'situa', 'mbs', 'nmb', 'mf', 'ndec', 'dat', 'montanttotal', 'pourcentage', 'office',
            'type', 'annee_id'
        ];
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Decision::all();
    }
}
