<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRoleRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $oldName = $this->input('oldName');

        return [
            'name' => [
                'required',
                'string',
                'max:40',
                Rule::unique('roles', 'name')->ignore($oldName, 'name'),
            ],
            'oldName' => 'required|string|max:40',
            'permissions'=>'required'
        ];
    }
}
