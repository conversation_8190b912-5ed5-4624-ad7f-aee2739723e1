<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Notifications\ImportCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedImport implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(Admin $user)
    {
        $this->user = $user;
    }

    public function handle()
    {
        $this->user->notify(new ImportCompletedNotification());
    }
}
