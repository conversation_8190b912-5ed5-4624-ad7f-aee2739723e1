<?php

namespace App\Http\Controllers;

use App\Exports\AttestationStatExport;
use App\Exports\BeppExport;
use App\Exports\CustomTableStatExport;
use App\Exports\CustomTableStatExportPage2;
use App\Exports\DiplomeStatExport;
use App\Exports\FicheOrganismeStatExport;
use App\Exports\FilsParentByProfessionTypeAndAnneeStatExport;
use App\Exports\GraphTotalByYearExport;
use App\Exports\InternationalByCountryStatExport;
use App\Exports\InternationalExport;
use App\Exports\ParUniversiteEtAnneeStatExport;
use App\Exports\RevenuSuffisantStatExport;
use App\Exports\TotalByFacSexNationalityExport;
use App\Models\AnneeUniversitaire;
use App\Models\Catb;
use App\Models\Classification;
use App\Models\Decision;
use App\Models\Demande;
use App\Models\DemandeAnneeEtude;
use App\Models\DemandeType;
use App\Models\Etablissement;
use App\Models\FicheOrganisme;
use App\Models\Profession;
use App\Models\Universite;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Mpdf\Mpdf;

class StatistiqueController extends Controller
{

  public function getStatBourseUniv(Request $request)
  {
    // Results Do Not Group Res=0 => takes into account only Res = 1
    if ($request->has('type') && $request->has('year')) {
      $year = AnneeUniversitaire::find($request->year)->title;

      // bourse / bourse insertion ( BI )/ prêt  / Aide Sociale / Bourse Stage ( BS )
      $request->validate([
        'type' => 'in:bourse,pret,insertion,stage,aide_sociale',
        'year' => 'integer',
        'export' => 'integer',
        'office' => 'string'
      ]);

      $sqlString = "SELECT
            IFNULL(h.discip, 'Total') AS discip,
            IFNULL(d.name_ar, 'Total') AS discip_ar,
            IFNULL(h.anet, 'Total') AS anet,
            COUNT(CASE WHEN h.sexe = 1 THEN 1 END) AS males,
            COUNT(CASE WHEN h.sexe = 2 THEN 1 END) AS females,
            IFNULL(COUNT(*), 0) AS count_per_group,
            IFNULL(SUM(m.net_a_payer) / 1000, 0) AS total_net_a_payer
            FROM
                historiques h
            Right JOIN
                mandates m ON h.cin = m.cin
            JOIN diplomes d ON h.discip = d.code
            WHERE
                h.res = 1
                AND h.office like ?
                AND h.type = ?
                AND m.type = ? AND h.annee_id = ? AND m.annee_universitaire_id = ?

            GROUP BY
                discip_ar,
                h.anet";

      if (!($request->export)) {
        $sqlString .= " WITH ROLLUP;";
      } else {
        $sqlString .= " order by discip_ar desc;";
      }

      $result = DB::select($sqlString, [
        '%' . $request->office . '%',
        /*$request->export ? 'bourse' : */
        $request->type,
        /*$request->export ? 'bourse' : */
        $request->type,
        $request->year,
        $request->year
      ]);
    }
    if ($request->export) {
      $totals = collect($result)->groupBy('annee_id')->map(function ($group) {
        return [
          'males' => $group->sum('males'),
          'females' => $group->sum('females'),
          'count_per_group' => $group->sum('count_per_group'),
          'total_net_a_payer' => $group->sum('total_net_a_payer'), #/1000,
        ];
      });

      $res = DB::select(
        "SELECT
                COUNT(CASE WHEN h.sexe = 1 THEN 1 END) AS males,
                COUNT(CASE WHEN h.sexe = 2 THEN 1 END) AS females,
                IFNULL(COUNT(*), 0) AS count,
                IFNULL(SUM(m.net_a_payer)/1000, 0) AS montant
                FROM
                    historiques h
                Right JOIN
                    mandates m ON h.cin = m.cin
                JOIN diplomes d ON h.discip = d.code
                WHERE
                    h.res = 0
                    AND h.office like ?
                    AND h.type = ?
                    AND m.type = ? AND h.annee_id = ? AND m.annee_universitaire_id = ?

                GROUP BY
                    h.annee_id",
        ['%' . $request->office . '%', $request->type, $request->type, $request->year, $request->year]
      );

      $dataMesrs = Profession::select(
        DB::raw('COUNT(*) AS count'),
        DB::raw('SUM(montanttotal) AS montant'),
        DB::raw('COUNT(CASE WHEN historiques.sexe = 1  THEN 1 END) AS males'),
        DB::raw('COUNT(CASE WHEN historiques.sexe = 2  THEN 1 END) AS females')
      )
        ->join('classifications', 'professions.id', '=', 'classifications.profession_id')
        ->join('historiques', 'classifications.code', '=', DB::raw('SUBSTRING(historiques.lot, 1, 2)'))
        ->where('type_code', 2)
        ->where('historiques.annee_id', $request->year)
        ->where('historiques.office', $request->office)
        ->groupBy('historiques.annee_id')
        ->first();

      $dataAideSociale = Decision::where('res', 1)
        ->where('annee_id', $request->year)
        ->where('type', 'aide_sociale')
        ->where('office', $request->office)
        ->selectRaw(
          'type,
                    COUNT(CASE WHEN historiques.sexe = 1 THEN 1 END) AS males,
                    COUNT(CASE WHEN historiques.sexe = 2 THEN 1 END) AS females,
                    IFNULL(COUNT(*), 0) AS count,
                    IFNULL(SUM(historiques.montanttotal), 0) AS montant'
        )
        ->groupBy('type')
        ->first();

      $dataInsertion = Decision::where('res', 1)
        ->where('annee_id', $request->year)
        ->where('type', 'insertion')
        ->where('office', $request->office)
        ->selectRaw(
          'type,
                    COUNT(CASE WHEN historiques.sexe = 1 THEN 1 END) AS males,
                    COUNT(CASE WHEN historiques.sexe = 2 THEN 1 END) AS females,
                    IFNULL(COUNT(*), 0) AS count,
                    IFNULL(SUM(historiques.montanttotal), 0) AS montant'
        )
        ->groupBy('type')
        ->first();


      $label = "";

      switch ($request->type) {
        case 'bourse':
          $label = "منح";
          break;
        case 'insertion':
          $label = "منح الادماج";
          break;
        case 'aide_sociale':
          $label = "مساعدات الاجتماعية";
          break;
        case 'stage':
          $label = "منح التربص";
          break;
        case 'pret':
          $label = "قروض";
          break;

        default:
          # code...
          break;
      }

      //return response($result);
      return Excel::download(new BeppExport($request->office, $result, $year, $label, $totals, $dataMesrs, $dataAideSociale, $dataInsertion, @$res[0]), 'bepp.xlsx');
    } else {
      return $result;
    }
  }

  // currently not being used
  public function getStatRes(Request $request)
  {
    $result = DB::select("SELECT
                #IFNULL(h.discip, 'Total') AS discip,
                #IFNULL(h.anet, 'Total') AS anet,
                IFNULL(h.sexe, 'Total') AS sexe,
                IFNULL(COUNT(*),0) AS count_per_group,
                IFNULL(SUM(m.net_a_payer), 0) AS total_net_a_payer

                FROM
                    historiques h
                left JOIN
                    mandates m ON h.cin = m.cin

                    where h.res = 0
                GROUP BY
                #    h.discip,
                #    h.anet,
                    h.sexe
                WITH ROLLUP;
            ");

    return $result;
  }

  public function getStatGraphTotalByYear(Request $request)
  {
    if ($request->has('type')) {
      $request->validate([
        'type' => 'in:bourse,pret,insertion,stage,aide_sociale',
        'export' => 'integer',
        'office' => 'required|string',
      ]);

      $result = DB::select("SELECT
                historiques.annee_id,
                annee_universitaires.title,
                count(historiques.annee_id) as total
                from historiques
                join annee_universitaires on historiques.annee_id = annee_universitaires.id
                where historiques.type = ? AND historiques.office like ?
                group by historiques.annee_id, annee_universitaires.title
                order by annee_universitaires.title;
            ", [$request->type, "%" . $request->office . "%"]);

      if ($request->export) {
        if (count($result) == 0) {
          // Handle the case where no data is found
          return response()->json(['message' => 'no_data.'], 404);
        }
        //dd($result);
        return Excel::download(new GraphTotalByYearExport($result, $request->office), 'diplomesStat.xlsx');
      } else {
        return ['data' => $result];
      }
    }
  }

  public function getStatTotalByFacSexNationality(Request $request)
  {
    $request->validate([
      'year' => 'required|integer',
      'export' => 'integer',
      'office' => 'required|string',
    ]);
    /**
     *  Nationality :
     *      Tunisian : catb not in ( 6, 7)
     *      Etranger : catb in (6, 7)
     *      Catb max = 7
     */
    if ($request->export) {
      $year_name = AnneeUniversitaire::find($request->year)->title;

      $data = [];
      foreach (['bourse', 'pret', 'insertion', 'stage', 'aide_sociale'] as $type) {
        $result = DB::select("SELECT
                IFNULL(h.fac, 'Total') AS faculte,
                IFNULL(e.name, 'Total') AS name,
                IFNULL(e.name_ar, 'Total') AS name_ar,
                IFNULL(e.name_fr, 'Total') AS name_fr,
                COUNT(CASE WHEN (h.sexe = 1 AND h.catb not in (6,7)) THEN 1 END) AS males_tn,
                COUNT(CASE WHEN (h.sexe = 2 AND h.catb not in (6,7)) THEN 1 END) AS females_tn,
                COUNT(CASE WHEN (h.catb not in (6,7)) THEN 1 END) AS total_tn,

                COUNT(CASE WHEN (h.sexe = 1 AND h.catb  in (6,7)) THEN 1 END) AS males_etr,
                COUNT(CASE WHEN (h.sexe = 2 AND h.catb  in (6,7)) THEN 1 END) AS females_etr,
                COUNT(CASE WHEN (h.catb  in (6,7)) THEN 1 END) AS total_etr,

                IFNULL(COUNT(*), 0) AS total_general
                FROM
                    historiques h

                Join etablissements e on h.fac = e.code

                WHERE
                h.office like ? AND h.annee_id = ? AND h.type = ?
                GROUP BY
                    h.fac,
                    e.name,
                    e.name_ar,
                    e.name_fr
                    ;
            ", ["%" . $request->office . "%", $request->year, $type]);

        $data[] = $result;
      }

      $labels = ['المنح الجامعية', 'القروض الجامعية', 'منح الادماج', 'منح التربص', 'المساعدات الاجتماعية'];
      return Excel::download(new TotalByFacSexNationalityExport($data, $year_name, $labels, $request->office), 'stat.xlsx');
    } elseif ($request->has('type') && $request->has('year')) {
      $request->validate([
        'type' => 'in:bourse,pret,insertion,stage,aide_sociale',
        'year' => 'integer',
        'export' => 'integer'
      ]);
      $result = DB::select("SELECT
                IFNULL(h.fac, 'Total') AS faculte,
                IFNULL(e.name, 'Total') AS name,
                IFNULL(e.name_ar, 'Total') AS name_ar,
                IFNULL(e.name_fr, 'Total') AS name_fr,
                COUNT(CASE WHEN (h.sexe = 1 AND h.catb not in (6,7)) THEN 1 END) AS males_tn,
                COUNT(CASE WHEN (h.sexe = 2 AND h.catb not in (6,7)) THEN 1 END) AS females_tn,
                COUNT(CASE WHEN (h.catb not in (6,7)) THEN 1 END) AS total_tn,

                COUNT(CASE WHEN (h.sexe = 1 AND h.catb  in (6,7)) THEN 1 END) AS males_etr,
                COUNT(CASE WHEN (h.sexe = 2 AND h.catb  in (6,7)) THEN 1 END) AS females_etr,
                COUNT(CASE WHEN (h.catb  in (6,7)) THEN 1 END) AS total_etr,

                IFNULL(COUNT(*), 0) AS total_general
                FROM
                    historiques h

                Join etablissements e on h.fac = e.code

                WHERE
                h.office like ? AND h.annee_id = ? AND h.type = ?
                GROUP BY
                    h.fac,
                    e.name,
                    e.name_ar,
                    e.name_fr
                    ;
            ", ["%" . $request->office . "%", $request->year, $request->type]);

      return $result;
    }
  }


  /**
   * The functions below belong to the home screen statistics
   */

  public function homeStats(Request $request)
  {
    /**
     * verif is_* everywhere
     *
     *  add a day to end date and format Carbon to Y-m-d
     */
    //

    ini_set('memory_limit', '-1');

    // All requests suppose current annee_universitaire

    /*
            CHECK IF BOURSE INSERTION SHOULD BE ADDED TO ALL REQUESTS ( etat_bourse_insertion )
    */
    try {
      //code...
      $request->validate([
        'annee_universitaire_id' => 'required|numeric'
      ]);
    } catch (\Throwable $th) {
      // automatically get current annee universitaire
      $anneeUniversitaireCurrent = AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first();
      $request['annee_universitaire_id'] = $anneeUniversitaireCurrent->id;
    }

    $returns = [];

    $catbEtrangers = Catb::where('tunisien', 0)->get()->pluck('code');


    $user = Auth::user();
    if (!isset($user)) {
      return;
    }


//    $directions_array = ['DRS', 'DRK', 'DRM'];

    $builder_year_to_clone = Demande::withoutGlobalScopes()->where('demandes.annee_universitaire_id', $request->annee_universitaire_id);
//      join('demande_annee_etudes', 'demandes.id', '=', 'demande_annee_etudes.demande_id')
//      ->join('etablissements', 'demande_annee_etudes.code_etab', '=', 'etablissements.code')
//      ->whereIn('etablissements.code_dir_reg', $directions_array)
//      ->where('demandes.annee_universitaire_id', $request->annee_universitaire_id);


    // Total Bourse, Pret, Aide Sociale, Stage
    $builder_year = clone $builder_year_to_clone;
    $total_total = $builder_year->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $total_bourse = $builder_year->where('is_bourse', 1)->whereNot('is_pret', 1)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $total_insertion = $builder_year->where('is_bourse_insertion', 1)->whereNot('is_pret', 1)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $total_pret = $builder_year->where('is_pret', 1)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $total_aide_sociale = $builder_year->where('is_aide_sociale', 1)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $total_stage = $builder_year->where('is_bourse_stage', 1)->distinct()->count('demandes.id');

    // Traités ( >= en_desicion ) Bourse, Pret, Aide Sociale, Stage
    $builder_year = clone $builder_year_to_clone;
    $traites_total = $builder_year->where('etat', '>=', 5)->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $traites_bourse = $builder_year->where('is_bourse', 1)->whereNot('is_pret', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $traites_insertion = $builder_year->where('is_bourse_insertion', 1)->whereNot('is_pret', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $traites_pret = $builder_year->where('is_pret', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $traites_aide_sociale = $builder_year->where('is_aide_sociale', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $traites_stage = $builder_year->where('is_bourse_stage', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

    // En attente de paiement ( eligible )
    $builder_year = clone $builder_year_to_clone;
    $attente_paiement_total = $builder_year->where(function ($query) {
      $query->whereIn('etat_bourse_insertion', [1, 4])->orWhereIn('etat_bourse', [1, 4])->orWhereIn('etat_pret', [3, 4])->orWhereIn('etat_aide_sociale', [1, 4])->orWhereIn('etat_bourse_stage', [1, 4]);
    })->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $attente_paiement_bourse = $builder_year->where('is_bourse', 1)->whereNot('is_pret', 1)->whereIn('etat_bourse', [1, 4])->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $attente_paiement_insertion = $builder_year->where('is_bourse_insertion', 1)->whereNot('is_pret', 1)->whereIn('etat_bourse_insertion', [1, 4])->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $attente_paiement_pret = $builder_year->where('is_pret', 1)->whereIn('etat_pret', [3, 4])->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $attente_paiement_aide_sociale = $builder_year->where('is_aide_sociale', 1)->whereIn('etat_aide_sociale', [1, 4])->distinct()->count('demandes.id');

    $builder_year = clone $builder_year_to_clone;
    $attente_paiement_stage = $builder_year->where('is_bourse_stage', 1)->whereIn('etat_bourse_stage', [1, 4])->distinct()->count('demandes.id');

    $payes_montant_grouped_by_type = FicheOrganisme::where('annee_universitaire_id', $request->annee_universitaire_id)
      ->groupBy('type')
      ->selectRaw('type, SUM(montant_total) as montant')
      ->get();

    $payes_total_grouped_by_type = Decision::where('annee_id', $request->annee_universitaire_id)
      ->where('office', "like", "%C%")
      ->where('situa', "P")
      ->groupBy('type')
      ->selectRaw('type, COUNT(*) as total')
      ->get();


    $returns[] = [
      'total' => [
        'total' => $total_total,
        'bourse' => $total_bourse,
        'insertion' => $total_insertion,
        'pret' => $total_pret,
        'aide_sociale' => $total_aide_sociale,
        'stage' => $total_stage,
      ],
      'traites' => [
        'total' => $traites_total,
        'bourse' => $traites_bourse,
        'insertion' => $traites_insertion,
        'pret' => $traites_pret,
        'aide_sociale' => $traites_aide_sociale,
        'stage' => $traites_stage,
      ],
      'attente_paiement' => [
        'total' => $attente_paiement_total,
        'bourse' => $attente_paiement_bourse,
        'insertion' => $attente_paiement_insertion,
        'pret' => $attente_paiement_pret,
        'aide_sociale' => $attente_paiement_aide_sociale,
        'stage' => $attente_paiement_stage,
      ],
      'payes_total_grouped_by_type' => $payes_total_grouped_by_type,
      'payes_montant_grouped_by_type' => $payes_montant_grouped_by_type

    ];


    /**
     *
     * GET THREE TABLES : Monastir, Sousse, Kairouan
     */

    $directions_array = ['DRS', 'DRK', 'DRM'];

    $demandes_directions_builder = Demande::withoutGlobalScopes()->join('demande_annee_etudes', 'demandes.id', '=', 'demande_annee_etudes.demande_id')
      ->join('etablissements', 'demande_annee_etudes.code_etab', '=', 'etablissements.code')
      ->whereIn('etablissements.code_dir_reg', $directions_array)
      ->where('demandes.annee_universitaire_id', $request->annee_universitaire_id);

    foreach ($directions_array as $direction_item) {
      $direction_builder_to_clone = clone $demandes_directions_builder;
      $direction_builder_to_clone = $direction_builder_to_clone->where('etablissements.code_dir_reg', $direction_item);
      //dd($direction_builder->get());

      # total...
      $direction_builder = clone $direction_builder_to_clone;
      $total_total_d = $direction_builder->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $total_bourse_d = $direction_builder->where('is_bourse', 1)->whereNot('is_pret', 1)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $total_insertion_d = $direction_builder->where('is_bourse_insertion', 1)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $total_pret_d = $direction_builder->where('is_pret', 1)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $total_aide_sociale_d = $direction_builder->where('is_aide_sociale', 1)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $total_stage_d = $direction_builder->where('is_bourse_stage', 1)->distinct()->count('demandes.id');


      # traités
      $direction_builder = clone $direction_builder_to_clone;
      $traites_total_d = $direction_builder->where('etat', '>=', 5)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $traites_bourse_d = $direction_builder->where('is_bourse', 1)->whereNot('is_pret', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $traites_insertion_d = $direction_builder->where('is_bourse_insertion', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $traites_pret_d = $direction_builder->where('is_pret', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $traites_aide_sociale_d = $direction_builder->where('is_aide_sociale', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $traites_stage_d = $direction_builder->where('is_bourse_stage', 1)->where('etat', '>=', 5)->distinct()->count('demandes.id');


      # attente paiement
      $direction_builder = clone $direction_builder_to_clone;
      $attente_paiement_total_d = $direction_builder->where(function ($query) {
        $query->whereIn('etat_bourse_insertion', [1, 4])->orWhereIn('etat_bourse', [1, 4])->orWhereIn('etat_pret', [3, 4])->orWhereIn('etat_aide_sociale', [1, 4])->orWhereIn('etat_bourse_stage', [1, 4]);
      })->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $attente_paiement_bourse_d = $direction_builder->where('is_bourse', 1)->whereNot('is_pret', 1)->whereIn('etat_bourse', [1, 4])->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $attente_paiement_insertion_d = $direction_builder->where('is_bourse_insertion', 1)->whereIn('etat_bourse_insertion', [1, 4])->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $attente_paiement_pret_d = $direction_builder->where('is_pret', 1)->whereIn('etat_pret', [3, 4])->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $attente_paiement_aide_sociale_d = $direction_builder->where('is_aide_sociale', 1)->whereIn('etat_aide_sociale', [1, 4])->distinct()->count('demandes.id');

      $direction_builder = clone $direction_builder_to_clone;
      $attente_paiement_stage_d = $direction_builder->where('is_bourse_stage', 1)->whereIn('etat_bourse_stage', [1, 4])->distinct()->count('demandes.id');

      $codes_etab = Etablissement::where('code_dir_reg', $direction_item)->get()->pluck('code');

      $payes_grouped_by_type = Decision::whereNotIn('catb', $catbEtrangers)
        ->where('annee_id', $request->annee_universitaire_id)
        ->whereIn('fac', $codes_etab)
        ->groupBy('type')
        ->selectRaw('type, COUNT(*) as total, IFNULL(SUM(montanttotal), 0) as montant')
        ->get();

      $returns[] = [
        $direction_item => [
          'total_total_d' => $total_total_d,
          'total_bourse_d' => $total_bourse_d,
          'total_insertion_d' => $total_insertion_d,
          'total_pret_d' => $total_pret_d,
          'total_aide_sociale_d' => $total_aide_sociale_d,
          'total_stage_d' => $total_stage_d,

          'traites_total_d' => $traites_total_d,
          'traites_bourse_d' => $traites_bourse_d,
          'traites_insertion_d' => $traites_insertion_d,
          'traites_pret_d' => $traites_pret_d,
          'traites_aide_sociale_d' => $traites_aide_sociale_d,
          'traites_stage_d' => $traites_stage_d,

          'attente_paiement_total_d' => $attente_paiement_total_d,
          'attente_paiement_bourse_d' => $attente_paiement_bourse_d,
          'attente_paiement_insertion_d' => $attente_paiement_insertion_d,
          'attente_paiement_pret_d' => $attente_paiement_pret_d,
          'attente_paiement_aide_sociale_d' => $attente_paiement_aide_sociale_d,
          'attente_paiement_stage_d' => $attente_paiement_stage_d,

          'payes_grouped_by_type' => $payes_grouped_by_type

        ]
      ];
    }
//    }

    /**
     * Accord de bourse des etudiants
     */

    $types_ids_array = [
      2 => 'Nouveau Bachelier',
      3 => 'Cours d’Etude',
      4 => 'Renouvellement',
      5 => 'Doctorat',
      6 => 'Master'
    ];

    $accords_bourse_array = [];

    foreach ($types_ids_array as $typeId => $label) {
      $typeIds = [];
      # code...
      $type = DemandeType::find($typeId);

      $typeIds[] = $type?->id;
      if ($type?->fils) {
        foreach ($type->fils as $child) {
          $typeIds[] = $child->id;
          if ($child->fils) {
            foreach ($child->fils as $childd) {
              $typeIds[] = $childd->id;
              if ($childd->fils) {
                foreach ($childd->fils as $childdd) {
                  $typeIds[] = $childdd->id;
                }
              }
            }
          }
        }
      }

      $nombre_demandes = Demande::withoutGlobalScopes()
        ->select(['demande_type_id', 'annee_universitaire_id'])
        ->whereIn('demande_type_id', $typeIds)
        ->where('annee_universitaire_id', $request->annee_universitaire_id)
        ->count();

      // Get the database names for cross-database join
      $secondDb = config('database.connections.mysql2.database');

      $result = Demande::withTrashed()
        ->leftJoin(DB::raw("{$secondDb}.users as u"), 'demandes.user_id', '=', 'u.id')
        ->leftJoin('historiques as h', function ($join) {
          $join->on('h.cin', '=', 'u.cin')
               ->on('h.annee_id', '=', 'demandes.annee_universitaire_id');
        })
        ->where('demandes.annee_universitaire_id', $request->annee_universitaire_id)
        ->where('demandes.deleted_at', null)
        ->where('h.situa', "=", "P")
        ->where('h.office', "like", "%C%")
        ->whereIn('demandes.demande_type_id', $typeIds)
        ->selectRaw('COUNT(*) as nombre_payes_total, IFNULL(SUM(h.montanttotal), 0) as nombre_montant_total')
        ->first();

      $nombre_payes_total = $result->nombre_payes_total;
      $nombre_montant_total = $result->nombre_montant_total;

      $accords_bourse_array[] = [$label, $nombre_demandes, $nombre_payes_total, $nombre_montant_total];
    }

    $returns[] = $accords_bourse_array;
    $types_ids_etranger = DemandeType::where('type' , 'etranger')->pluck('id')->toArray();

    $nombre_demandes_etrg = Demande::withoutGlobalScopes()
      ->whereIn('demande_type_id', $types_ids_etranger)
      ->where('annee_universitaire_id', $request->annee_universitaire_id)
      ->count();

    $nombre_montant_international = Decision::whereIn('catb', $catbEtrangers)
      ->where('situa', "P")
      ->where('office', "like", "%C%")
      ->where('annee_id', $request->annee_universitaire_id)
      ->selectRaw('COUNT(*) as nombre_total, IFNULL(SUM(montanttotal), 0) as montant_total')
      ->get();

    $nombre_montant_international->add($nombre_demandes_etrg);

    $returns[] = $nombre_montant_international;

    return $returns;
  }

  // includes commented pdf , but test it
  public function generateStatDiplome(Request $request)
  {
    $request->validate([
      'year' => 'required|numeric',
      'export' => 'integer',
      'office' => 'required|string',
    ]);

    $year = AnneeUniversitaire::find($request->year)->title;

    $types = ['bourse' => 'المنح الجامعية', 'pret' => 'القروض الجامعية', 'aide_sociale' => 'المساعدة الاجتماعية', 'insertion' => 'منح الادماج'];
    # code...
    $bourse = DB::select("SELECT
            d.name_ar AS discip,
            h.anet AS anet,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb < 6) THEN 1 END) AS males_tn,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb < 6) THEN 1 END) AS females_tn,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb >= 6) THEN 1 END) AS males_etr,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb >= 6) THEN 1 END) AS females_etr
            FROM
                historiques h
            Join diplomes d on h.discip = d.code
            WHERE
                h.annee_id = ? AND h.type = 'bourse' AND h.office like ?
            GROUP BY
                d.name_ar,
                h.anet
            ORDER BY d.name_ar;
        ", [$request->year, "%" . $request->office . "%"]);

    $pret = DB::select("SELECT
            d.name_ar AS discip,
            h.anet AS anet,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb < 6) THEN 1 END) AS males_tn,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb < 6) THEN 1 END) AS females_tn,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb >= 6) THEN 1 END) AS males_etr,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb >= 6) THEN 1 END) AS females_etr
            FROM
                historiques h
            Join diplomes d on h.discip = d.code
            WHERE
                h.annee_id = ? AND h.type = 'pret' AND h.office like ?
            GROUP BY
                d.name_ar,
                h.anet
            ORDER BY d.name_ar;
        ", [$request->year, "%" . $request->office . "%"]);


    $aide_sociale = DB::select("SELECT
            d.name_ar AS discip,
            h.anet AS anet,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb < 6) THEN 1 END) AS males_tn,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb < 6) THEN 1 END) AS females_tn,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb >= 6) THEN 1 END) AS males_etr,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb >= 6) THEN 1 END) AS females_etr
            FROM
                historiques h
            Join diplomes d on h.discip = d.code
            WHERE
                h.annee_id = ? AND h.type = 'aide_sociale' AND h.office like ?
            GROUP BY
                d.name_ar,
                h.anet
            ORDER BY d.name_ar;
        ", [$request->year, "%" . $request->office . "%"]);

    $insertion = DB::select("SELECT
            d.name_ar AS discip,
            h.anet AS anet,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb < 6) THEN 1 END) AS males_tn,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb < 6) THEN 1 END) AS females_tn,
            COUNT(CASE WHEN (h.sexe = 1 AND h.catb >= 6) THEN 1 END) AS males_etr,
            COUNT(CASE WHEN (h.sexe = 2 AND h.catb >= 6) THEN 1 END) AS females_etr
            FROM
                historiques h
            Join diplomes d on h.discip = d.code
            WHERE
                h.annee_id = ? AND h.type = 'insertion' AND h.office like ?
            GROUP BY
                d.name_ar,
                h.anet
            ORDER BY d.name_ar;
        ", [$request->year, "%" . $request->office . "%"]);

    $data = [
      'المنح الجامعية' => $bourse,
      'القروض الجامعية' => $pret,
      'المساعدة الاجتماعية' => $aide_sociale,
      'منح الادماج' => $insertion,
    ];

    if ($request->export) {
      if (count($data) == 0) {
        // Handle the case where no data is found
        return response()->json(['message' => 'no_data.'], 404);
      }
      return Excel::download(new DiplomeStatExport($data, $request->office, $year), 'diplomesStat.xlsx');
    } else {
      return ['data' => $data];
    }


    /*
        $mpdf = new Mpdf();

        $html = view('statistiques.statDiplome', compact('bourse', 'pret', 'aide_sociale', 'insertion'))->render();

        $mpdf->WriteHTML($html);
        $mpdf->Output('stat_diplome_niveau_etudes.pdf', 'D');
    */
  }

  // pdf
  public function generateStatAttestation(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer'
    ]);

    $types_data = DB::select("SELECT code, title_ar from attestation_types");

    $types_codes = collect($types_data)->pluck('code');
    $types_labels = collect($types_data)->pluck('title_ar');

    $attestations = [];
    foreach ($types_codes as $type) {
      $attestations_grp = DB::select("SELECT
                o.name_ar,
                SUM(a.status = 'prete') AS traites,
                SUM(a.status is not null) AS enregistres
                from attestations a
                join offices o on o.id = a.office_id
                join attestation_types t on a.attestation_types_id = t.id
                where t.code = '" . $type . "'
                AND o.code in ('DRS', 'DRM' , 'DRK')
                AND a.annee_universitaire_id = " . $request->annee_universitaire_id . "
                GROUP by o.name_ar;
            ");
      array_push($attestations, $attestations_grp);
    }

    $mpdf = new Mpdf();
    $html = view('statistiques.statAttestation', compact('attestations', 'types_labels'))->render();
    $mpdf->WriteHTML($html);
    $mpdf->Output('stat_nbre_demandes_par_AU_et_direction.pdf', 'D');
  }


  public function exportExcelAttestationStat(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer'
    ]);

    $year = AnneeUniversitaire::find($request->annee_universitaire_id)->title;

    $types_data = DB::select("SELECT code, title_ar from attestation_types");

    $types_codes = collect($types_data)->pluck('code');
    $types_labels = collect($types_data)->pluck('title_ar');

    $attestations = [];
    foreach ($types_codes as $type) {
      $attestations_grp = DB::select("SELECT
                o.name_ar,
                SUM(a.status = 'prete') AS traites,
                SUM(a.status is not null) AS enregistres
                from attestations a
                join offices o on o.id = a.office_id
                join attestation_types t on a.attestation_types_id = t.id
                where t.code = '" . $type . "'
                AND a.annee_universitaire_id = " . $request->annee_universitaire_id . "
                AND o.code in ('DRS', 'DRM' , 'DRK','C')
                GROUP by o.name_ar;
            ");
      array_push($attestations, $attestations_grp);
    }
    //dd($attestations, $types_labels);

    if ($request->export) {
      if (count($attestations) == 0) {
        // Handle the case where no data is found
        return response()->json(['message' => 'no_data.'], 404);
      }
      return Excel::download(new AttestationStatExport($attestations, $types_labels, $year), 'attestations.xlsx');
    } else {
      return ['data' => $attestations, 'types_labels' => $types_labels];
    }
  }

  public function statAttestationCards(Request $request)
  {
    //dd($request->startDate, $request->endDate,Carbon::parse($request->startDate)->format('Y-m-d'),Carbon::parse($request->endDate)->format('Y-m-d'));

    if ($request->has('year') && $request->has('startDate') && $request->has('endDate') && $request->startDate && $request->endDate) {
      # code...
      $attests = DB::select('SELECT IFNULL(count(*),0) as total, t.title_ar, t.title_fr, t.title
            from attestations a
            join attestation_types t on a.attestation_types_id = t.id
            where a.year="' . $request->year . '"
            and a.created_at >= "' . Carbon::parse($request->startDate)->format('Y-m-d') . '"
            and a.created_at <= "' . Carbon::parse($request->endDate)->format('Y-m-d') . '"
            group by t.title_ar, t.title_fr, t.title');

      return $attests;
    }

    if ($request->has('year') && $request->year) {

      $attests = DB::select('SELECT IFNULL(count(*),0) as total, t.title_ar, t.title_fr, t.title
            from attestations a
            join attestation_types t on a.attestation_types_id = t.id
            where a.year="' . $request->year . '"
            group by t.title_ar, t.title_fr, t.title');

      return $attests;
    } else {
      $attests = DB::select('SELECT IFNULL(count(*),0) as total, t.title_ar, t.title_fr, t.title
            from attestations a
            join attestation_types t on a.attestation_types_id = t.id
            group by t.title_ar, t.title_fr, t.title');

      return $attests;
    }
  }


  public function exportFicheOrganisme(Request $request)
  {
    $request->validate([
      'annee_end_id' => 'required|numeric',
      'annee_start_id' => 'required|numeric',
      'export' => 'integer',
      //'office' => 'required|string',
    ]);
    $yearStartObj = AnneeUniversitaire::find($request->annee_start_id);
    $yearEndObj = AnneeUniversitaire::find($request->annee_end_id);
    $yearStart = $yearStartObj->order;
    $yearEnd = $yearEndObj->order;
    $years_ids = AnneeUniversitaire::whereBetween('order', [$yearEnd, $yearStart])
      ->get()
      ->pluck('id');
    //dd($request->annee_start_id,$request->annee_end_id, "years 1 ",$years_ids, "years 2 ", $years_ids2);

    //$data = DB::select('SELECT count(*), type, sum(nb_total) as col_total, sum(montant_total) as col_montant, annee_universitaire_id
    //    from fiche_organismes
    //    group by annee_universitaire_id, type ');

    $data = DB::table('fiche_organismes')
      ->whereIn('fiche_organismes.annee_universitaire_id', $years_ids)
      //->where('fiche_organismes.office', 'like', "%". $request->office . "%")
      ->join('annee_universitaires', 'fiche_organismes.annee_universitaire_id', '=', 'annee_universitaires.id')
      ->select(DB::raw('COUNT(*) as count, type, SUM(nb_total) as col_total, SUM(montant_total) as col_montant, title'))
      ->groupBy('title', 'type')
      ->get()
      ->map(function ($item) {
        // Map types to other strings
        if ($item->type === 'stage') {
          $item->type = 'منحة تربص';
        }
        if ($item->type === 'pret') {
          $item->type = 'القروض الجامعية';
        }
        if ($item->type === 'bourse') {
          $item->type = 'منحة جامعية';
        }
        if ($item->type === 'insertion') {
          $item->type = 'منحة الادماج الجامعي';
        }
        if ($item->type === 'aide sociale') {
          $item->type = 'المساعدات الجامعية للطلبة';
        }
        // You can add more mappings here if needed
        return $item;
      });
    //dd($data);

    // TITLE IS ANNEE UNIVERSITAIRE TITLE

    $arr = [];
    foreach ($data->unique('type') as $type) {
      foreach ($data->unique('title') as $year) {
        $filteredData = $data->where('title', $year->title)
          ->where('type', $type->type);

        $total = $filteredData->isEmpty() ? '0' : $filteredData->first()->col_total;
        $montant = $filteredData->isEmpty() ? '0' : $filteredData->first()->col_montant;
        $count = $filteredData->isEmpty() ? '0' : $filteredData->first()->count;
        $arr[] = [
          'count' => $count,
          'total' => $total,
          'montant' => $montant,
          'annee' => $year->title,
          'real_annee' => $filteredData->isEmpty() ? 'NONE' : $filteredData->first()->title,
          'type' => $type->type,
          'real_type' => $filteredData->isEmpty() ? 'NONE' : $filteredData->first()->type,
        ];
      }
    }

    $totals = $data->groupBy('title')->map(function ($group) {
      return [
        'col_total' => $group->sum('col_total'),
        'col_montant' => $group->sum('col_montant')
      ];
    });


    if ($request->export) {
      if ($data->isEmpty()) {
        // Handle the case where no data is found
        return response()->json(['message' => 'no_data.'], 404);
      }
      return Excel::download(new FicheOrganismeStatExport($data, $totals, "C", $yearStartObj->title, $yearEndObj->title), 'ficheOrganisme.xlsx');
    } else {
      return ['data' => $data, 'totals' => $totals];
    }
  }


  public function statListEtudiantsEtrangersBoursiers(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer'
    ]);

    $user = Auth::user();
    $catbEtrangers = Catb::where('tunisien', 0)->get()->pluck('code');

    if (true) { //($user->office && $user->office->parent_id == null) {
      $international = Decision::select('cin', 'nom', 'datnais', 'montanttotal')->whereIn('catb', $catbEtrangers)
        ->where('annee_id', $request->annee_universitaire_id)
        ->with('user')
        ->with('etablissement')
        ->with('country_iso')
        ->get();
    } else {
      $codes_etab = Etablissement::where('code_dir_reg', $user->office->code)->get()->pluck('code');

      $international = Decision::select('name', 'surname')->whereIn('catb', $catbEtrangers)
        ->where('annee_id', $request->annee_universitaire_id)
        ->whereIn('fac', $codes_etab)
        ->with('user')
        ->with('etablissement')
        ->with('country_iso')
        ->get();
    }

    return $international;
  }


  // check if changes depending on user office
  public function exportListEtudiantsEtrangersBoursiers(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer',
      'office' => 'required|string',
    ]);

    $user = Auth::user();
    $year = AnneeUniversitaire::find($request->annee_universitaire_id)->title;
    $catbEtrangers = Catb::where('tunisien', 0)->get()->pluck('code');

    if (true) { //($user->office && $user->office->parent_id == null) {
      $international = Decision::select('cin', 'nom', 'datnais', 'montanttotal', 'fac')
        ->whereIn('catb', $catbEtrangers)
        ->where('annee_id', $request->annee_universitaire_id)
        ->where('office', 'like', "%" . $request->office . "%")
        ->with('user')
        ->with('etablissement')
        ->with('country_iso')
        ->get();
    } else {
      $codes_etab = Etablissement::where('code_dir_reg', $user->office->code)->get()->pluck('code');

      $international = Decision::select('cin', 'nom', 'datnais', 'montanttotal', 'fac')
        ->whereIn('catb', $catbEtrangers)
        ->where('annee_id', $request->annee_universitaire_id)
        ->where('office', 'like', "%" . $request->office . "%")
        ->whereIn('fac', $codes_etab)
        ->with('user')
        ->with('etablissement')
        ->with('country_iso')
        ->get();
    }

    if ($request->export) {
      if ($international->isEmpty()) {
        // Handle the case where no data is found
        return response()->json(['message' => 'no_data.'], 404);
      }
      return Excel::download(new InternationalExport($international, $year, $request->office), 'international.xlsx');
    } else {
      return ['data' => $international];
    }
  }


  public function exportEtrangersBoursiersParPays(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer',
      'office' => 'required|string',
    ]);

    $user = Auth::user();
    $year = AnneeUniversitaire::find($request->annee_universitaire_id)->title;
    $catbEtrangers = Catb::where('tunisien', 0)->get()->pluck('code');

    if ($user->office && $user->office->parent_id == null) {
      $data = DB::table('historiques')
        ->select('countries.code', 'countries.name_ar')
        ->selectRaw('SUM(historiques.montanttotal) as total_amount')
        ->selectRaw('COUNT(*) as record_count')
        ->whereIn('historiques.catb', $catbEtrangers)
        ->where('historiques.annee_id', $request->annee_universitaire_id)
        ->where('historiques.office', 'like', "%" . $request->office . "%")
        //->whereIn('historiques.fac', $codes_etab)
        ->leftJoin('countries', function ($join) {
          $join->on(DB::raw('SUBSTR(historiques.cin, 1, 2)'), '=', 'countries.code');
        })
        ->groupBy('countries.code', 'countries.name_ar')
        ->orderBy('countries.name_ar', 'desc')
        ->get();
    } else {
      $codes_etab = Etablissement::where('code_dir_reg', $user->office->code)->get()->pluck('code');

      $data = DB::table('historiques')
        ->select('countries.code', 'countries.name_ar')
        ->selectRaw('SUM(historiques.montanttotal) as total_amount')
        ->selectRaw('COUNT(*) as record_count')
        ->whereIn('historiques.catb', $catbEtrangers)
        ->where('historiques.annee_id', $request->annee_universitaire_id)
        ->where('historiques.office', 'like', "%" . $request->office . "%")
        ->whereIn('historiques.fac', $codes_etab)
        ->leftJoin('countries', function ($join) {
          $join->on(DB::raw('SUBSTR(historiques.cin, 1, 2)'), '=', 'countries.code');
        })
        ->groupBy('countries.code', 'countries.name_ar')
        ->orderBy('countries.name_ar', 'desc')
        ->get();
    }


    //dd($data->pluck('etablissement'));
    if ($request->export) {
      if ($data->isEmpty()) {
        // Handle the case where no data is found
        return response()->json(['message' => 'no_data.'], 404);
      }
      return Excel::download(new InternationalByCountryStatExport($data, $year, $request->office), 'internationalParPaysStat.xlsx');
    } else {
      return ['data' => $data];
    }
  }

  // check if this is type bourse only because there's a second page containing insertion, pret, aide_sociale
  public function exportCustomTable(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer',
      //'office' => 'required|string'
    ]);

    $year = AnneeUniversitaire::find($request->annee_universitaire_id)->title;

    // check if doctorat is catb=5 or discip = doct because results differ
    $data = FicheOrganisme::where('annee_universitaire_id', $request->annee_universitaire_id)
      //->where('fiche_organismes.office', 'like', "%". $request->office . "%")
      ->leftJoin('historiques', 'fiche_organismes.num_decision', '=', 'historiques.ndec')
      ->groupBy('fiche_organismes.annee_universitaire_id', 'fiche_organismes.num_decision')
      ->selectRaw('
                    fiche_organismes.*  , count(*) as nb_fiches_org,
                    COUNT(CASE WHEN historiques.catb = 1  THEN 1 END) AS new_students,
                    COUNT(CASE WHEN historiques.catb= 2 THEN 1 END) AS renouvellement,
                    COUNT(CASE WHEN historiques.catb= 3 THEN 1 END) AS en_cours_etude,
                    COUNT(CASE WHEN historiques.discip = "DOCT" THEN 1 END) AS doctorat,
                    COUNT(CASE WHEN historiques.discip = "MSTR" and historiques.anet=1 THEN 1 END) AS master1,
                    COUNT(CASE WHEN historiques.discip = "MSTR" and historiques.anet=2 THEN 1 END) AS master2,

                    COUNT(CASE WHEN historiques.catb in (6,7) THEN 1 END) AS etrangers
                ')->get();

    $totals = $data->groupBy('annee_universitaire_id')->map(function ($group) {
      return [
        'nb_total' => $group->sum('nb_total'),
        'montant_total' => $group->sum('montant_total'),
        'tax_total' => $group->sum('tax_total'),
        'montant_ttc' => $group->sum('montant_ttc'),

        'new_students' => $group->sum('new_students'),
        'renouvellement' => $group->sum('renouvellement'),
        'en_cours_etude' => $group->sum('en_cours_etude'),
        'master1' => $group->sum('master1'),
        'master2' => $group->sum('master2'),
        'doctorat' => $group->sum('doctorat'),
        'etrangers' => $group->sum('etrangers'),

      ];
    });

    if ($request->export) {
      return Excel::download(new CustomTableStatExport($data, $year, $totals, "C"), 'customStatTable.xlsx');
    } else {
      return ['data' => $data, 'totals' => $totals];
    }
  }

  public function exportCustomTablePage2(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'export' => 'required|integer',
    ]);

    $year = AnneeUniversitaire::find($request->annee_universitaire_id)->title;

    // check if doctorat is catb=5 or discip = doct because results differ
    $labels = ['insertion', 'pret', 'aide_sociale'];
    $labels_values = ['منحة الادماج الجامعي', 'القروض الجامعية', 'المساعدات الاجتماعية'];
    $array = [];

    foreach ($labels as $type) {
      $dataInterm = FicheOrganisme::where('annee_universitaire_id', $request->annee_universitaire_id)
        ->where('fiche_organismes.type', $type)
        //->where('fiche_organismes.office', $request->office)
        ->get();

      $totalsInterm = $dataInterm->groupBy('annee_universitaire_id')->map(function ($group) {
        return [
          'nb_total' => $group->sum('nb_total'),
          'montant_total' => $group->sum('montant_total'),
          'tax_total' => $group->sum('tax_total'),
          'montant_ttc' => $group->sum('montant_ttc'),

        ];
      });

      $array[] = [$dataInterm, $totalsInterm];
    }
    //dd($array[0][0][0]->date_emission);


    if ($request->export) {
      return Excel::download(new CustomTableStatExportPage2($array, $year, $labels_values), 'customStatTablePage2.xlsx');
    } else {
      return ['data' => $array];
    }
  }

  public function exportStatParUniversiteEtAnnee(Request $request)
  {
    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'universite_id' => 'required|numeric',
      'office' => 'nullable|string',
      'export' => 'required|integer'
    ]);

    $universite = Universite::findOrFail($request->universite_id);
    $year = AnneeUniversitaire::findOrFail($request->annee_universitaire_id)->title;

    $listetablissements = Etablissement::where('code_univ', $universite->code)->pluck('code');

    // in case they want to add other types of bourses, add select counts like "prets" and check which don't apply to international students
    $data = Decision::whereIn('fac', $listetablissements)
      ->when($request->office, function ($query) use ($request) {
        return $query->where('office', 'like', "%" . $request->office . "%");
      })
      ->where('annee_id', $request->annee_universitaire_id)
      ->join('diplomes', 'historiques.discip', '=', 'diplomes.code')
      ->selectRaw('COUNT(CASE WHEN historiques.catb not in (6,7) and type="bourse"  THEN 1 END) AS tunisiens,
                            COUNT(CASE WHEN historiques.catb in (6,7) and type="bourse" THEN 1 END) AS etrangers,
                            COUNT(CASE WHEN type="pret" THEN 1 END) AS prets,
                            diplomes.name_ar as diplome,
                            annee_id
                ')->groupBy('diplome')->get();

    // return univ, anne etc

    $totals = $data->groupBy('annee_id')->map(function ($group) {
      return [
        'tunisiens' => $group->sum('tunisiens'),
        'etrangers' => $group->sum('etrangers'),
        'prets' => $group->sum('prets'),
      ];
    });

    //return [$data,$year,$totals, $universite->name_ar];

    if ($request->export) {
      return Excel::download(new ParUniversiteEtAnneeStatExport($data, $year, $totals, $universite->name_ar), 'parUniversiteEtParAnnee.xlsx');
    } else {
      return ['data' => $data, 'totals' => $totals];
    }
  }

  public function exportRevenuSuffisantStat(Request $request)
  {
    $request->validate([
      'export' => 'required|integer',
      'office' => 'required|string',
    ]);

    $data = Decision::selectRaw('COUNT(CASE WHEN historiques.catb = 1  THEN 1 END) AS nouveaux,
            COUNT(CASE WHEN historiques.catb = 3  THEN 1 END) AS anciens,
            COUNT(CASE WHEN historiques.discip = "MSTR"  THEN 1 END) AS master,
            COUNT(*) as total,
            annee_universitaires.title,
            annee_universitaires.smig
            ')->leftJoin('annee_universitaires', 'historiques.annee_id', '=', 'annee_universitaires.id')
      ->whereColumn('historiques.revp', '>', 'annee_universitaires.smig')
      ->where('historiques.office', 'like', "%" . $request->office . "%")
      ->groupBy('annee_universitaires.title', 'annee_universitaires.smig')
      ->orderByDesc('annee_universitaires.title')
      ->get();

    if ($request->export) {
      if ($data->isEmpty()) {
        // Handle the case where no data is found
        return response()->json(['message' => 'no_data.'], 404);
      }
      return Excel::download(new RevenuSuffisantStatExport($data, $request->office), 'revenuSuffisant.xlsx');
    } else {
      return ['data' => $data];
    }
  }

  // must update foreign keys in classifications table ( profession_id ) and historiques.lot first 2 chars must be correct

  public function exportFilsParentMeducMesrsParAnnee(Request $request)
  {

    /**
     * SELECT professions.name_ar, classifications.title_ar, SUBSTR(historiques.lot, 1, 2), count(*)
     * FROM professions
     * join classifications on professions.id = classifications.profession_id
     * join historiques on classifications.code = SUBSTR(historiques.lot, 1, 2)
     * where (type_code = 2 AND historiques.annee_id = 1)
     * group by professions.name;
     *
     * SELECT professions.name_ar, professions.code, SUBSTR(historiques.lot, 1,1), count(*)
     * FROM professions join historiques on professions.code = SUBSTR(historiques.lot, 1,1)
     * where (type_code = 2 AND historiques.annee_id = 1) group by professions.name;
     */


    $request->validate([
      'annee_universitaire_id' => 'required|numeric',
      'profession_type_id' => 'required|numeric',
      'demande_type' => 'sometimes|required|string',
      'export' => 'required|integer'
    ]);

    $year = AnneeUniversitaire::find($request->annee_universitaire_id)->title;

    $demandeType = "bourse";

    if ($request->demande_type && $request->demande_type != "") {
      $demandeType = $request->demande_type;
    }

    if ($request->profession_type_id == 1) {

      $data = Decision::from('historiques as h')
        ->select([
          DB::raw('COUNT(DISTINCT h.id) AS count'),
          DB::raw('SUM(montanttotal) AS montant'),
          DB::raw('COUNT(DISTINCT CASE WHEN h.sexe = 1 THEN h.id END) AS males'),
          DB::raw('COUNT(DISTINCT CASE WHEN h.sexe = 2 THEN h.id END) AS females'),
        ])
        ->leftJoin('classifications as c', function ($join) {
          $join->on(DB::raw('SUBSTRING(h.lot, 1, CHAR_LENGTH(c.code))'), '=', 'c.code')
            ->whereRaw("SUBSTRING(h.lot, CHAR_LENGTH(c.code) + 1, 1) REGEXP '^[0-9]+$'");
        })
        ->withTrashed()
        ->where('c.profession_id', null)
        ->where('h.annee_id', $request->annee_universitaire_id)
        ->where('h.type', $demandeType)
        ->where('h.situa', "P")
        ->where('h.deleted_at', null)
        ->where('h.office', 'like', "%" . $request->office . "%")
        ->get();


      $totals = $data->groupBy('historiques.annee_id')->map(function ($group) {
        return [
          'males' => $group->sum('males'),
          'females' => $group->sum('females'),
          'count' => $group->sum('count'),
          'montant' => $group->sum('montant'),
        ];
      });
      return ['data' => $data, 'totals' => $totals];
    } else {
      # code...
      $data = Profession::select([
        'professions.name_ar as profession_name',
        DB::raw('COUNT(DISTINCT h.id) AS count'),
        DB::raw('SUM(montanttotal) AS montant'),
        DB::raw('COUNT(DISTINCT CASE WHEN h.sexe = 1 THEN h.id END) AS males'),
        DB::raw('COUNT(DISTINCT CASE WHEN h.sexe = 2 THEN h.id END) AS females'),
      ])
        ->rightJoin('classifications as c', 'professions.id', '=', 'c.profession_id')
        ->rightJoin('historiques as h', function ($join) {
          $join->on(DB::raw('SUBSTRING(h.lot, 1, CHAR_LENGTH(c.code))'), '=', 'c.code')
            ->whereRaw("SUBSTRING(h.lot, CHAR_LENGTH(c.code) + 1, 1) REGEXP '^[0-9]+$'");
        })
        ->where('type_code', $request->profession_type_id)
        ->where('h.type', $demandeType)
        ->where('h.situa', "P")
        ->where('h.annee_id', $request->annee_universitaire_id)
        ->where('h.office', 'like', "%" . $request->office . "%")
        ->groupBy('professions.name')
        ->get();

      $totals = $data->groupBy('h.annee_id')->map(function ($group) {
        return [
          'males' => $group->sum('males'),
          'females' => $group->sum('females'),
          'count' => $group->sum('count'),
          'montant' => $group->sum('montant'),
        ];
      });
    }


    if ($request->export) {
      return Excel::download(new FilsParentByProfessionTypeAndAnneeStatExport($data, $year, $request->profession_type_id, $totals, $request->office), 'filsParentMeducMesrs.xlsx');
    } else {
      return ['data' => $data, 'totals' => $totals];
    }
  }
}



