<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreFiliereRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'name_fr' => 'required|string|max:100',
            'name_ar' => 'required|string|max:100',
            'code_etab' => 'required',
            'code_diplome' => 'required|string',
            'annee_universitaire' => 'required|integer',
//            'code' => 'required|integer|unique:filieres,code',
            'code' => [
                'required',
                Rule::unique('filieres', 'code')->where('annee_universitaire', $this->input('annee_universitaire')),
            ],
            ];
    }
}
