<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Auditable as AuditableTrait;
use OwenIt\Auditing\Contracts\Auditable;

class Rectificatif extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'id','cin', 'catb', 'lot', 'nom', 'datnais', 'gouv', 'sexe', 'profp', 'anet', 'discip',
        'fac', 'univ', 'inf', 'sup', 'enf', 'revp', 'revm', 'avis', 'res', 'moy', 'natdec',
        'situa', 'mbs', 'nmb', 'mf', 'ndec', 'dat', 'montanttotal', 'pourcentage', 'office',
        'type', 'annee_id', 'rectificatif_numero_id'
    ];

    public function rectificatif_numero() : BelongsTo
    {
        return $this->belongsTo(RectificatifNumero::class,'rectificatif_numero_id');
    }

    public function annee_universitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,'annee_id');
    }
}
