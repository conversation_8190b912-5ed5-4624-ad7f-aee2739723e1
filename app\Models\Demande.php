<?php

namespace App\Models;

use App\Http\Controllers\Api\ProfessionTypeController;
use App\Models\Scopes\DemandeScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Demande extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected static function booted()
    {
        static::addGlobalScope(new DemandeScope());
    }

    const ETAT_STRING = [
        1 => 'DOSSIER_EN_ATTENTE',
        2 => 'DOSSIER_EN_COURS',
        3 => 'EN_COURS_DE_DECISION',
        4 => 'CONTROL_FISCAL',
        5 => 'PRISE_DE_DECISION',
        6 => 'PREPARATION_PAIEMENT',
        7 => 'SUIVI_PAIEMENT',
        99 => 'REFUS_PAIEMENT',
        100 => 'DOSSIER_REFUS',
    ];
    const ETAT = [
        'DOSSIER_EN_ATTENTE'=> 1,
        'DOSSIER_EN_COURS'=> 2,
        'EN_COURS_DE_DECISION'=> 3 ,
        'CONTROL_FISCAL'=> 4,
        'PRISE_DE_DECISION'=> 5,
        'PREPARATION_PAIEMENT'=> 6,
        'SUIVI_PAIEMENT'=> 7,
        'REFUS_PAIEMENT'=> 99,
        'DOSSIER_REFUS'=> 100,
    ];
    const ETAT_COMPLEMENT = [
        'DEPOT_COMPLEMENT_EN_ATTENTE'=> 'depot_complement_en_attente',
        'DOCUMENT_COMPLEMENT_RECU'=> 'document_complement_recu',
        'CLOTURE'=> 'cloture',
    ];
    const ETAT_DOSSIER = [
        'DOSSIER_REFUS'=> 'dossier_refus',
        'DOSSIER_INCOHERENT'=> 'dossier_incoherent',
        'DOSSIER_INCOMPLET'=> 'dossier_incomplet',
        'DOSSIER_COMPLET'=> 'dossier_complet',
    ];
    const ETAT_CONTRAT = [
        'DEPOT_CONTRAT_EN_ATTENTE'=> 'depot_contrat_en_attente',
        'DOCUMENT_CONTRAT_RECU'=> 'document_contrat_recu',
        'DOCUMENT_CONTRAT_CONFORME'=> 'document_contrat_conforme',
        'DOCUMENT_CONTRAT_NON_CONFORME'=> 'document_contrat_non_conforme',
        'ACCEPTE'=> 'accepte',
        'REFUS'=> 'refus',
    ];

    const ETAT_BOURSE = [
        'EN_ATTENTE'=> 0,
        'ELIGIBLE'=> 1,
        'NON_ELIGIBLE'=> 2,
        'EN_INSTANCE'=> 3,
        'SUIVI_PAIEMENT'=> 4,
        'DECISION_FAVORABLE'=> 5,
        'DECISION_NON_FAVORABLE'=> 6,
        'DECISION_AMBIGU'=> 7,
        'MANDAT_RECU'=> 8,
        'MANDAT_EN_INSTANCE'=> 9,
        'MANDAT_PAYEE'=> 10,
        'MANDAT_BLOQUE'=> 11,
        ];
    const ETAT_BOURSE_INSERTION = [
        'EN_ATTENTE'=> 0,
        'ELIGIBLE'=> 1,
        'NON_ELIGIBLE'=> 2,
        'EN_INSTANCE'=> 3 ,
        'SUIVI_PAIEMENT'=> 4 ,
        'DECISION_FAVORABLE'=> 5,
        'DECISION_NON_FAVORABLE'=> 6,
        'DECISION_AMBIGU'=> 7,
        'MANDAT_RECU'=> 8,
        'MANDAT_EN_INSTANCE'=> 9,
        'MANDAT_PAYEE'=> 10,
        'MANDAT_BLOQUE'=> 11,
    ];
    const ETAT_PRET = [
        'EN_ATTENTE'=> 0,
        'PROPOSITION'=> 1,
        'NON_ELIGIBLE'=> 2,
        'ELIGIBLE'=> 3,
        'SUIVI_PAIEMENT'=> 4 ,
        'DECISION_FAVORABLE'=> 5,
        'DECISION_NON_FAVORABLE'=> 6,
        'DECISION_AMBIGU'=> 7,
        'MANDAT_RECU'=> 8,
        'MANDAT_EN_INSTANCE'=> 9,
        'MANDAT_PAYEE'=> 10,
        'MANDAT_BLOQUE'=> 11,
    ];
    const ETAT_BOURSE_STAGE = [
        'EN_ATTENTE'=> 0,
        'ELIGIBLE'=> 1,
        'NON_ELIGIBLE'=> 2,
        'SUIVI_PAIEMENT'=> 4 ,
        'DECISION_FAVORABLE'=> 5,
        'DECISION_NON_FAVORABLE'=> 6,
        'DECISION_AMBIGU'=> 7,
        'MANDAT_RECU'=> 8,
        'MANDAT_EN_INSTANCE'=> 9,
        'MANDAT_PAYEE'=> 10,
        'MANDAT_BLOQUE'=> 11,
    ];
    const ETAT_AIDE_SOCIALE = [
        'EN_ATTENTE'=> 0,
        'ELIGIBLE'=> 1,
        'NON_ELIGIBLE'=> 2,
        'SUIVI_PAIEMENT'=> 4 ,
        'DECISION_FAVORABLE'=> 5,
        'DECISION_NON_FAVORABLE'=> 6,
        'DECISION_AMBIGU'=> 7,
        'MANDAT_RECU'=> 8,
        'MANDAT_EN_INSTANCE'=> 9,
        'MANDAT_PAYEE'=> 10,
        'MANDAT_BLOQUE'=> 11,
    ];

    const ETAT_DOCUMENT_STRING = [
        1 => 'EN_COURS',
        2 => 'CONFIRMEE_PAR_ETUDIANT',
        3 => 'CONFIRMEE_PAR_ADMIN',
        4 => 'REJETEE_PAR_ADMIN',
        5 => 'CONFIRMEE',
    ];
    const ETAT_DOCUMENT = [
        'EN_COURS'=> 1,
        'CONFIRMEE_PAR_ETUDIANT'=> 2,
        'CONFIRMEE_PAR_ADMIN'=> 3,
        'REJETEE_PAR_ADMIN'=> 4 ,
        'CONFIRMEE'=> 5,
    ];

    public $appends=[
        'etat_string',
        'etat_document_string',
        'contrat_file_url',
        'demande_last_annee_etude',
        'demande_dernier_annee_etude'
    ];

    public function getCurrentEtablissement()
    {
        $demande =  DemandeAnneeEtude::where('demande_id', $this->id)->orderBy('id', 'desc')->first();
        if($demande){
            $codeEtab = $demande->code_etab;
            $etablissement = Etablissement::where('code', $codeEtab)->first();

            return $etablissement;
        } else {
            return null;
        }

    }

    public function getContratFileUrlAttribute(){
        if ( $this->contrat_file ) {
            return asset('uploads/contrat_files/'.$this->id.'/'.$this->contrat_file);
        }
        return null;
    }
    public function getEtatStringAttribute(): ?string
    {
        return $this->etat ? self::ETAT_STRING[$this->etat] : '';
    }
    public function getEtatDocumentStringAttribute(): ?string
    {
        return $this->etat_document ? self::ETAT_DOCUMENT_STRING[$this->etat_document] : '';
    }

    protected $with = [
        'user',
        'demandeType',
        'classificationFinal',
        'classification',
        'anneeUniversitaire',
    ];
    protected $casts = [
        'contrat_end_date' => 'date',
        'control_fiscal_date' => 'date',
        'bs_start' => 'date',
        'bs_end' => 'date',
        'is_bourse' => 'boolean',
        'is_bourse_insertion' => 'boolean',
        'is_pret' => 'boolean',
        'is_aide_sociale' => 'boolean',
        'is_bourse_stage' => 'boolean',
    ];

    protected $fillable = [
        'code',
        'type',
        'code_catb',
        'code_decision',
        'etat',
        'etat_document',
        'etat_dossier',
        'etat_complement',

        'is_bourse' ,
        'is_bourse_insertion' ,
        'is_pret' ,
        'is_aide_sociale' ,
        'is_bourse_stage' ,

        'etat_bourse',
        'etat_bourse_insertion',
        'etat_pret',
        'etat_contrat',
        'comment_contrat_incomplet',

        'etat_bourse_stage',
        'etat_aide_sociale',

        'lot',
        'preparation_bourse_id',
        'preparation_insertion_id',
        'preparation_pret_id',
        'preparation_aide_sociale_id',
        'preparation_stage_id',

        'natdec_bourse',
        'natdec_insertion',
        'natdec_pret',
        'natdec_aide_sociale',
        'natdec_stage',

        'bs_nb_jour',
        'bs_start',
        'bs_end',

        'type_score',
        'situation_etudiant',
        'situation_familiale_dece',
        'situation_familiale_divorce',
        'situation_familiale_handicap',
        'compare_resultat',
        'score_total',


        'comment_refus_aide_sociale',
        'comment_refus_bourse_stage',
        'comment_refus_pret',
        'comment_refus_bourse_insertion',
        'comment_refus_bourse',

        'control_fiscal',
        'nbr_notif_control_fiscal',
        'control_fiscal_date',
        'contrat_file',
        'contrat_end_date',
        'contrat_montant',
        'config',
        'user_id',
        'annee_universitaire_id',
        'etudiant_annee_universitaire_id',
        'demande_type_id',
        'config_demande_type_id',
        'classification_id',
        'classification_final_id',

        'profession_final_id',

        'comment_refus',
        'comment_incoherent',
        'nbr_notif_incoherent',
        'comment_incomplete',
        'nbr_notif_incomplete',

        'type_calcule',
        'revenu_net',
        'revenu_annuel_pere',
        'revenu_annuel_mere',
        'nbr_freres_soeurs',
        'nbr_freres_soeurs_parraines',
        'nbr_freres_soeurs_handicapes',
        'nbr_freres_soeurs_unite',
        'nbr_freres_soeurs_parraines_unite',
        'nbr_freres_soeurs_handicapes_unite',
        'distance',
        'distance_unite',
        'revenu_annuel_conjoint',
        'current_code_etab',
        'num_bordereau',
        'centre_control_fiscal',
    ];

    public function demandeType() : BelongsTo
    {
        return $this->belongsTo(DemandeType::class, 'demande_type_id');
    }

    public function configDemandeType() : BelongsTo
    {
        return $this->belongsTo(ConfigDemandeType::class, 'config_demande_type_id');
    }

    public function classification() : BelongsTo
    {
        return $this->belongsTo(Classification::class, 'classification_id');
    }
    public function classificationFinal() : BelongsTo
    {
        return $this->belongsTo(Classification::class, 'classification_final_id');
    }

    public function anneeUniversitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class, 'annee_universitaire_id');
    }

    public function user() : BelongsTo
    {
        return $this->setConnection( config('database.secondConnection') )->belongsTo(User::class, 'user_id');
    }

    public function professionFinal() : BelongsTo
    {
        return $this->setConnection( config('database.default') )->belongsTo(Profession::class, 'profession_final_id');
    }

    public function demandeDocumentsClassifications(): HasMany
    {
        return $this->hasMany(DocumentClassificationDemande::class);
    }

    public function demandeAnneeEtudes(): HasMany
    {
        return $this->hasMany(DemandeAnneeEtude::class, 'demande_id');
    }

    public function getDemandeLastAnneeEtudeAttribute(): ?DemandeAnneeEtude
    {
        return DemandeAnneeEtude::with('etablissement')->where('demande_id', $this->id)->where('annee_universitaire_id', $this->annee_universitaire_id)->first();
    }

    public function getDemandeDernierAnneeEtudeAttribute(): ?DemandeAnneeEtude
    {
        return DemandeAnneeEtude::with('etablissement','anneeUniversitaire')
            ->where('demande_id', $this->id)
            ->orderBy(
                AnneeUniversitaire::select('title')
                ->from('annee_universitaires')
                ->whereColumn('annee_universitaires.id', 'demande_annee_etudes.annee_universitaire_id')
            , 'desc')
            ->skip(1)->take(1)->first();
    }
}
