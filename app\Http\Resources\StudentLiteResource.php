<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class StudentLiteResource extends JsonResource
{
    public static $wrap = null;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'firstName' => $this->firstName,
            'name' => $this->name,
            'name_ar' => $this->name_ar,
            'username' => $this->username,
            'email' => $this->email,
            'password' => $this->password,
            'phoneNumber' => $this->phoneNumber,
            'address' => $this->address,
            'annee_bac' => $this->annee_bac,
            'num_bac' => $this->num_bac,
            'cin' => $this->cin,
            'matricule' => $this->matricule,
            'num_passport' => $this->num_passport,
            'nationality_id' => $this->nationality_id,
            'country_id' => $this->country_id,
            'code_gouv' => $this->code_gouv,
            'sex' => $this->sex,
            'type' => $this->type,
            'date_naissance' => $this->date_naissance,
            'address_naissance' => $this->address_naissance,
            'status' => $this->status,

        ];
    }
}
