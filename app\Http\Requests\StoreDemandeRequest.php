<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDemandeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
//            'code' => 'required|string|max:55',
//            'etat' => 'required|string|max:55',
//            'config' => 'nullable|json',
            'user_id' => 'nullable|integer',
            ];
    }

//    /**
//     * Prepare inputs for validation.
//     *
//     * @return void
//     */
//    protected function prepareForValidation(): void
//    {
//        $this->merge([
//            'config' => json_encode($this->config),
//        ]);
//    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }

}
