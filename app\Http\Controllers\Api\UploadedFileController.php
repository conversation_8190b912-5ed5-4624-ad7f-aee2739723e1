<?php

namespace App\Http\Controllers\Api;

use App\Exports\DiplomeImport;
use App\Exports\DistanceImport;
use App\Exports\EtablissementDiplomeImport;
use App\Exports\EtablissementImport;
use App\Exports\FiliereImport;
use App\Exports\GouvernoratImport;
use App\Exports\LyceeImport;
use App\Exports\MontantPretImport;
use App\Exports\OfficeImport;
use App\Exports\ProfessionImport;
use App\Exports\StudentFromMesImport;
use App\Exports\StudentFromMesModelImport;
use App\Exports\StudentInternationnalImport;
use App\Exports\UniversiteImport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUploadedFileRequest;
use App\Http\Requests\UpdateUploadedFileRequest;
use App\Http\Resources\DocumentPredefiniResource;
use App\Http\Resources\UploadedFileResource;
use App\Jobs\NotifyUserOfCompletedImport;
use App\Models\DocumentClassificationDemande;
use App\Models\UploadedFile;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRowImport;

class UploadedFileController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $result = UploadedFileResource::collection(UploadedFile::all());
        if ($request->type) {
            $result = UploadedFileResource::collection(UploadedFile::where(['type' => $request->type])->get());
        }
        return $result;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Application|ResponseFactory|Response|JsonResponse
     */
    public function store(Request $request) : Application|ResponseFactory|Response|JsonResponse
    {
        $type = $request->type;
        $validator = Validator::make($request->all(), [
            'type' => 'required|string',
            'name' => 'required|string',
            'annee_universitaire' => 'sometimes|required_if:type,filiere',
            'annee_bac' => 'sometimes|required_if:type,studentFromMes',
            'document_file'=> 'required|mimes:xlx,xls,xlsx',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        if( $request->hasFile('document_file') ){
            $file = $request->file('document_file');
            if ($type === 'gouvernorat') {
                Excel::import(new GouvernoratImport, $file);
            }
            if ($type === 'gouvernorat_distance') {
                Excel::import(new DistanceImport, $file);
            }
            if ($type === 'diplome') {
                Excel::import(new DiplomeImport, $file);
            }
            if ($type === 'montantPret') {
                Excel::import(new MontantPretImport, $file);
            }
            if ($type === 'universite') {
                Excel::import(new UniversiteImport, $file);
            }
            if ($type === 'etablissement') {
                if ($request->is_diplome === 'true') {
                    Excel::import(new EtablissementDiplomeImport, $file);
                } else {
                    Excel::import(new EtablissementImport, $file);
                }
            }
            if ($type === 'filiere') {
                Excel::import(new FiliereImport($request->annee_universitaire), $file);
            }
            if ($type === 'lycee') {
                Excel::import(new LyceeImport, $file);
            }
            if ($type === 'profession') {
                Excel::import(new ProfessionImport, $file);
            }
            if ($type === 'office') {
                Excel::import(new OfficeImport, $file);
            }
            if ($type === 'internationalStudent') {
                Excel::import(new StudentInternationnalImport, $file);
            }
            if ($type === 'studentFromMes') {
                $annee_bac = $request->annee_bac;
                $annee_universitaire = DB::table('annee_universitaires')->where('annee_bac',$annee_bac)->first();
                $headings = (new HeadingRowImport(1))->toArray($file);
                Validator::make(array_flip($headings[0][0]),
                    [
                        'nbac' => 'required',
                        'cin' => 'required',
                        'nom_a' => 'required',
                        'nom_l' => 'required',
                        'jj' => 'required',
                        'mm' => 'required',
                        'aa' => 'required',
                        'cd_lyc' => 'required',
                        'cd_gouv' => 'required',
                        'sex' => 'required',
                        'prof' => 'required',
                        'code_filiere' => 'required',
                        'tour' => 'nullable',

                    ],
                    [],
                    [
                        'nbac' => '(nbac)',
                        'cin' => '(cin)',
                        'nom_a' => '(nom_a)',
                        'nom_l' => '(nom_l)',
                        'jj' => '(jj)',
                        'mm' => '(mm)',
                        'aa' => '(aa)',
                        'cd_lyc' => '(cd_lyc)',
                        'cd_gouv' => '(cd_gouv)',
                        'sex' => '(sex)',
                        'prof' => '(prof)',
                        'code_filiere' => '(code_filiere)',
                        'tour' => '(tour)',
                    ]
                )->validate();
                Validator::make($request->all(),
                    [
                        'annee_bac' => function($attribute, $value, $onFailure) use ($annee_universitaire) {
                            if (!$annee_universitaire?->id) {
                                $onFailure('Cette année de bac ne correspond à aucun année universitaire ');
                            }
                        },
                    ],
                    [],
                    [
                        'annee_bac' => ' Année de bac ',
                    ]
                )->validate();
                Excel::import(new StudentFromMesModelImport($request->annee_bac, $annee_universitaire?->id), $file)
                    ->chain([ new NotifyUserOfCompletedImport(auth()->user()), ]);
//                (new StudentFromMesModelImport($request->annee_bac))->queue($file)->chain([
//                    new NotifyUserOfCompletedImport(auth()->user),
//                ]);
//                Excel::import(new StudentFromMesModelImport, $file);
//                (new StudentFromMesModelImport)->import($file, 'local', \Maatwebsite\Excel\Excel::XLSX);


//                use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
//                use PhpOffice\PhpSpreadsheet\Writer\Csv;
//                $reader = new Xlsx();
//                $reader->setReadDataOnly(true);
//                $spreadsheet = $reader->load($file);
//
//                $writer = (new Csv($spreadsheet))
//                    ->setEnclosure('')
//                    ->setLineEnding("\n")
//                    ->setDelimiter(';');
//                $writer->setSheetIndex(0);
//                $writer->save('csvcsv.csv');
            }

            $file = $request->file('document_file');
            $file_name=$request->name.'_'.time().'.'.$request->document_file->extension();

            $file->storeAs('/uploads/uploaded_files/'.$type.'/', $file_name);
//            $file->move(public_path('/uploads/uploaded_files/'.$request->type),$file_name);

        }
        else {
            $file_name=null;
        }
        $documentPredefini = UploadedFile::create([
            'type' => $request->type,
            'name' => $request->name,
            'annee_universitaire' => $request->annee_universitaire,
            'document_file' => $file_name
        ]);

        return response(new UploadedFileResource($documentPredefini) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateUploadedFileRequest $request
     * @param UploadedFile $uploadedFile
     * @return Application|ResponseFactory|Response|JsonResponse
     */
    public function edit(UpdateUploadedFileRequest $request , UploadedFile $uploadedFile) : Application|ResponseFactory|Response|JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string',
            'name' => 'required|string',
            'document_file'=> 'required|mimes:xlx,xls,xlsx',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }
        if($request->hasFile('document_file')){
            if($uploadedFile->document_file){
                $old_path=public_path().'/uploads/uploaded_files/'.$uploadedFile->type.'/'.$uploadedFile->document_file;
                if(File::exists($old_path)){
                    File::delete($old_path);
                }
            }
            $file = $request->file('document_file');
            $file_name='document_'.time().'.'.$request->document_file->extension();

            $file->storeAs('/uploads/uploaded_files/'.$uploadedFile->type.'/', $file_name);

        } else {
            $file_name=$uploadedFile->document_file;
        }
        $uploadedFile->update([
            'name' => $request->name,
            'type' => $request->type,
            'document_file' => $file_name
        ]);

        return response(new UploadedFileResource($uploadedFile) , 201);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UploadedFile  $uploadedFile
     * @return \Illuminate\Http\Response
     */
    public function destroy(UploadedFile $uploadedFile)
    {
        $uploadedFile->delete();
        return response("ok", 204);
    }

    public function getUploadedFileDocument(Request $request)
    {
        $request->validate([
            'id' => 'required',
        ]);

        $doc =UploadedFile::findOrFail($request->id);
        if (!$doc) {
            return response()->json('Document not found',404);
        }

        $filename = $doc->document_file;
        $path= '/uploads/uploaded_files/'.$request->type.'/'. $filename;

        if (!Storage::exists($path)) {
            return response()->json(Storage::path($path), 404);
        }
        return Storage::download($path, $filename);

    }
}
