<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttestationTypeHasAttestationTypeDocumentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 5,
            'documents_attestations_id' => 4
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 5,
            'documents_attestations_id' => 7
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 2,
            'documents_attestations_id' => 2
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 2,
            'documents_attestations_id' => 8
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 3,
            'documents_attestations_id' => 1
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 3,
            'documents_attestations_id' => 2
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 1,
            'documents_attestations_id' => 9
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 1,
            'documents_attestations_id' => 8
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 4,
            'documents_attestations_id' => 9
        ]);

        DB::table('attest_type_has_doc_attest')->insert([
            'attestation_types_id' => 4,
            'documents_attestations_id' => 8
        ]);
    }
}
