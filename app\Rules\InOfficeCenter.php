<?php

namespace App\Rules;

use App\Models\Etablissement;
use Illuminate\Contracts\Validation\InvokableRule;

class InOfficeCenter implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail): void
    {
        $etablisement = Etablissement::where('code', $value)->first();
        if (!$etablisement || $etablisement->code_office !== 'C') {
            $fail('validation.in_office_center')->translate();
        }
    }
}
