<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StatRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'num_dec' => 'required|string',
            'date_payment' => 'required',
            'annee_universitaire_id' => 'required|exists:annee_universitaires,id',
            'tranche' => 'required|string',
            'nbr_mois' => 'required|string',
            'title' => 'nullable|string',
            'mnt' => 'nullable|string'
        ];
    }
}
