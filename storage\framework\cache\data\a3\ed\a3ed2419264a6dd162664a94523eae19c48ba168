1748264816O:58:"Illuminate\Http\Resources\Json\AnonymousResourceCollection":8:{s:8:"resource";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:3:{i:0;O:41:"App\Http\Resources\ProfessionTypeResource":4:{s:8:"resource";O:25:"App\Models\ProfessionType":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"bpasBack.profession_types";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:4:"code";s:1:"1";s:4:"name";s:20:"Ordinary professions";s:7:"name_fr";s:19:"Métiers ordinaires";s:7:"name_ar";s:29:"الوظائف العادية";s:10:"created_at";s:19:"2024-01-09 16:43:43";s:10:"updated_at";s:19:"2024-01-09 16:43:43";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:1;s:4:"code";s:1:"1";s:4:"name";s:20:"Ordinary professions";s:7:"name_fr";s:19:"Métiers ordinaires";s:7:"name_ar";s:29:"الوظائف العادية";s:10:"created_at";s:19:"2024-01-09 16:43:43";s:10:"updated_at";s:19:"2024-01-09 16:43:43";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:4:"code";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:1;O:41:"App\Http\Resources\ProfessionTypeResource":4:{s:8:"resource";O:25:"App\Models\ProfessionType":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"bpasBack.profession_types";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2;s:4:"code";s:1:"2";s:4:"name";s:40:"Professions in the ministry of education";s:7:"name_fr";s:38:"Métiers du ministère de l'éducation";s:7:"name_ar";s:58:"المهن في وزارة التربية والتعليم";s:10:"created_at";s:19:"2024-01-09 16:44:45";s:10:"updated_at";s:19:"2024-01-09 16:44:45";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:2;s:4:"code";s:1:"2";s:4:"name";s:40:"Professions in the ministry of education";s:7:"name_fr";s:38:"Métiers du ministère de l'éducation";s:7:"name_ar";s:58:"المهن في وزارة التربية والتعليم";s:10:"created_at";s:19:"2024-01-09 16:44:45";s:10:"updated_at";s:19:"2024-01-09 16:44:45";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:4:"code";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:2;O:41:"App\Http\Resources\ProfessionTypeResource":4:{s:8:"resource";O:25:"App\Models\ProfessionType":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"bpasBack.profession_types";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:3;s:4:"code";s:1:"3";s:4:"name";s:47:"Professions of the ministry of higher education";s:7:"name_fr";s:51:"Métiers du ministère de l'enseignement supérieur";s:7:"name_ar";s:66:"المهن التابعة لوزارة التعليم العالي";s:10:"created_at";s:19:"2024-01-09 16:45:37";s:10:"updated_at";s:19:"2024-01-09 16:45:45";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:3;s:4:"code";s:1:"3";s:4:"name";s:47:"Professions of the ministry of higher education";s:7:"name_fr";s:51:"Métiers du ministère de l'enseignement supérieur";s:7:"name_ar";s:66:"المهن التابعة لوزارة التعليم العالي";s:10:"created_at";s:19:"2024-01-09 16:45:37";s:10:"updated_at";s:19:"2024-01-09 16:45:45";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:4:"code";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:8:"collects";s:41:"App\Http\Resources\ProfessionTypeResource";s:10:"collection";r:2;s:29:" * preserveAllQueryParameters";b:0;s:18:" * queryParameters";N;s:12:"preserveKeys";b:1;}