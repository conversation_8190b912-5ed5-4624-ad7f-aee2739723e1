<?php

namespace App\Jobs;

use App\Exports\PreparationExport;
use App\Exports\PreparationViewExport;
use App\Models\Admin;
use App\Models\Demande;
use App\Models\ExportedFile;
use App\Models\Preparation;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\FastExcel;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Str;
use XLSXWriter;

class ProcessPreparationExport implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $preparation;
    public $changeStatus;

    public function __construct(?Admin $user, ?Preparation $preparation, ?string $changeStatus)
    {
        $this->user = $user;
        $this->preparation = $preparation;
        $this->changeStatus = $changeStatus;
    }

    function demandesGenerator($preparation_type) {
        foreach (Demande::where($preparation_type, $this->preparation->id)
                     ->with(['user', 'demandeType', 'anneeUniversitaire', 'professionFinal'])
                     ->without('classificationFinal', 'classification')
                     ->cursor() as $d) {
            yield $d;
        }
    }

    public function handle()
    {

        try {


            $preparation = Preparation::find($this->preparation->id);
            $type = $this->preparation?->type;
            if ($type) {
                $preparation_type = '';
                switch ($type) {
                    case 'bourse':
                        $preparation_type = 'preparation_bourse_id';
                        break;
                    case 'insertion':
                        $preparation_type = 'preparation_insertion_id';
                        break;
                    case 'pret':
                        $preparation_type = 'preparation_pret_id';
                        break;
                    case 'aide_sociale':
                        $preparation_type = 'preparation_aide_sociale_id';
                        break;
                    case 'stage':
                        $preparation_type = 'preparation_stage_id';
                        break;
                }
                if ($preparation_type) {
                    $decisTypeObj = ['bourse' => 'B', 'insertion' => 'B', 'pret' => 'P', 'aide_sociale' => 'A', 'stage' => 'S'];

                    $roww = ExportedFile::create([
                        'type' => $this->preparation?->type,
                        'vue' => 0,
                        'etat' => 'en_cours',
                    ]);
//                    $demandes = Demande::where($preparation_type, $this->preparation->id)
//                        ->with(['user', 'demandeType', 'anneeUniversitaire', 'professionFinal'])
//                        ->without('classificationFinal', 'classification')
//                        ->get();

//                    $header = [
//                        'CIN',
//                        'CATB',
//                        'LOT',
//                        'NOM',
//                        'DAT_NAISS',
//                        'GOUV_N',
//                        'SEXE',
//                        'PROFP',
//                        'ANET',
//                        'DIS',
//                        'FAC',
//                        'UNIV',
//                        'REVP',
//                        'REVM',
//                        'AVIS',
//                        'RES',
//                        'MOY',
//                        'DECIS',
//                    ];
//                    $writer = SimpleExcelWriter::create('/storage/uploads/exportedFile/preparation_' . $preparation->ndec_code . '_' . $preparation->type . '_' . $preparation->id . '.xlsx')
//                        ->addHeader($header);
                    (new FastExcel($this->demandesGenerator($preparation_type)))->export(storage::path('uploads/exportedFile/preparation_' . $preparation->ndec_code . '_' . $preparation->type . '_' . $roww->id . '.xlsx'), function ($demande) use ($type, $decisTypeObj) {
                        $moy = $demande->demande_dernier_annee_etude?->moyenne;
                        if (is_numeric($moy)) {
                            $moy = $moy * 100;
                            $moy = $moy > 0 ? Str::padLeft($moy, 4, '0') : '0000';
                        }else {
                            $moy = '0000';
                        }
                      if ($demande->demandeType->code === 'bourses_de_stage') {
                        return [
                          'CIN'=> $demande->user ? $demande->user?->type === 'tunisien' ? $demande->user?->cin : $demande->user?->matricule : '',
                          'CATB'=> $demande->code_catb ?? '',
                          'LOT'=> $demande->lot,
                          'NOM'=> $demande->user ? $demande->user->name . ' ' . ($demande->user->firstName ? ' ' . $demande->user->firstName : '') : '',
                          'DAT_NAISS'=> $demande->user ? $demande->user->date_naissance ? Carbon::parse($demande->user->date_naissance)->format('dmy') : '' : '',
                          'GOUV_N'=> $demande->user ? $demande->user->code_gouv : '',
                          'SEXE'=> $demande->user ? $demande->user->sex : '',
                          'PROFP'=> $demande->professionFinal->code,
                          'ANET'=> $demande->demande_last_annee_etude->annee_etude,
                          'DIS'=> $demande->demande_last_annee_etude->code_diplome,
                          'FAC'=> $demande->demande_last_annee_etude->code_etab,
                          'UNIV'=> '6',
                          'REVP'=> $demande->revenu_net > 0 ? Str::padLeft($demande->revenu_net, 5, '0') : '00000',
                          'REVM'=> '00000',
                          'AVIS'=> $decisTypeObj[$type],
                          'RES'=> $demande->demandeType->group === 'nouveau_bachelier' ? '1' : ($demande->demande_dernier_annee_etude?->resultat_success ? '1' : '0'),
                          'MOY'=> $moy,
                          'DECIS'=> 'A',
                          'NBR_JOURS' => $demande->bs_nb_jour - 1,
                        ];
                      }
                        return [
                            'CIN'=> $demande->user ? $demande->user?->type === 'tunisien' ? $demande->user?->cin : $demande->user?->matricule : '',
                            'CATB'=> $demande->code_catb ?? '',
                            'LOT'=> $demande->lot,
                            'NOM'=> $demande->user ? $demande->user->name . ' ' . ($demande->user->firstName ? ' ' . $demande->user->firstName : '') : '',
                            'DAT_NAISS'=> $demande->user ? $demande->user->date_naissance ? Carbon::parse($demande->user->date_naissance)->format('dmy') : '' : '',
                            'GOUV_N'=> $demande->user ? $demande->user->code_gouv : '',
                            'SEXE'=> $demande->user ? $demande->user->sex : '',
                            'PROFP'=> $demande->professionFinal->code,
                            'ANET'=> $demande->demande_last_annee_etude->annee_etude,
                            'DIS'=> $demande->demande_last_annee_etude->code_diplome,
                            'FAC'=> $demande->demande_last_annee_etude->code_etab,
                            'UNIV'=> '6',
                            'REVP'=> $demande->revenu_net > 0 ? Str::padLeft($demande->revenu_net, 5, '0') : '00000',
                            'REVM'=> '00000',
                            'AVIS'=> $decisTypeObj[$type],
                            'RES'=> $demande->demandeType->group === 'nouveau_bachelier' ? '1' : ($demande->demande_dernier_annee_etude?->resultat_success ? '1' : '0'),
                            'MOY'=> $moy,
                            'DECIS'=> 'A',
                        ];
                    });
//                    /** @var Demande $demande */
//                    foreach ($demandes as $demande) {
//                        $writer->addRow( [
//                            $demande->user ? $demande->user?->type === 'tunisien' ? $demande->user?->cin : $demande->user?->matricule : '',
//                            $demande->code_catb ?? '',
//                            $demande->lot,
//                            $demande->user ? $demande->user->name . ' ' . ($demande->user->firstName ? ' ' . $demande->user->firstName : '') : '',
//                            $demande->user ? $demande->user->date_naissance ? Carbon::parse($demande->user->date_naissance)->format('DDMMYY') : '' : '',
//                            $demande->user ? $demande->user->code_gouv : '',
//                            $demande->user ? $demande->user->sex : '',
//                            $demande->professionFinal->code,
//                            $demande->demande_last_annee_etude->annee_etude,
//                            $demande->demande_last_annee_etude->code_diplome,
//                            $demande->demande_last_annee_etude->code_etab,
//                            '6',
//                            $demande->revenu_net > 0 ? Str::padLeft($demande->revenu_net, 5, '0') : '00000',
//                            '00000',
//                            $decisTypeObj[$type],
//                            $demande->demandeType->group === 'nouveau_bachelier' ? '1' : ($demande->demande_dernier_annee_etude?->resultat_success ? '1' : '0'),
//                            $demande->demande_dernier_annee_etude?->moyenne ?? '',
//                            'A'
//                        ]);
//                    }
//
//                    $writer->close();

                    dispatch(new NotifyUserOfCompletedPreparationExport($this->user, $roww->type, $roww->id, $this->preparation->ndec_code));
                }
                if ($this->changeStatus === 'ok') {
                    $preparation->etat = Preparation::ETAT['EN_COURS'];
                    $preparation->save();
                }
            }
            else{
                Log::error("this->preparation->type is null ");
            }

        } catch (\Exception $e) {
            Log::error($e->getFile() . $e->getLine() . $e->getMessage());
        }

    }
}
