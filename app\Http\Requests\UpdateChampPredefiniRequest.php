<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateChampPredefiniRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function rules(): array
    {
        return [
            'label' => 'required|string',
            'label_fr' => 'required|string',
            'label_ar' => 'required|string',
            'name' => 'nullable|string|alpha_dash',
            'type' => 'required|string',
            'help' => 'nullable|string',
            'help_fr' => 'nullable|string',
            'help_ar' => 'nullable|string',
            'showLabel' => 'nullable|boolean',
            'required' => 'nullable|boolean',
            'affectClassification' => 'nullable|boolean',
            'showInRow' => 'nullable|boolean',
            'choices' => 'nullable|array',
        ];
    }


    protected function prepareForValidation(): void
    {
        $this->merge([
            'showLabel' => Helpers::toBoolean($this->showLabel),
            'required' => Helpers::toBoolean($this->required),
            'affectClassification' => Helpers::toBoolean($this->affectClassification),
            'showInRow' => Helpers::toBoolean($this->showInRow),
            ]);
    }
}
