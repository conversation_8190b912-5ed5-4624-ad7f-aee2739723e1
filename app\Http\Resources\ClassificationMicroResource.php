<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class ClassificationMicroResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'title' => $this->title,
            'title_fr' => $this->title_fr,
            'title_ar' => $this->title_ar,
            'active' => $this->active,
            'classable_par_admin' => $this->classable_par_admin,
            'classable' => $this->classable,
            'document_online' => $this->document_online,
            'show_documents' => $this->show_documents,
            'config' => $this->config,
//            'last_demande_type_config' => $this->last_demande_type_config,
//            'config_array' => json_decode($this->config, true),
//            'fils' => ClassificationMicroResource::collection($this->fils),
            'demande_type_id' => $this->demande_type_id,
            'demande_type' => new DemandeTypeForClassificationResource($this->demandeType),
            'parent_id' =>$this->parent_id,
            'parent' => $this->parent,
            'profession_id' =>$this->profession_id,
            'profession' => $this->profession,
            'created_at' => $this->created_at->format('Y-m-d'),
        ];
    }
}
