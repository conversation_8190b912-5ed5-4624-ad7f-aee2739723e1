<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use OwenIt\Auditing\Models\Audit as ModelsAudit;

class Audit extends ModelsAudit
{

    //protected $fillable = ['old_values', 'new_values', 'event', 'auditable_id', 'auditable_type', 'tags', 'user_id', 'user_type', 'ip_address'];
    protected $guarded = [];


    protected $with = [
        "user"
    ];

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    public function user() : BelongsTo {
        return $this->belongsTo(Admin::class, 'user_id');
    }
}
