<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ClassificationMicroResource;
use App\Http\Resources\ClassificationResource;
use App\Models\Classification;
use App\Http\Requests\StoreClassificationRequest;
use App\Http\Requests\UpdateClassificationRequest;
use App\Models\DemandeType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class ClassificationController extends Controller
{
    protected $cache_seconds = 900;

    public function __construct()
    {
//        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {

        $classifications =  Cache::remember('Classification', $this->cache_seconds, function () {
            return Classification::with('parent')->where('active', true);
        });


        return response()->json([
            "data"=> ClassificationMicroResource::collection($classifications->get()),
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function arbre(): JsonResponse
    {
        $typeTree = Cache::remember('ClassificationTree', $this->cache_seconds, function () {

            $cs = Classification::where('parent_id', null)->with('children')->without('parent','fils')->get();
            $typeTree = [];
            foreach ($cs as $type) {
                if ($type) {
                    $typeTree[] = ['id' => $type->id, 'level' => 1, 'title' => $type->title, 'title_fr' => $type->title_fr, 'title_ar' => $type->title_ar, 'parent_id' => $type->parent_id, 'demande_type_id' => $type->demande_type_id, 'count_fils'=>$type->children?->count() ?? 0];
                    if ($type?->children) {
                        foreach ($type->children as $child) {
                            $typeTree[] = ['id' => $child->id, 'level' => 2, 'title' => $child->title, 'title_fr' => $child->title_fr, 'title_ar' => $child->title_ar,'parent_id' => $child->parent_id, 'demande_type_id' => $type->demande_type_id, 'count_fils'=>$child->children?->count() ?? 0];
                            if ($child->children) {
                                foreach ($child->children as $childd) {
                                    $typeTree[] = ['id' => $childd->id, 'level' => 3, 'title' => $childd->title, 'title_fr' => $childd->title_fr, 'title_ar' => $childd->title_ar,  'parent_id' => $childd->parent_id, 'demande_type_id' => $type->demande_type_id, 'count_fils'=>$childd->children?->count() ?? 0];
                                    if ($childd->children) {
                                        foreach ($childd->children as $childdd) {
                                            $typeTree[] = ['id' => $childdd->id, 'level' => 4, 'title' => $childdd->title, 'title_fr' => $childdd->title_fr, 'title_ar' => $childdd->title_ar,  'parent_id' => $childdd->parent_id, 'demande_type_id' => $type->demande_type_id, 'count_fils'=>$childdd->children?->count() ?? 0];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return $typeTree;
        });

        return response()->json([ "data"=> $typeTree ]);
    }

}
