<?php

namespace App\Notifications;

use App\Models\ExportedFile;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class SyncCompletedNotification extends Notification
{
    use Queueable;

    public $syncCount;
    public $ndec_code;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($syncCount, $ndec_code)
    {
        $this->syncCount = $syncCount;
        $this->ndec_code = $ndec_code;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }
//        $syncCount = ['decision_favorable' => 0, 'decision_non_favorable' => 0, 'decision_ambigu' => 0];

        return [
            "title" => "Sync Completed ". $this->ndec_code,
            "subtitle" => " Sync Completed ". $this->ndec_code . ": - decision favorable : ". $this->syncCount['decision_favorable'] . "  - décision non favorable : ". $this->syncCount['decision_non_favorable'] . " - decision ambigu : ". $this->syncCount['decision_ambigu'] . " , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Synchronisation Complete ". $this->ndec_code,
            "subtitle_fr" => " Synchronisation Complete ". $this->ndec_code . " :  - decision favorable : ". $this->syncCount['decision_favorable'] . " - décision non favorable : ". $this->syncCount['decision_non_favorable'] . " - decision ambigu : ". $this->syncCount['decision_ambigu'] . " , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => " تم الإصدار ". $this->ndec_code,
            "subtitle_ar" => " اكتملت المزامنة ". $this->ndec_code . " :   - قرارات مواتية : ". $this->syncCount['decision_favorable'] . " - قرارات غير مناسبة  : ". $this->syncCount['decision_non_favorable'] . " - قرارات غامضة : ". $this->syncCount['decision_ambigu'] . " , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "refresh",
            "avatarAlt" => "Synchronisation",
            "avatarText" => "Synchronisation",
            "avatarColor" => "info",
            "type" => "Synchronisation",
            "target_id" => '',
            "target" => '',
            "model" => "ExportedFile",
            "url" => '',

        ];
    }
}
