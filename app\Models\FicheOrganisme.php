<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Auditable as AuditableTrait;
use OwenIt\Auditing\Contracts\Auditable;

class FicheOrganisme extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'organisme',
        'code_organisme_id',
        'type',
        'date_emission',
        'nb_total',
        'montant_total',
        'tax_total',
        'premier',
        'dernier',
        'montant_ttc',
        'fichier',
        'annee_universitaire_id',
        'num_decision',
        'office',

    ];

    public function annee_universitaire()
    {
        return $this->belongsTo(AnneeUniversitaire::class, 'annee_universitaire_id');
    }
    public function code_organisme()
    {
        return $this->belongsTo(CodeOrganisme::class, 'code_organisme_id');
    }
}
