<?php

namespace App\Notifications;

use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class ImportCompletedNotification extends Notification
{
    use Queueable;


    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Import Completed",
            "subtitle" => " Import Completed at ". Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Import Complété",
            "subtitle_fr" => "Import Complété à " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => " تم الإستيراد",
            "subtitle_ar" => " تم الإستيراد في  " . Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "text-plus",
            "avatarAlt" => "Import",
            "avatarText" => "Import",
            "avatarColor" => "info",
            "type" => "import",
            "target_id" => '',
            "target" => "",
            "model" => "",
            "url" => "",

        ];
    }
}
