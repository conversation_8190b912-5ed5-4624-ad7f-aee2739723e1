<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('group_conditions', function (Blueprint $table) {
            $table->id();
            $table->json('discip');
            $table->json('anet')->nullable();
            $table->unsignedBigInteger('group_id');
            $table->timestamps();
            $table->foreign('group_id','group_conditions_group_id_foreign')
            ->references('id')
            ->on('stat_groups');
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('group_conditions');
    }
};
