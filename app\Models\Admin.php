<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use App\Traits\Override\Notifiable;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Admin extends Authenticatable implements Auditable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels {
        AuditableTrait::transformAudit as parentTransformAudit;
    }
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    protected $with = [
        'roles.permissions'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'firstName',
        'name',
        'email',
        'password',
        'username',
        'role',
        'profile_photo',
        'status',
        'office_id'
    ];



    public $appends=[
        'profile_image_url',
    ];
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class, 'office_id');
    }

    public function getProfileImageUrlAttribute(){
        if($this->profile_photo){
            return asset('/uploads/profile_images/'.$this->profile_photo);
        }

        return 'https://ui-avatars.com/api/?background=random&name='.urlencode($this->name);

    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'model_has_roles', 'model_id', 'role_id')
            ->withPivot('start_at', 'end_at');
    }

    public function hasRoleWithinPeriod($role)
    {
        $startDate = $role->pivot->start_at ? Carbon::parse($role->pivot->start_at)->subDay() : null;
        $endDate = $role->pivot->end_at ? Carbon::parse($role->pivot->end_at)->addDay() : null;
        $today = Carbon::now();
        // Check if the start and end dates are null or if today is within the date range
        // between(start,end,incluive): true mean that the 2 dates are included in the check
        if (($startDate == null && $endDate == null) || ($today->between($startDate, $endDate, true))) {
            return true;
        }

        return false;
    }

    public function hasRoleToday()
    {
        $today = Carbon::today();

        $roles = $this->roles()
            ->where(function ($query) use ($today) {
                $query->where(function ($query) use ($today) {
                    $query->whereNull('model_has_roles.start_at')
                        ->orWhereDate('model_has_roles.start_at', '<=', $today);
                })
                ->where(function ($query) use ($today) {
                    $query->whereNull('model_has_roles.end_at')
                        ->orWhereDate('model_has_roles.end_at', '>=', $today);
                });
            })->get();

        return count($roles) > 0;
    }

    public function getMorphClass(): string
    {
        return __CLASS__;
    }
}
