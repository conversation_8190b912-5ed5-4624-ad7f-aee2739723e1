<?php

namespace App\Notifications;

use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class ImportHasFailedNotification extends Notification
{
    use Queueable;

    public function __construct(private \Throwable $exception)
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
            \Log::error($e->getMessage());
        }

        return [
            "title" => "Import Failed",
            "subtitle" => " Import Failed at ". Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Import échoué",
            "subtitle_fr" => "Import échoué à " .  Carbon::now()->format('d/m/y H:i:s')." , message : ".$this->exception->getMessage(),
            "title_ar" => " فشل الإستيراد",
            "subtitle_ar" => " فشل الإستيراد في  " . Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "file-alert",
            "avatarAlt" => "Import",
            "avatarText" => "Import",
            "avatarColor" => "error",
            "type" => "import",
            "target_id" => '',
            "target" => "",
            "model" => "",
            "url" => "",

        ];
    }
}
