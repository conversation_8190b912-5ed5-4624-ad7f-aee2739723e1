<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Admin;
use Illuminate\Http\Response;
use App\Http\Requests\StoreRoleRequest;
use App\Http\Requests\UpdateRoleRequest;
use App\Http\Resources\RoleResource;
use App\Models\DemandeType;
use App\Models\DemandeTypeHasRole;
use App\Models\Role;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $roles = Role::with('demande_types')->get();
        foreach ($roles as $role) {
            $permissions = $role->permissions;
        }
        return $roles;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreRoleRequest $request
     * @return RoleResource
     */
    public function store(StoreRoleRequest $request): Response
    {
        // if (auth()->user()?->can('create admin_roles_permissions')) {

            $data = $request->validated();
            $role = Role::create(['name' => $data['name'],'guard_name'=>'web']);
            $role->givePermissionTo('read admin_home');
            $role->givePermissionTo('read account');
            $role->givePermissionTo('update account');
            $role->givePermissionTo($data['permissions']);

            // sections
            if($role->hasAnyPermission(['read admin_bureau_ordre', 'read admin_historique_etudiant', 'read admin_guichet', 'read admin_bourse_universitaire', 'read admin_pret_universitaire', 'read admin_bourse_stage', 'read admin_aide_sociale', 'read admin_bourse_alternance'])
                || $role->hasAnyPermission(['read agent_bureau_ordre', 'read agent_guichet', 'read agent_bourse_universitaire', 'read agent_pret_universitaire', 'read agent_bourse_stage', 'read agent_aide_sociale', 'read agent_bourse_alternance']))
            {
                $role->givePermissionTo('read admin_section_bourse');
            }
            if($role->hasAnyPermission(['read admin_preparation_paiement', 'read admin_suivi_paiement', 'read admin_generation_des_fichiers'])
                || $role->hasAnyPermission(['read agent_preparation_paiement', 'read agent_suivi_paiement', 'read agent_generation_des_fichiers']))
            {
                $role->givePermissionTo('read admin_section_paiement');
            }
            if($role->hasAnyPermission(['read admin_absences', 'read admin_retrait_inscription', 'read admin_demandes_recouvrements', 'read admin_reclamations', 'read admin_attestations', 'read admin_rectificatifs'])
                || $role->hasAnyPermission(['read agent_absences', 'read agent_retrait_inscription', 'read agent_demandes_recouvrements', 'read agent_reclamations', 'read agent_attestations', 'read agent_rectificatifs']))
            {
                $role->givePermissionTo('read admin_section_autres');
            }
            if($role->hasAnyPermission(['read admin_types_demandes','read admin_classifications', 'read admin_attestations_type', 'read admin_historiques', 'read admin_fiches_organismes', 'read admin_configuration', 'read admin_users', 'read admin_roles_permissions'])
                || $role->hasAnyPermission(['read agent_types_demandes','read agent_classifications', 'read agent_attestations', 'read agent_historiques', 'read agent_fiches_organismes', 'read agent_configuration', 'read agent_users', 'read agent_roles_permissions'])
            ){
                $role->givePermissionTo('read admin_section_administration');
            }


            if($request->has('demande_types')){
                foreach ($request->demande_types as $key => $value) {
                    DemandeTypeHasRole::create([
                        'demande_type_id'=>$value,
                        'role_id'=>$role->id,
                    ]);
                }

            }

            return response(new RoleResource($role) , 201);

        // }else{
        //     return response([
        //         'errors' => [
        //             "name"=>[__('auth.NotAuthorized')]
        //         ]
        //     ], 422);
        // }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  UpdateRoleRequest  request
     * @return \Illuminate\Http\Response
     */
    public function edit(UpdateRoleRequest $request): Response
    {
        // if (auth()->user()?->can('update admin_roles_permissions')) {

        $data = $request->validated();
        $role = Role::where('name', $data['oldName'])->first();
        $role->name = $data['name'];
        $role->syncPermissions($data['permissions']);

        $role->givePermissionTo('read admin_home');
        $role->givePermissionTo('read account');
        $role->givePermissionTo('update account');
        $role->save();

        if($request->has('demande_types')){
            // delete old values
            DemandeTypeHasRole::where('role_id', $role->id)->delete();

            // give new access to requested demandes
            foreach ($request->demande_types as $key => $value) {
                DemandeTypeHasRole::create([
                    'demande_type_id'=>$value,
                    'role_id'=>$role->id,
                ]);
            }

        }

        return response(new RoleResource($role) , 201);

        // }else{
        //     return response([
        //         'errors' => [
        //             "name"=>[__('auth.NotAuthorized')]
        //         ]
        //     ], 422);
        // }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id): Response
    {
        // if (auth()->user()?->can('delete admin_roles_permissions')) {

        $role = Role::where('id', $id)->first();
        $users = Admin::role($role->name)->get();

        DemandeTypeHasRole::where('role_id', $role->id)->delete();

        foreach ($users as $user) {
            if(count($user->roles) > 1 ){
                $user->removeRole($role);
                $user->save();
            }else{
                $user->removeRole($role);
                $user->status = 0;
                $user->save();
            }
        }
        $role->users()->detach();
        $role->delete();

        return response(new RoleResource($role) , 201);

        // }else{
        //     return response([
        //         'errors' => [
        //             "name"=>[__('auth.NotAuthorized')]
        //         ]
        //     ], 422);
        // }
    }

    public function grantDemandeTypesAccess(Request $request){
        $request->validate([
            'role_id' => 'required',
            'demandes_types' => 'required|array'
        ]);
        $errors = ["Success"];

        // grant access to all predefined types
        for ($i=1; $i < 35; $i++) {
            # code...
            try {
                //code...
                DemandeTypeHasRole::create([
                    'demande_type_id'=>$i,
                    'role_id'=>$request->role_id,
                ]);
            } catch (\Throwable $th) {
                array_push($errors, $th->getMessage()); // foreign_id check fails
            }

        }

        // grant access to specific types
        /*foreach ($request->demande_types as $key => $value) {
            DemandeTypeHasRole::create([
                'demande_type_id'=>$value['id'],
                'role_id'=>$request->role_id,
            ]);
        }*/

        return response($errors , 201);
    }
}
