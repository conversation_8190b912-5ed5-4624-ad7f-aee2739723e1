<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use App\Models\AttestationType;
use App\Models\Demande;
use App\Models\DemandeType;
use App\Models\RetraitInscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DemandeExport implements FromCollection,WithHeadings, ShouldQueue
{
    use Exportable;

    private mixed $request;
    private mixed $id;

    public function __construct($request,$id){
        $this->request = $request;
        $this->id = $id;
    }


    public function headings():array{
        return[
            'nom',
            'nom Ar',
            'annee_universitaire',
            'demandeType',
            'classification',
            'classification_final',
            'profession',
            'code',
            'code catb',
            'code decision',
            'etat',
            'etat_dossier',
            'etat_complement',
            'lot',
        ];
    }

    public function collection()
    {
        $request = $this->request;
        if ($request['demande_type_code'] === 'bureau_ordre') {
            $demandes = Demande::where(function ($query) {
                $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                    ->orWhere(function ($query) {
                        $query->where('etat', Demande::ETAT['DOSSIER_EN_COURS'])
                            ->where('etat_dossier', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                            ->where('etat_complement', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                            ->whereNotNull('etat_dossier');
                    })
                    ->orWhere(function ($query) {
                        $query->where('etat', Demande::ETAT['PRISE_DE_DECISION'])
                            ->where('etat_contrat', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE']);
                    });
            })->orderBy('id', 'desc');
            $type = null;
            $typeIds = [];
            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
                $demandes = $demandes->whereIn('demande_type_id', $typeIds);
            }

            $demandes = $demandes->when(
                $request['annee_universitaire_id'],
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request['annee_universitaire_id']);
                }
            );
            if ($request['status']) {
                $demandes = $demandes->where('etat', '=', (int)$request['status']);
            }

        }
        elseif ($request['demande_type_code'] === 'guichet') {
            $type = null;

            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }

            $demandes = Demande::orderBy('id', 'desc');
            $typeIds = [];
            if ($type) {
                $typeIds[] = $type->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
                $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');
            }

            if ($request['status']) {
                $pieces = explode("---", $request['status']);
                $group = $pieces[0];
                $key = $pieces[1];
                //status status_dossier status_complement status_contrat status_bourse status_bourse_insertion status_pret status_bourse_stage status_aide_sociale
                if($group === 'status'){
                    $demandes = $demandes->where('etat', '=', $key);
                }
                if($group === 'status_dossier'){
                    $demandes = $demandes->where('etat_dossier', '=', $key);
                }
                if($group === 'status_complement'){
                    $demandes = $demandes->where('etat_complement', '=', $key);
                }
                if($group === 'status_contrat'){
                    $demandes = $demandes->where('etat_contrat', '=', $key);
                }
                if($group === 'status_bourse'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_bourse',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_bourse', '=', $key);
                    }
                }
                if($group === 'status_bourse_insertion'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_bourse_insertion',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_bourse_insertion', '=', $key);
                    }
                }
                if($group === 'status_pret'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_pret',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_pret', '=', $key);
                    }
                }
                if($group === 'status_bourse_stage'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_bourse_stage',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_bourse_stage', '=', $key);
                    }
                }
                if($group === 'status_aide_sociale'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_aide_sociale',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_aide_sociale', '=', $key);
                    }
                }
            }
            if ($request['annee_id']) {
                $demandes = $demandes->where('annee_universitaire_id', $request['annee_id']);
            }
            if ($request['cin'] || $request['nom']) {
                $demandes = $demandes->where(function (Builder $query) use ($request) {
                    if ($request['cin']) {
                        $query->orWhereHas( 'user' , function (Builder $query) use ($request) {
                            $query->where('cin', 'like', '%' . $request['cin'] . '%');
                        })
                            ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                                $query->where('num_passport', 'like', '%' . $request['cin'] . '%');
                            });
                    }
                    if ($request['nom']) {
                        $query->orWhereHas( 'user' , function (Builder $query) use ($request) {
                            $query->where('name', 'like', '%' . $request['nom'] . '%');
                        })
                            ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                                $query->where('name_ar', 'like', '%' . $request['nom'] . '%');
                            })
                            ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                                $query->where('firstName', 'like', '%' . $request['nom'] . '%');
                            });
                    }
                });
            }
        }
        elseif ($request['demande_type_code'] === 'preparation_paiement') {
            $type = null;
            $typeIds = [];
            if ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret') {
                $type = DemandeType::where('code', 'bourses_universitaires')->first();
                if ($request['type_preparation'] === 'bourse') {
                    $types = DemandeType::whereIn('code', ['int_nv','int_ce', 'int_rnv', 'int_mstr','int_doc'])->pluck('id')->toArray();
                    foreach ( $types as $typeId) {
                        $typeIds[] = $typeId;
                    }
                }
            }
            if ($request['type_preparation'] === 'aide_sociale') {
                $type = DemandeType::where('code', 'aide_sociale')->first();
            }
            if ($request['type_preparation'] === 'stage') {
                $type = DemandeType::where('code', 'bourses_de_stage')->first();
            }
            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');

            if ($request['annee_universitaire_id']) {
                $demandes = $demandes->where('annee_universitaire_id', $request['annee_universitaire_id']);
            }

            if (isset($request['attestation_status']) && $request['attestation_status'] && $request['attestation_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['attestation_status']) && $request['attestation_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['attestation_pret_status']) && $request['attestation_pret_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['attestation_pret_status']) && $request['attestation_pret_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['retrait_status']) && $request['retrait_status'] === "true") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersRetrait);
            }
            if (isset($request['retrait_status']) && $request['retrait_status'] === "false") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersRetrait);
            }

            if (isset($request['type_preparation']) && ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret')) {
                $demandes = $demandes->whereNot(function ($query) {
                    $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                        ->orWhere(function ($query) {
                            $query->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                                ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                                ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                                ->whereNotNull('etat_dossier');
                        })->orWhere(function ($query) {
                            $query->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                                ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                                ->whereNotNull('etat_contrat');
                        });
                });
            }

            $demandes = $demandes->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION']);

            if (isset($request['type_preparation']) && $request['type_preparation']) {
                if ($request['type_preparation'] === 'bourse') {
                    $demandes = $demandes->where('etat_bourse', '=', Demande::ETAT_BOURSE['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'insertion') {
                    $demandes = $demandes->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'pret') {
                    $demandes = $demandes->where('etat_pret', '=', Demande::ETAT_PRET['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'aide_sociale') {
                    $demandes = $demandes->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'stage') {
                    $demandes = $demandes->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['ELIGIBLE']);
                }

            }

            if (isset($request['revenu_net_min']) && $request['revenu_net_min'] != '') {
                $demandes = $demandes->where('revenu_net', '>=', $request['revenu_net_min']);
            }
            if (isset($request['revenu_net_max']) && $request['revenu_net_max'] != '') {
                $demandes = $demandes->where('revenu_net', '<=', $request['revenu_net_max']);
            }

//            if ($request['status']) {
//                $demandes = $demandes->where('etat', '=', $request['status']);
//            }

            $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType');
        }
        elseif ($request['demande_type_code'] === 'suivi_preparation_paiement') {
            $type = null;
            $typeIds = [];
            if ($request['type_preparation'] && ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret')) {
                $type = DemandeType::where('code', 'bourses_universitaires')->first();
                if ($request['type_preparation'] === 'bourse') {
                    $types = DemandeType::whereIn('code', ['int_nv','int_ce', 'int_rnv', 'int_mstr','int_doc'])->pluck('id')->toArray();
                    foreach ( $types as $typeId) {
                        $typeIds[] = $typeId;
                    }
                }
            }
            if ($request['type_preparation'] && $request['type_preparation'] === 'aide_sociale') {
                $type = DemandeType::where('code', 'aide_sociale')->first();
            }
            if ($request['type_preparation'] && $request['type_preparation'] === 'stage') {
                $type = DemandeType::where('code', 'bourses_de_stage')->first();
            }
            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');

            if ($request['attestation_status'] && $request['attestation_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if ($request['attestation_status'] &&  $request['attestation_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if ($request['attestation_pret_status'] && $request['attestation_pret_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if ($request['attestation_pret_status'] &&  $request['attestation_pret_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if ($request['retrait_status'] && $request['retrait_status'] === "true") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersRetrait);
            }
            if ($request['retrait_status'] && $request['retrait_status'] === "false") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersRetrait);
            }


            if ($request['type_preparation'] && ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret')) {
                $demandes = $demandes->whereNot(function ($query) {
                    $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                        ->orWhere(function ($query) {
                            $query->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                                ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                                ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                                ->whereNotNull('etat_dossier');
                        })->orWhere(function ($query) {
                            $query->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                                ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                                ->whereNotNull('etat_contrat');
                        });
                });
            }

            $demandes = $demandes->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION']);

            if ($request['type_preparation']) {
                if ($request['type_preparation'] === 'bourse') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_bourse', '=', Demande::ETAT_BOURSE['SUIVI_PAIEMENT'])
                            ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_FAVORABLE'])
                            ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_AMBIGU']);
                    })
                        ->where('preparation_bourse_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'insertion') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'])
                            ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_FAVORABLE'])
                            ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_AMBIGU']);
                    })
                        ->where('preparation_insertion_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'pret') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_pret', '=', Demande::ETAT_PRET['SUIVI_PAIEMENT'])
                            ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_FAVORABLE'])
                            ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_AMBIGU']);
                    })
                        ->where('preparation_pret_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'aide_sociale') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['SUIVI_PAIEMENT'])
                            ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_FAVORABLE'])
                            ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_AMBIGU']);
                    })
                        ->where('preparation_aide_sociale_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'stage') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['SUIVI_PAIEMENT'])
                            ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_FAVORABLE'])
                            ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_AMBIGU']);
                    })
                        ->where('preparation_stage_id', $request['preparation_id']);
                }

            }

            if ($request['revenu_net_min'] && $request['revenu_net_min'] != '') {
                $demandes = $demandes->where('revenu_net', '>=', $request['revenu_net_min']);
            }
            if ($request['revenu_net_max'] && $request['revenu_net_max'] != '') {
                $demandes = $demandes->where('revenu_net', '<=', $request['revenu_net_max']);
            }

            $demandes = $demandes->when(
                $request['annee_universitaire_id'],
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request['annee_universitaire_id']);
                }
            );

            $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType');
        }
        else {
            $type = DemandeType::where('code', $request['demande_type_code'])->first();

            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }

            $typeIds = [];
            if ($request['demande_type_code'] === 'bourses_universitaires' && !$request['demande_type_id']) {
                $types = DemandeType::whereIn('code', ['int_nv','int_ce', 'int_rnv', 'int_mstr','int_doc'])->pluck('id')->toArray();
                foreach ( $types as $typeId) {
                    $typeIds[] = $typeId;
                }
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
//            return response()->json($type);
            $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');

            // test refus
            if ($request['refus'] === 'refus') {
                $demandes = $demandes->where('etat', '=', Demande::ETAT['REFUS_PAIEMENT'])->orWhere('etat', '=', Demande::ETAT['DOSSIER_REFUS']);
            }
            else {
                $demandes = $demandes
                    ->whereNot(function ($query) {
                        $query
                            ->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                            ->orWhere(function ($query) {
                                $query
                                    ->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                                    ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                                    ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                                    ->whereNotNull('etat_dossier');
                            })
                            ->orWhere(function ($query) {
                                $query
                                    ->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                                    ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                                    ->whereNotNull('etat_contrat');
                            });
                    })
                    ->where('etat', '<', Demande::ETAT['PREPARATION_PAIEMENT']);
            }

            // test is_pret
            if ($request['pret'] === 'pret') {
                $demandes = $demandes->where('is_pret', true);
            }
            else {
                $demandes = $demandes->where('is_pret' ,false);
            }
            $demandes = $demandes->when(
                $request['annee_universitaire_id'],
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request['annee_universitaire_id']);
                }
            );
            if ($request['status']) {
                $demandes = $demandes->where('etat', '=', $request['status']);
            }
        }

        /** Adding Filter query */
        if (isset($request['q']) && $request['q']) {
            $demandes = $demandes->where(function (Builder $query) use ($request) {

                $query->orWhere('code','like', '%' . $request['q'] . '%')
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('name', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('name_ar', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('firstName', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('email', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('cin', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('num_passport', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('cin', 'like', '%' . $request['q'] . '%');
                    });
            });
        }
        /** Adding Filter by dates */
        if (isset($request['dates']) &&  count($request['dates'])) {
            $start = Carbon::createFromFormat('d/m/Y', $request['dates'][0]) ;
            $end = Carbon::createFromFormat('d/m/Y', $request['dates'][1]);
            $demandes = $demandes->whereDate('created_at', '>=', $start)->whereDate('created_at', '<=', $end);
        }

        $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType', 'demandeAnneeEtudes')->get();

        return $demandes->map( fn (Demande $demande) => [
                'nom' => $demande->user?->name ?? '',
                'nom_ar' => $demande->user?->name_ar ?? '',
                'annee_universitaire' => $demande->anneeUniversitaire->title,
                'demandeType' => $demande->demandeType->title_fr,
                'classification' => $demande->classification?->title_fr ?? '',
                'classification_final' => $demande->classificationFinal?->title_fr ?? '',
                'profession' => $demande->professionFinal?->name_fr ?? '',
                'code' => $demande->code,
                'code_catb' => $demande->code_catb,
                'code_decision' => $demande->code_decision,
                'etat' => Demande::ETAT_STRING[$demande->etat],
                'etat_dossier' => $demande->etat_dossier,
                'etat_complement' => $demande->etat_complement,
                'lot' => $demande->lot,
            ]);
    }


}
