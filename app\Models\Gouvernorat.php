<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Gouvernorat extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'nbr',
        'code',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function delegations() : HasMany
    {
        return $this->hasMany(Delegation::class);
    }


    public function distances() : HasMany
    {
        return $this->hasMany(Distance::class, 'code_gouv1', 'code');
    }

    public function universites() : HasMany
    {
        return $this->hasMany(Universite::class,'code_gouv','code');
    }

    public function lycees() : HasMany
    {
        return $this->hasMany(Lycee::class,'code_gouv','code');
    }

    public function users() : HasManyThrough
    {
        return $this->hasManyThrough(Admin::class, Delegation::class);
    }

}
