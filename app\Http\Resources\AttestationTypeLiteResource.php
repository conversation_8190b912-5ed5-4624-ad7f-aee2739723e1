<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AttestationTypeLiteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'code'=> $this->code,
            'title'=> $this->title,
            'title_fr'=> $this->title_fr,
            'title_ar'=> $this->title_ar,
            'filigrane'=> $this->filigrane,
            'langues'=> $this->langues,
            'active'=> $this->active,
            'non_boursier'=> $this->non_boursier,
            'non_pret'=> $this->non_pret,
            'parent_id'=> $this->parent_id,
        ];
    }
}
