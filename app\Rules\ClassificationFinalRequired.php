<?php

namespace App\Rules;

use App\Models\Classification;
use App\Models\Etablissement;
use Illuminate\Contracts\Validation\InvokableRule;

class ClassificationFinalRequired implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail): void
    {
        if (Classification::find($value)?->fils()->count() > 0) {
            $fail('validation.FinalClassificationRequired')->translate();
        }
    }
}
