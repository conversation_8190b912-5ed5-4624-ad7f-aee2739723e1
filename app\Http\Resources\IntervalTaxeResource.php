<?php

namespace App\Http\Resources;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use JsonSerializable;
use Illuminate\Http\Resources\Json\JsonResource;

class IntervalTaxeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'min' => $this->min,
            'max' => $this->max,
            'taxe' => $this->taxe,
        ];
    }
}
