<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;
use Illuminate\Support\Str;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Permission
        Permission::create(['name' => 'read admin_home']);
        Permission::create(['name' => 'create admin_home']);
        Permission::create(['name' => 'update admin_home']);
        Permission::create(['name' => 'delete admin_home']);

        Permission::create(['name' => 'read admin_bureau_ordre']);
        Permission::create(['name' => 'create admin_bureau_ordre']);
        Permission::create(['name' => 'update admin_bureau_ordre']);
        Permission::create(['name' => 'delete admin_bureau_ordre']);

        Permission::create(['name' => 'read admin_guichet']);
        Permission::create(['name' => 'create admin_guichet']);
        Permission::create(['name' => 'update admin_guichet']);
        Permission::create(['name' => 'delete admin_guichet']);

        Permission::create(['name' => 'read admin_historique_etudiant']);
        Permission::create(['name' => 'create admin_historique_etudiant']);
        Permission::create(['name' => 'update admin_historique_etudiant']);
        Permission::create(['name' => 'delete admin_historique_etudiant']);


        // Admin
        Permission::create(['name' => 'read admin_bourse_universitaire']);
        Permission::create(['name' => 'create admin_bourse_universitaire']);
        Permission::create(['name' => 'update admin_bourse_universitaire']);
        Permission::create(['name' => 'delete admin_bourse_universitaire']);

        Permission::create(['name' => 'read admin_pret_universitaire']);
        Permission::create(['name' => 'create admin_pret_universitaire']);
        Permission::create(['name' => 'update admin_pret_universitaire']);
        Permission::create(['name' => 'delete admin_pret_universitaire']);

        Permission::create(['name' => 'read admin_bourse_insertion']);
        Permission::create(['name' => 'create admin_bourse_insertion']);
        Permission::create(['name' => 'update admin_bourse_insertion']);
        Permission::create(['name' => 'delete admin_bourse_insertion']);

        Permission::create(['name' => 'read admin_bourse_stage']);
        Permission::create(['name' => 'create admin_bourse_stage']);
        Permission::create(['name' => 'update admin_bourse_stage']);
        Permission::create(['name' => 'delete admin_bourse_stage']);

        Permission::create(['name' => 'read admin_aide_sociale']);
        Permission::create(['name' => 'create admin_aide_sociale']);
        Permission::create(['name' => 'update admin_aide_sociale']);
        Permission::create(['name' => 'delete admin_aide_sociale']);

        Permission::create(['name' => 'read admin_bourse_alternance']);
        Permission::create(['name' => 'create admin_bourse_alternance']);
        Permission::create(['name' => 'update admin_bourse_alternance']);
        Permission::create(['name' => 'delete admin_bourse_alternance']);


        // Agent

        Permission::create(['name' => 'read agent_bourse_universitaire']);
        Permission::create(['name' => 'create agent_bourse_universitaire']);
        Permission::create(['name' => 'update agent_bourse_universitaire']);
        Permission::create(['name' => 'delete agent_bourse_universitaire']);

        Permission::create(['name' => 'read agent_pret_universitaire']);
        Permission::create(['name' => 'create agent_pret_universitaire']);
        Permission::create(['name' => 'update agent_pret_universitaire']);
        Permission::create(['name' => 'delete agent_pret_universitaire']);

        Permission::create(['name' => 'read agent_bourse_insertion']);
        Permission::create(['name' => 'create agent_bourse_insertion']);
        Permission::create(['name' => 'update agent_bourse_insertion']);
        Permission::create(['name' => 'delete agent_bourse_insertion']);

        Permission::create(['name' => 'read agent_bourse_stage']);
        Permission::create(['name' => 'create agent_bourse_stage']);
        Permission::create(['name' => 'update agent_bourse_stage']);
        Permission::create(['name' => 'delete agent_bourse_stage']);

        Permission::create(['name' => 'read agent_aide_sociale']);
        Permission::create(['name' => 'create agent_aide_sociale']);
        Permission::create(['name' => 'update agent_aide_sociale']);
        Permission::create(['name' => 'delete agent_aide_sociale']);

        Permission::create(['name' => 'read agent_bourse_alternance']);
        Permission::create(['name' => 'create agent_bourse_alternance']);
        Permission::create(['name' => 'update agent_bourse_alternance']);
        Permission::create(['name' => 'delete agent_bourse_alternance']);


/*
        Permission::create(['name' => 'read admin_nb', 'demande_type_id' => 2]);
        Permission::create(['name' => 'read admin_ce', 'demande_type_id' => 3]);
        Permission::create(['name' => 'read admin_rnw', 'demande_type_id' => 4]);
        Permission::create(['name' => 'read admin_doc', 'demande_type_id' => 5]);
        Permission::create(['name' => 'read admin_mstr', 'demande_type_id' => 6]);
        Permission::create(['name' => 'read admin_nb_etd_rg', 'demande_type_id' => 9]);
        Permission::create(['name' => 'read admin_nb_prt_etrg', 'demande_type_id' => 10]);
        Permission::create(['name' => 'read admin_nb_prt_mes', 'demande_type_id' => 11]);
        Permission::create(['name' => 'read admin_nb_prt_meduc', 'demande_type_id' => 12]);
        Permission::create(['name' => 'read admin_ce_etd_rg', 'demande_type_id' => 13]);
        Permission::create(['name' => 'read admin_ce_prt_etrg', 'demande_type_id' => 14]);
        Permission::create(['name' => 'read admin_ce_prt_mes', 'demande_type_id' => 15]);
        Permission::create(['name' => 'read admin_ce_prt_meduc', 'demande_type_id' => 16]);
        Permission::create(['name' => 'read admin_rnv_etd_rg', 'demande_type_id' => 17]);
        Permission::create(['name' => 'read admin_rnv_prt_mes', 'demande_type_id' => 18]);
        Permission::create(['name' => 'read admin_rnv_prt_meduc', 'demande_type_id' => 19]);
        Permission::create(['name' => 'read admin_doc_etd_rg', 'demande_type_id' => 20]);
        Permission::create(['name' => 'read admin_doc_prt_etrg', 'demande_type_id' => 21]);
        Permission::create(['name' => 'read admin_mstr_nv', 'demande_type_id' => 22]);
        Permission::create(['name' => 'read admin_mstr_rnv', 'demande_type_id' => 23]);
        Permission::create(['name' => 'read admin_mstr_mrt', 'demande_type_id' => 24]);
        Permission::create(['name' => 'read admin_mstr_nv_etd_rg', 'demande_type_id' => 25]);
        Permission::create(['name' => 'read admin_mstr_nv_prt_etrg', 'demande_type_id' => 26]);
        Permission::create(['name' => 'read admin_mstr_nv_prt_mes', 'demande_type_id' => 27]);
        Permission::create(['name' => 'read admin_mstr_nv_prt_meduc', 'demande_type_id' => 28]);
        Permission::create(['name' => 'read admin_mstr_rnv_etd_rg', 'demande_type_id' => 29]);
        Permission::create(['name' => 'read admin_mstr_nv_prt_mes', 'demande_type_id' => 30]);
        Permission::create(['name' => 'read admin_mstr_nv_prt_meduc', 'demande_type_id' => 31]);
*/

        Permission::create(['name' => 'read admin_attestations']);
        Permission::create(['name' => 'create admin_attestations']);
        Permission::create(['name' => 'update admin_attestations']);
        Permission::create(['name' => 'delete admin_attestations']);

        Permission::create(['name' => 'read admin_attestations_type']);
        Permission::create(['name' => 'create admin_attestations_type']);
        Permission::create(['name' => 'update admin_attestations_type']);
        Permission::create(['name' => 'delete admin_attestations_type']);

        Permission::create(['name' => 'read admin_reclamations']);
        Permission::create(['name' => 'create admin_reclamations']);
        Permission::create(['name' => 'update admin_reclamations']);
        Permission::create(['name' => 'delete admin_reclamations']);

        Permission::create(['name' => 'read admin_historique']);
        Permission::create(['name' => 'create admin_historique']);
        Permission::create(['name' => 'update admin_historique']);
        Permission::create(['name' => 'delete admin_historique']);

        Permission::create(['name' => 'read admin_statistiques']);
        Permission::create(['name' => 'create admin_statistiques']);
        Permission::create(['name' => 'update admin_statistiques']);
        Permission::create(['name' => 'delete admin_statistiques']);

        Permission::create(['name' => 'read admin_types_demandes']);
        Permission::create(['name' => 'create admin_types_demandes']);
        Permission::create(['name' => 'update admin_types_demandes']);
        Permission::create(['name' => 'delete admin_types_demandes']);

        Permission::create(['name' => 'read admin_mes_rs_etudiants']);
        Permission::create(['name' => 'create admin_mes_rs_etudiants']);
        Permission::create(['name' => 'update admin_mes_rs_etudiants']);
        Permission::create(['name' => 'delete admin_mes_rs_etudiants']);

        Permission::create(['name' => 'read admin_classifications']);
        Permission::create(['name' => 'create admin_classifications']);
        Permission::create(['name' => 'update admin_classifications']);
        Permission::create(['name' => 'delete admin_classifications']);

        Permission::create(['name' => 'read admin_roles_permissions']);
        Permission::create(['name' => 'create admin_roles_permissions']);
        Permission::create(['name' => 'update admin_roles_permissions']);
        Permission::create(['name' => 'delete admin_roles_permissions']);

        Permission::create(['name' => 'read account']);
        Permission::create(['name' => 'create account']);
        Permission::create(['name' => 'update account']);
        Permission::create(['name' => 'delete account']);

        Permission::create(['name' => 'read admin_users']);
        Permission::create(['name' => 'create admin_users']);
        Permission::create(['name' => 'update admin_users']);
        Permission::create(['name' => 'delete admin_users']);

        Permission::create(['name' => 'read admin_retrait_inscription']);
        Permission::create(['name' => 'create admin_retrait_inscription']);
        Permission::create(['name' => 'update admin_retrait_inscription']);
        Permission::create(['name' => 'delete admin_retrait_inscription']);

        Permission::create(['name' => 'read admin_preparation_paiement']);
        Permission::create(['name' => 'create admin_preparation_paiement']);
        Permission::create(['name' => 'update admin_preparation_paiement']);
        Permission::create(['name' => 'delete admin_preparation_paiement']);

        Permission::create(['name' => 'read admin_suivi_paiement']);
        Permission::create(['name' => 'create admin_suivi_paiement']);
        Permission::create(['name' => 'update admin_suivi_paiement']);
        Permission::create(['name' => 'delete admin_suivi_paiement']);

        Permission::create(['name' => 'read admin_generation_des_fichiers']);
        Permission::create(['name' => 'create admin_generation_des_fichiers']);
        Permission::create(['name' => 'update admin_generation_des_fichiers']);
        Permission::create(['name' => 'delete admin_generation_des_fichiers']);

        Permission::create(['name' => 'read admin_etudiants']);
        Permission::create(['name' => 'create admin_etudiants']);
        Permission::create(['name' => 'update admin_etudiants']);
        Permission::create(['name' => 'delete admin_etudiants']);

        Permission::create(['name' => 'read admin_absences']);
        Permission::create(['name' => 'create admin_absences']);
        Permission::create(['name' => 'update admin_absences']);
        Permission::create(['name' => 'delete admin_absences']);

        Permission::create(['name' => 'read admin_demandes_recouvrements']);
        Permission::create(['name' => 'create admin_demandes_recouvrements']);
        Permission::create(['name' => 'update admin_demandes_recouvrements']);
        Permission::create(['name' => 'delete admin_demandes_recouvrements']);

        Permission::create(['name' => 'read admin_rectificatifs']);
        Permission::create(['name' => 'create admin_rectificatifs']);
        Permission::create(['name' => 'update admin_rectificatifs']);
        Permission::create(['name' => 'delete admin_rectificatifs']);

        Permission::create(['name' => 'read admin_fiches_organismes']);
        Permission::create(['name' => 'create admin_fiches_organismes']);
        Permission::create(['name' => 'update admin_fiches_organismes']);
        Permission::create(['name' => 'delete admin_fiches_organismes']);

        Permission::create(['name' => 'read admin_documents_predefinis']);
        Permission::create(['name' => 'create admin_documents_predefinis']);
        Permission::create(['name' => 'update admin_documents_predefinis']);
        Permission::create(['name' => 'delete admin_documents_predefinis']);

        Permission::create(['name' => 'read admin_champs_predefinis']);
        Permission::create(['name' => 'create admin_champs_predefinis']);
        Permission::create(['name' => 'update admin_champs_predefinis']);
        Permission::create(['name' => 'delete admin_champs_predefinis']);

        Permission::create(['name' => 'read admin_historiques']);
        Permission::create(['name' => 'create admin_historiques']);
        Permission::create(['name' => 'update admin_historiques']);
        Permission::create(['name' => 'delete admin_historiques']);

        Permission::create(['name' => 'read admin_configuration']);
        Permission::create(['name' => 'create admin_configuration']);
        Permission::create(['name' => 'update admin_configuration']);
        Permission::create(['name' => 'delete admin_configuration']);

        /**
         *  SECTIONS
        */

        Permission::create(['name' => 'read admin_section_bourse']);
        Permission::create(['name' => 'read admin_section_paiement']);
        Permission::create(['name' => 'read admin_section_autres']);
        Permission::create(['name' => 'read admin_section_administration']);


        //Admin Role
        $adminRole = Role::create(['name' => 'admin']);

        // Assign All Permissions To Admin
        $adminRole->givePermissionTo(Permission::where('guard_name', 'web')->where('name', 'not like', "agent_%")->get());

        // Admin User
        $adminUser = Admin::create( [
                'username' => 'admin',
                'name' => 'Admin',
                'firstName' => 'Admin',
                'status' => true,
                'role' => 'admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'remember_token' => Str::random(10),
            ]
        );
        $adminUser->assignRole('admin') ;


    }
}
