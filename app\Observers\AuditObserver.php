<?php

namespace App\Observers;

use App\Models\Audit;
use Illuminate\Support\Facades\Log;

class AuditObserver
{
    /**
     * Handle the Audit "created" event.
     *
     * @param Audit $audit
     * @return void
     */
    public function created(Audit $audit)
    {
//        Log::debug('created_audit++');
//        Log::debug($audit);
//        Log::debug('created_audit--');

        $auditable = $audit->auditable;
//        Log::debug($auditable);

        if ($auditable) {
            $audit->auditable_name = $auditable->name ?? $auditable->title ?? $auditable->label ?? $auditable->code ?? $auditable->num_decision ?? 'none';
        }
        $audit->save();

    }

    /**
     * Handle the Audit "updated" event.
     *
     * @param Audit $audit
     * @return void
     */
    public function updated(Audit $audit)
    {
        //
    }

    /**
     * Handle the Audit "deleted" event.
     *
     * @param Audit $audit
     * @return void
     */
    public function deleted(Audit $audit)
    {
        //
    }

    /**
     * Handle the Audit "restored" event.
     *
     * @param Audit $audit
     * @return void
     */
    public function restored(Audit $audit)
    {
        //
    }

    /**
     * Handle the Audit "force deleted" event.
     *
     * @param Audit $audit
     * @return void
     */
    public function forceDeleted(Audit $audit)
    {
        //
    }
}
