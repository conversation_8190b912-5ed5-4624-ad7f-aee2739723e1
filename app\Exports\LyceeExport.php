<?php

namespace App\Exports;

use App\Models\Lycee;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class LyceeExport implements FromCollection,WithHeadings
{

    public function headings():array{
        return[
            'id',
            'code',
            'code_gouv',
            'name',
            'name_fr',
            'name_ar',
            'created_at',
            'created_at',
            'updated_at'
        ];
    }

    public function collection()
    {
        return Cache::remember('LyceeExport', 900, function () {
            return Lycee::all();
        });
        //return Lycee::all();
    }

}
