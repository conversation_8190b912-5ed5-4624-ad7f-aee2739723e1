<?php

namespace App\Jobs;

use App\Exports\PreparationExport;
use App\Exports\PreparationViewExport;
use App\Models\Admin;
use App\Models\Decision;
use App\Models\Demande;
use App\Models\ExportedFile;
use App\Models\Preparation;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\FastExcel;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Str;
use XLSXWriter;

class ProcessRestorePreparation implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $preparation;

    public function __construct(?Admin $user, ?Preparation $preparation)
    {
        $this->user = $user;
        $this->preparation = $preparation;
    }

    public function handle()
    {

        try {


            $preparation = Preparation::find($this->preparation->id);

            if ($preparation->type) {
                $demandes = Demande::where('preparation_' . $preparation->type . '_id', $preparation->id);
                if ($preparation->type === 'bourse') {
                    $demandes = $demandes->whereIn('etat_bourse', [Demande::ETAT_BOURSE['SUIVI_PAIEMENT'],Demande::ETAT_BOURSE['DECISION_FAVORABLE'],Demande::ETAT_BOURSE['DECISION_NON_FAVORABLE'],Demande::ETAT_BOURSE['DECISION_AMBIGU']]);
                }
                if ($preparation->type === 'insertion') {
                    $demandes = $demandes->whereIn('etat_bourse_insertion', [Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'],Demande::ETAT_BOURSE_INSERTION['DECISION_FAVORABLE'],Demande::ETAT_BOURSE_INSERTION['DECISION_NON_FAVORABLE'],Demande::ETAT_BOURSE_INSERTION['DECISION_AMBIGU']]);
                }
                if ($preparation->type === 'pret') {
                    $demandes = $demandes->whereIn('etat_pret', [Demande::ETAT_PRET['SUIVI_PAIEMENT'],Demande::ETAT_PRET['DECISION_FAVORABLE'],Demande::ETAT_PRET['DECISION_NON_FAVORABLE'],Demande::ETAT_PRET['DECISION_AMBIGU']]);
                }
                if ($preparation->type === 'aide_sociale') {
                    $demandes = $demandes->whereIn('etat_aide_sociale',  [Demande::ETAT_AIDE_SOCIALE['SUIVI_PAIEMENT'],Demande::ETAT_AIDE_SOCIALE['DECISION_FAVORABLE'],Demande::ETAT_AIDE_SOCIALE['DECISION_NON_FAVORABLE'],Demande::ETAT_AIDE_SOCIALE['DECISION_AMBIGU']]);
                }
                if ($preparation->type === 'stage') {
                    $demandes = $demandes->whereIn('etat_bourse_stage', [Demande::ETAT_BOURSE_STAGE['SUIVI_PAIEMENT'],Demande::ETAT_BOURSE_STAGE['DECISION_FAVORABLE'],Demande::ETAT_BOURSE_STAGE['DECISION_NON_FAVORABLE'],Demande::ETAT_BOURSE_STAGE['DECISION_AMBIGU']]);
                }
                $demandes = $demandes->get();

                if (!$demandes->isEmpty()) {

                    if ($preparation->type === 'bourse') {
                        foreach ($demandes as $demande) {
                            $demande->preparation_bourse_id = null;
                            $demande->etat_bourse = Demande::ETAT_BOURSE['ELIGIBLE'];
                            $demande->natdec_bourse = null;
                            $demande->save();
                        }
                    }
                    if ($preparation->type === 'insertion') {
                        foreach ($demandes as $demande) {
                            $demande->preparation_insertion_id = null;
                            $demande->etat_bourse_insertion = Demande::ETAT_BOURSE_INSERTION['ELIGIBLE'];
                            $demande->natdec_insertion = null;
                            $demande->save();
                        }
                    }
                    if ($preparation->type === 'pret') {
                        foreach ($demandes as $demande) {
                            $demande->preparation_pret_id = null;
                            $demande->etat_pret = Demande::ETAT_PRET['ELIGIBLE'];
                            $demande->natdec_pret = null;
                            $demande->save();
                        }
                    }
                    if ($preparation->type === 'aide_sociale') {
                        foreach ($demandes as $demande) {
                            $demande->preparation_aide_sociale_id = null;
                            $demande->etat_aide_sociale = Demande::ETAT_AIDE_SOCIALE['ELIGIBLE'];
                            $demande->natdec_aide_sociale = null;
                            $demande->save();
                        }
                    }
                    if ($preparation->type === 'stage') {
                        foreach ($demandes as $demande) {
                            $demande->preparation_stage_id = null;
                            $demande->etat_bourse_stage = Demande::ETAT_BOURSE_STAGE['ELIGIBLE'];
                            $demande->natdec_stage = null;
                            $demande->save();
                        }
                    }
                }
                $preparation->delete();
            }

            dispatch(new NotifyUserOfCompletedRestorePreparation($this->user));

        } catch (\Exception $e) {
            dispatch(new NotifyUserOfCompletedSyncPreparation($this->user,true));
            Log::error($e->getFile() . $e->getLine() . $e->getMessage());
        }

    }
}
