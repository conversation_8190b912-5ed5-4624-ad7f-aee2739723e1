import { Server } from "socket.io";
import dotenv from "dotenv";

dotenv.config();

const PORT = process.env.PORT || 5000;
const ORIGIN = process.env.ORIGIN || "*" ;

const io = new Server({
  cors: {
    origin: ORIGIN,
  },
});



let onlineUsers = [];

const addNewUser = (username, socketId) => {
  !onlineUsers.some((user) => user.username === username) &&
    onlineUsers.push({ username, socketId });
};

const removeUser = (socketId) => {
  onlineUsers = onlineUsers.filter((user) => user.socketId !== socketId)
};

const getUser = (username) => {
  return onlineUsers.find((user) => user.username === username)
};

io.on("connection", (socket) => {
  console.log("a user connected");
  socket.on("newUser", (user) => {
    if (user?.username){
      addNewUser(user?.username, socket.id)
    }
  });

  socket.on("sendNotification", ({ senderName, receiverName }) => {
    const receiver = getUser(receiverName);
    io.to(receiver?.socketId).emit("getNotification", {
      senderName,
    });
  });

  socket.on("sendText", ({ senderName, receiverName, text }) => {
    const receiver = getUser(receiverName);
    io.to(receiver.socketId).emit("getText", {
      senderName,
      text,
    });
  });

  socket.on("disconnect", () => {
    removeUser(socket.id);
    console.log("a user disconnected");
  });
});

console.info(`Server is listening on port ${PORT}`);
console.info(`Server origin ${ORIGIN}`);

io.listen(PORT);
