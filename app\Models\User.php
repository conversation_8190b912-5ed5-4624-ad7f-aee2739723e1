<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use App\Traits\Override\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class User extends Model implements Auditable
{

    use Sortable,Notifiable;
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    protected $connection;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.secondConnection'))->getDatabaseName() . '.' . $this->getTable();

        $this->connection = config('database.secondConnection');
    }

    protected $with = [
        'country', 'gouvernorat','attestations','retraitInscriptions'
    ];
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'firstName',
        'name',
        'name_ar',
        'username',
        'email',
        'password',
        'phoneNumber',
        'address',

        'annee_bac',
        'num_bac',
        'cin',
        'matricule',
        'num_passport',

        'nationality_id',
        'country_id',
        'code_gouv',
        'delegation_id',
        'zipCode',

        'sex',
        'type',
        'pere',
        'mere',

        'role',
        'profile_photo',
        'phoneNumber2',
        'code_postal',
        'email_perso',
        'date_naissance',
        'address_naissance',
        'status',

        'student_from_mes_id',
        'international_student_id',
    ];


    public function historiqueDecisions() : HasMany
    {
        if ($this->type === 'tunisien') {
            return $this->setConnection(config('database.default'))->hasMany(Decision::class,'cin','cin');
        }
        return $this->setConnection(config('database.default'))->hasMany(Decision::class,'cin','matricule');
    }

    public function demandes(): HasMany
    {
        return $this->setConnection(config('database.default'))->hasMany(Demande::class, 'user_id');
    }

    public function attestations(): HasMany
    {
        return $this->setConnection(config('database.default'))->hasMany(Attestation::class, 'student_id');
    }


    public function reclamations(): HasMany
    {
        return $this->setConnection(config('database.default'))->hasMany(Reclamation::class, 'student_id');
    }


    public function retraitInscriptions(): HasMany
    {
        return $this->setConnection(config('database.default'))->hasMany(RetraitInscription::class, 'student_id');
    }

    public function etudiantAnneeUniversitaires(): HasMany
    {
        return $this->setConnection(config('database.secondConnection'))->hasMany(EtudiantAnneeUniversitaire::class, 'user_id', 'id');
    }

    public function anneeBac(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(AnneeBac::class, 'annee_bac', 'id');
    }


    public function country(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Country::class);
    }

    public function nationality(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Country::class, 'nationality_id', 'id');
    }

    public function delegation(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Delegation::class);
    }

    public function gouvernorat(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Gouvernorat::class, 'code_gouv', 'code');
    }

    public $appends = [
        'profile_image_url',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_naissance' => 'date',
        'status' => 'boolean',
    ];

    public function getProfileImageUrlAttribute()
    {
        if ($this->profile_photo) {
            return asset('/uploads/profile_images/' . $this->profile_photo);
        }

        return 'https://ui-avatars.com/api/?background=random&name=' . urlencode($this->name);

    }
}
