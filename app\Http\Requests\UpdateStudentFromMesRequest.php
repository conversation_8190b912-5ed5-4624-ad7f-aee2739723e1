<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStudentFromMesRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'NBAC' => 'required',
            'CIN' => 'required|string|digits:8',
            'NOM_A' => 'required',
            'NOM_L' => 'required',
//            'JJ' => 'required',
//            'MM' => 'required',
//            'AA' => 'required',
            'CD_LYC' => 'required',
            'CD_GOUV' => 'required',
            'SEX' => 'required',
//            'PROF' => 'required',
            'CODE_FILIERE' => 'required',
            'TOUR' => 'required',
            'annee_bac' => 'required',
        ];
    }
}
