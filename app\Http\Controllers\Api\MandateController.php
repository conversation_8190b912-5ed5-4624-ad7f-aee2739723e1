<?php

namespace App\Http\Controllers\Api;

use App\Exports\MandatesExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMandateRequest;
use App\Jobs\SendMandatesNotifications;
use App\Models\Mandate;
use App\Models\User;
use App\Notifications\MandateNotification;
use Illuminate\Http\Request;
use Notification;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use SplFileObject;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Writer\Csv;

class MandateController extends Controller
{
    public function index(Request $request)
    {
        $mandates =  Mandate::when(
            $request->has('cin'),
            function ($query) use ($request) {
                return $query->where('cin',  'like', '%' . $request->cin . '%');
            }
        )
            ->when(
                $request->has('nom'),
                function ($query) use ($request) {
                    return $query->where('nom',  'like', '%' . $request->nom . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', 'like', '%' . $request->annee_universitaire . '%');
                }
            )
            ->when(
                $request->has('annee_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', 'like', '%' . $request->annee_id . '%');
                }
            )
            ->when(
                $request->has('num_dec'),
                function ($query) use ($request) {
                    return $query->where('num_dec',  'like', '%' . $request->num_dec . '%');
                }
            )
            ->when(
                $request->has('num_emission'),
                function ($query) use ($request) {
                    return $query->where('num_emission',  'like', '%' . $request->num_emission . '%');
                }
            )
            ->when(
                $request->has('date_payement'),
                function ($query) use ($request) {
                    return $query->where('date_payement',  'like', '%' . $request->date_payement . '%');
                }
            )
            ->when(
                $request->has('type'),
                function ($query) use ($request) {
                    return $query->where('type',  'like', '%' . $request->type . '%');
                }
            )
            ->when(
                $request->state !== null,
                function ($query) use ($request) {
                    return $query->where('state', Mandate::ETAT[$request->state]);
                }
            )
            ->orderBy('id', 'desc')
            ->with('etablissement')
            ->with('annee_universitaire')
            ->select([
                'id', 'cin', 'nom', 'fac', 'univ', 'annee', 'num_dec', 'nbre_mois', 'net_a_payer',
                'num_emission', 'date_payement', 'date_fin_validite', 'type', 'annee_universitaire_id', 'state'
            ])
            ->paginate(
                $request->input('perPage') ?? config('constants.pagination'),
            );

        return response()->json($mandates, 200);
    }

    public function uploadMandateFile(StoreMandateRequest $request)
    {
        $filePath = $request->file('file')->getRealPath();

        // Créer une instance de SplFileObject
        $file = new SplFileObject($filePath);

        // Définir le mode de lecture comme CSV
        $file->setFlags(SplFileObject::READ_CSV);

        // Récupérer la première ligne du fichier CSV
        $firstRow = $file->fgetcsv();

        $enteteType = [
            'cin', 'nom', 'fac', 'univ', 'annee', 'num_dec', 'nbre_mois', 'net_a_payer',
            'num_emission', 'date_payement', 'date_fin_validite'
        ];

        $firstRowLower = array_map(function ($column) {
            return str_replace(' ', '_', strtolower($column));
        }, $firstRow);

        // Récupérer la valeur de la colonne date_payement de la deuxieme ligne
        $secondRow = $file->fgetcsv();
        $datePayement = $secondRow[9] ?? null;
        if($datePayement!=$request->date_payment){
            return response()->json([
                'message' => "La date de paiement saisie dans la formulaire et la date de paiement dans le fichier ne sont pas identiques",
                'errors' => [
                    "file" => [
                        "La date de paiement saisie dans la formulaire et la date de paiement dans le fichier ne sont pas identiques"
                    ],
                ]
            ], 422);
        }

        // Vérifier si les deux tableaux sont égaux
        if ($firstRowLower === $enteteType) {

            // $csvFilePath = str_replace('\\', '/', $csvFilePath);


            $file = $request->file('file');

            $fileName = uniqid() . $file->getClientOriginalName();

            $file->move(storage_path('uploads'), $fileName);

            $filePath = str_replace('\\', '/', storage_path('uploads') . '/' . $fileName);

            DB::table('mandates')
            ->where('annee_universitaire_id', $request->input('annee_universitaire_id'))
            ->where('type', $request->input('type'))
            ->where('date_payement', $request->input('date_payment'))
            ->delete();

            DB::statement("
            LOAD DATA LOCAL INFILE '$filePath'
        INTO TABLE mandates
        FIELDS TERMINATED BY ','
        LINES TERMINATED BY '\\n'
        IGNORE 1 ROWS
        (@CIN, @NOM, @FAC, @UNIV, @ANNEE, @NUM_DEC, @NBRE_MOIS, @NET_A_PAYER,
        @NUM_EMISSION, @DATE_PAYEMENT, @DATE_FIN_VALIDITE)
        SET
            cin = @CIN,
            nom = @NOM,
            fac = @FAC,
            univ = @UNIV,
            annee = @ANNEE,
            num_dec = @NUM_DEC,
            nbre_mois = @NBRE_MOIS,
            net_a_payer = @NET_A_PAYER,
            num_emission = @NUM_EMISSION,
            date_payement = CASE WHEN LENGTH(@DATE_PAYEMENT) < 5 THEN NULL
                                 WHEN @DATE_PAYEMENT IS NOT NULL THEN LPAD(@DATE_PAYEMENT, 6, '0')
                                 ELSE NULL END,
            date_fin_validite = CASE WHEN LENGTH(@DATE_FIN_VALIDITE) < 7 THEN NULL
                                     WHEN @DATE_FIN_VALIDITE IS NOT NULL THEN LPAD(@DATE_FIN_VALIDITE, 8, '0')
                                     ELSE NULL END,
            type = ?,
            annee_universitaire_id = ?
    ", [$request->input('type'), $request->input('annee_universitaire_id')]);


            unlink($filePath);

            return response()->json("success", 200);
        } else {
            return response()->json([
                'message' => "Le fichier importé n'est pas valide",
                'errors' => [
                    "file" => [
                        "Le fichier importé n'est pas valide"
                    ],
                ]
            ], 422);
        }

        return response()->json('error', 500);
    }

    private function convertXlsxToCsv($xlsxFilePath)
    {
        try {
            $reader = new Xlsx();
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($xlsxFilePath);

            $csvFilePath = storage_path('uploads') . '/' . uniqid() . '_converted.csv';

            $writer = (new Csv($spreadsheet))
                ->setEnclosure('')
                ->setLineEnding("\n")
                ->setDelimiter(',');
            $writer->setSheetIndex(0);
            $writer->save($csvFilePath);

            return $csvFilePath;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getDistinctNDEC(Request $request)
    {
        $distinctNdecValues = Mandate::whereRaw('UPPER(type) = UPPER(?)', [$request->type])
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id',  $request->annee_universitaire_id);
                }
            )->distinct('num_dec')->pluck('num_dec');
        return response()->json($distinctNdecValues, 200);
    }

    public function getDistinctNDECByAnnee(Request $request)
    {
        $distinctNdecValues = Mandate::select(['annee_universitaire_id','date_payement','num_dec'])->groupBy('annee_universitaire_id')->groupBy('date_payement')->groupBy('num_dec')->get();
        return response()->json($distinctNdecValues, 200);
    }

    public function getAllNDEC(Request $request)
    {
        $ndecs = Mandate::select('num_dec', 'annee_universitaire_id')->distinct('num_dec', 'annee_universitaire_id')->get();
        return response()->json($ndecs, 200);
    }

    public function historiqueEtudiant(Request $request, $cin)
    {
        if($request->type){
            $type = $request->type == "bourse" ? "Bourse" : ($request->type == "pret"? "Prêt" : ucfirst($request->type));

            $historique =  Mandate::where('cin', $cin)->where('type', $type)->with('etablissement')->with('annee_universitaire')->get();

        } else {
            $historique =  Mandate::where('cin', $cin)->with('etablissement')->with('annee_universitaire')->get();

        }
        return response()->json($historique, 200);
    }

    public function mandatesToExport(Request $request)
    {
        $mandates = Mandate::when(
            $request->has('cin'),
            function ($query) use ($request) {
                return $query->where('cin', 'like', '%' . $request->cin . '%');
            }
        )
            ->when(
                $request->has('nom'),
                function ($query) use ($request) {
                    return $query->where('nom', 'like', '%' . $request->nom . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', 'like', '%' . $request->annee_universitaire . '%');
                }
            )
            ->when(
                $request->has('date_payement'),
                function ($query) use ($request) {
                    return $query->where('date_payement', 'like', '%' . $request->date_payement . '%');
                }
            )
            ->when(
                $request->has('type'),
                function ($query) use ($request) {
                    return $query->where('type', 'like', '%' . $request->type . '%');
                }
            )
            ->when(
                $request->state !== null,
                function ($query) use ($request) {
                    return $query->where('state', Mandate::ETAT[$request->state]);
                }
            )
            ->join('annee_universitaires', 'mandates.annee_universitaire_id', '=', 'annee_universitaires.id')
            ->select(['mandates.type', 'mandates.cin', 'mandates.nom', 'mandates.fac', 'mandates.univ', 'annee_universitaires.title as annee_universitaire',
             'mandates.num_dec', 'mandates.nbre_mois', 'mandates.net_a_payer', 'mandates.num_emission', 'mandates.date_payement', 'mandates.date_fin_validite',
             'state'])
            ->get();
            foreach ($mandates as $key => $mandate) {
                $mandate->state = Mandate::ETAT_STRING[$mandate->state];
            }
            return $mandates;
    }


    public function changeState($id, $state)
    {
        Mandate::where('id', $id)
            ->update(['state' => $state]);
        $mandate = Mandate::find($id);
        if ($state == 3 && $mandate->student) {
            Notification::send([$mandate->student], new MandateNotification());
        }
        return response()->json('success', 200);
    }

    public function notifierEtudiants(Request $request)
    {
        $request->validate([
            'type' => 'required|string',
            'date_payement' => 'required',
            'annee_universitaire_id' => 'required|numeric',
        ]);

        $madates = Mandate::where('type', $request->type)
            ->where('date_payement', $request->date_payement)
            ->where('annee_universitaire_id', $request->annee_universitaire_id)
            ->where('state', Mandate::ETAT['EN_COURS']);

        if ($madates->clone()->count() == 0) {
            return response()->json('Pas des mandats avec ces critères', 400);
        } else {
            $studentCIN = $madates->clone()->pluck('cin');

            $students = User::whereIn('cin', $studentCIN)->get();
            $madates->clone()->update(['state' => Mandate::ETAT['NOTIFIE']]);
            dispatch(new SendMandatesNotifications($students));

            // Notification::send($students, new MandateNotification());

            return response()->json('success', 200);
        }
    }

    public function getDistinctDatePayment(Request $request)
    {
        return Mandate::when($request->annee_universitaire_id, function ($query) use ($request) {
            return $query->where('annee_universitaire_id', $request->annee_universitaire_id);
        })->when($request->type, function ($query) use ($request) {
            return $query->where('type', $request->type);
        })->distinct('date_payement')->pluck('date_payement');
    }

    public function countNotifiedStudents(Request $request)
    {
        return Mandate::where('annee_universitaire_id', $request->input('annee_universitaire_id'))
                ->where('type', $request->input('type'))
                ->where('date_payement', $request->input('date_payment'))
                ->where('state', Mandate::ETAT['EN_COURS'])
                ->count();
    }
}
