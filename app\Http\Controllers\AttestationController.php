<?php

namespace App\Http\Controllers;

use App\Exports\AttestationExport;
use App\Exports\ReclamationExport;
use App\Http\Resources\AttestationLiteResource;
use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use App\Models\AttestationType;
use App\Models\DocumentsAttestationUpload;
use App\Models\Etablissement;
use App\Models\Office;
use App\Models\User;
use App\Notifications\AttestationPreteNotification;
use App\Notifications\AttestationRefuseNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Mpdf\Output\Destination;
use PhpOffice\PhpWord\TemplateProcessor;
use Symfony\Component\Process\Exception\ProcessFailedException;

//use Symfony\Component\Process\Process;
use Barryvdh\DomPDF\Facade\Pdf;
use Barryvdh\DomPDF\PDF as DomPDFPDF;
use Dompdf\Dompdf;

//use Dompdf\Dompdf;
use Error;
use Illuminate\Support\Facades\Storage;
use Mccarlosen\LaravelMpdf\Facades\LaravelMpdf;
use Mpdf\Mpdf;

//use Illuminate\Notifications\Notification;
use Notification;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Writer\PDF\DomPDF as PDFDomPDF;
use Symfony\Component\Process\Process;

//use Illuminate\Support\Facades\Process;
use Str;

class AttestationController extends Controller
{
    public function index(Request $request)
    {

        $etablissement_id = $request->query('etablissement_id', null);
        $university_id = $request->query('university_id', null);

        $attestations = Attestation::with('student')->withOut('student.attestations')
            ->with('attestationType')
            ->with('etablissement')
            ->with('office');
        if ($request->has('year') && $request->year && $request->year !== 'all') {
            $attestations = $attestations->where('year', $request->year);
        }
        if ($request->has('typeId') && $request->typeId) {
            $attestations = $attestations->where('attestation_types_id', $request->typeId);
        }
        if ($request->has('etat') && $request->etat) {
            $attestations = $attestations->where('status', $request->etat);
        }
        if ($request->has('startDate') && $request->startDate && $request->has('endDate') && $request->endDate) {
            $attestations = $attestations->whereBetween('created_at', [$request->startDate, $request->endDate]);
        } else{
            if ($request->has('startDate') && $request->startDate) {
                $attestations = $attestations->whereDate('created_at', '>=', $request->startDate);
            }
            if ($request->has('endDate') && $request->endDate) {
                $attestations = $attestations->whereDate('created_at', '<=', $request->endDate);
            }
        }

        if ($university_id){
            if ($etablissement_id){
                $attestations = $attestations->where('etablissement_id', $etablissement_id);
            } else {
                $etabIds = Etablissement::where('code_univ', $university_id)->pluck('id');
                $attestations = $attestations->whereIn('etablissement_id', $etabIds);
            }
        }
        else {
            if ($etablissement_id){
                $attestations = $attestations->where('etablissement_id', $etablissement_id);
            }
        }
        if ($request->q) {
            $userIds = User::where(function (Builder $query) use ($request) {
                $query
                    ->orWhere('email', 'like', '%' . $request->q . '%')
                    ->orWhere('name', 'like', '%' . $request->q . '%')
                    ->orWhere('name_ar', 'like', '%' . $request->q . '%')
                    ->orWhere('firstName', 'like', '%' . $request->q . '%')
                    ->orWhere('cin', 'like', '%' . $request->q . '%')
                    ->orWhere('num_bac', 'like', '%' . $request->q . '%')
                    ->orWhere('matricule', 'like', '%' . $request->q . '%')
                    ->orWhere('num_passport', 'like', '%' . $request->q . '%')
                ;
            })->pluck('id');
            $attestations = $attestations->whereIn('student_id', $userIds);
        }

        if ($request->orderByColumn && $request->orderByDirection) {
            $attestations = $attestations->orderBy($request->orderByColumn, $request->orderByDirection);
        } else {
            $attestations = $attestations->orderBy('id', 'desc');
        }

        return response()->json( AttestationLiteResource::collection( $attestations
            ->with('attestationType')
            ->with('etudiantAnneeUniversitaire')
            ->with('attestationDocuments')
            ->orderBy('attestations.created_at', 'desc')
            ->get()));

    }
    public function indexStatByType(Request $request)
    {

        $attestationsTypes = AttestationType::withcount(['attestations'=> function ($attestations) use ($request)
        {
            $etablissement_id = $request->query('etablissement_id', null);
            $university_id = $request->query('university_id', null);

            if ($request->has('year') && $request->year && $request->year !== 'all') {
                $attestations = $attestations->where('year', $request->year);
            }
            if ($request->has('typeId') && $request->typeId) {
                $attestations = $attestations->where('attestation_types_id', $request->typeId);
            }
            if ($request->has('etat') && $request->etat) {
                $attestations = $attestations->where('status', $request->etat);
            }
            if ($request->has('startDate') && $request->startDate && $request->has('endDate') && $request->endDate) {
                $attestations = $attestations->whereBetween('created_at', [$request->startDate, $request->endDate]);
            } else{
                if ($request->has('startDate') && $request->startDate) {
                    $attestations = $attestations->whereDate('created_at', '>=', $request->startDate);
                }
                if ($request->has('endDate') && $request->endDate) {
                    $attestations = $attestations->whereDate('created_at', '<=', $request->endDate);
                }
            }
            if ($university_id){
                if ($etablissement_id){
                    $attestations = $attestations->where('etablissement_id', $etablissement_id);
                } else {
                    $etabIds = Etablissement::where('code_univ', $university_id)->pluck('id');
                    $attestations = $attestations->whereIn('etablissement_id', $etabIds);
                }
            }
            else {
                if ($etablissement_id){
                    $attestations = $attestations->where('etablissement_id', $etablissement_id);
                }
            }
            if ($request->q) {
                $userIds = User::where(function (Builder $query) use ($request) {
                    $query
                        ->orWhere('email', 'like', '%' . $request->q . '%')
                        ->orWhere('name', 'like', '%' . $request->q . '%')
                        ->orWhere('name_ar', 'like', '%' . $request->q . '%')
                        ->orWhere('firstName', 'like', '%' . $request->q . '%')
                        ->orWhere('cin', 'like', '%' . $request->q . '%')
                        ->orWhere('num_bac', 'like', '%' . $request->q . '%')
                        ->orWhere('matricule', 'like', '%' . $request->q . '%')
                        ->orWhere('num_passport', 'like', '%' . $request->q . '%')
                    ;
                })->pluck('id');
                $attestations = $attestations->whereIn('student_id', $userIds);
            }
        }])->get();

        return $attestationsTypes;
    }


    public function store(Request $request)
    {
        try {
            //code...

            $request->validate([
                'attestation_types_id' => 'required|numeric',
                'student_id' => 'required|numeric',
                'detail' => 'string',

                'code_etablissement' => 'string', // todo
                'year' => 'required|string',
                'office' => 'required|numeric'
                //'status' => 'required|string',
            ]);

            Attestation::create([
                'attestation_types_id' => $request->attestation_types_id,
                'student_id' => $request->student_id,
                'detail' => isset($request['detail']) ? $request->detail : "",
                'status' => "en_cours",
                'etablissement_id' => $request->code_etablissement,
                'office_id' => $request->office
            ]);

            if ($request->hasFile('file')) {
                // file treatment
                //dd("request has file");
            }

            return response("created", 200);
        } catch (\Throwable $th) {
            //throw $th;
            return $th->getMessage();
        }
    }


    public function edit(Request $request)
    {
        try {
            //dd($request);
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'attestation_types_id' => 'required|numeric',
                'student_id' => 'required|numeric',
                'detail' => 'string',
                //'status' => 'string',

                'year' => 'required|string',
                'etablissement_id' => 'string' // todo
            ]);

            $attestation = Attestation::findOrFail($request->id);

            // ETABLISSEMENT
            if (isset($request['etablissement_id'])) {
                $etablissement_id = $request->etablissement_id; //Etablissement::where('code', $request->code_etablissement)->first();
            } else {
                $etablissement_id = $attestation->etablissement_id;
            }


            // STATUS
            if (isset($request['status'])) {
                $status = $request->status;
                if ($status == "refuser") {
                    # code...
                    $detail = isset($request['detail']) ? $request->detail : $attestation->detail;
                    $status = "refuse";

                } elseif ($status == "generer") {
                    $detail = $attestation->detail;
                } elseif ($status == "prete") {
                    $status = "prete";
                    $detail = $attestation->detail;
                } elseif ($status == "annulee") {
                    $status = "annulee";
                    $detail = $attestation->detail;

                } else { // gets run when status == en_cours_de_traitement / test for en cours / generer
                    $detail = $attestation->detail;
                    $status = "en_cours";
                }
            } else {
                # code...
//                Log::debug("2");
                $status = $attestation->status;
                $detail = $attestation->detail;
            }

            // if status not refuse, the amount and issue number are required
            /*if($status != "refuse" && $status !="annulee" ){
                $request->validate([
                    'montant' => 'required|numeric',
                    'num_emission' => 'required|numeric'
                ]);
            }*/

            // MONTANT
            if (isset($request['montant'])) {
//                Log::debug("found montant");
                $montant = $request->montant;
            } else {
//                Log::debug("montant not set");
                $montant = $attestation->montant;
            }

            // Raison non retrait
            if (isset($request['raison_non_retrait'])) {
                $raison_non_retrait = $request->raison_non_retrait;
            } else {
                $raison_non_retrait = $attestation->raison_non_retrait;
            }

            // date mandat
            if (isset($request['date_mandat'])) {
//                Log::debug("date mandat" . $request->date_mandat);
                $date_mandat = $request->date_mandat; //Etablissement::where('code', $request->code_etablissement)->first();
            } else {
                $date_mandat = $attestation->date_mandat;
            }

            $attestationType = AttestationType::findOrFail($attestation->attestation_types_id);


            Attestation::findOrFail($request->id)->update([
                'attestation_types_id' => $request->attestation_types_id,
                'student_id' => $request->student_id,
                'detail' => $detail, //isset($detail) ? $detail : null,
                'status' => $status,

                'year' => $request->year,
                'etablissement_id' => $etablissement_id,
                'response_date' => ($status == "prete") ? date("Y-m-d") : $attestation->response_date,
                'montant' => $montant,

                'num_emission' => isset($request['num_emission']) ? $request->num_emission : $attestation->num_emission,
                'date_mandat' => $date_mandat,
                'raison_non_retrait' => $raison_non_retrait,
            ]);

            $attest = Attestation::findOrFail($request->id);
            if ($attest->status == "prete") {
                Notification::send([$attest->student], new AttestationPreteNotification($attest, $attestationType));
            } elseif ($attest->status == "refuse") {
                Notification::send([$attest->student], new AttestationRefuseNotification($attest, $attestationType));
            }

            if ($request->hasFile('file')) {
                //dd("request has file");
            }

            return response("updated", 200);
        } catch (\Throwable $th) {
            //throw $th;
            return $th->getMessage(); //$th->getLine();
        }
    }

    public function destroy($id)
    {
        try {
            //code...
            Attestation::findOrFail($id)->delete();
            return response("deleted", 200);
        } catch (\Throwable $th) {
            //throw $th;
            return $th->getMessage();
        }
    }

    public function covert_doc_to_pdf($filepath)
    {
        //$doc = PrototypeLettre::find(1);
        //$filepath = public_path($doc->local_path);
        // Create a unique filename for the PDF
//        Log::debug("filepath");
//        Log::debug($filepath);
        $pdfFilename = pathinfo($filepath, PATHINFO_FILENAME) . '.pdf';
        $pdfPath = public_path('officeDoc/' . $pdfFilename);

//        Log::debug($pdfFilename);
//        Log::debug($pdfPath);


        // Run unoconv to convert DOC to PDF
        $tt = 'D:\Installs\LibreOffice\program\python.exe D:\unoconv-master\unoconv -f pdf ' . $filepath; // was -o

        // the only way it worked in local environment :
        //  D:\Installs\LibreOffice\program\python.exe unoconv -f pdf D:\Documents\Tac-Tic\Work\bpas\bpas_back\public\officeDoc\document_1701092638.docx
        //$tt = 'D:\Installs\LibreOffice\program\python.exe D:\unoconv-master\unoconv -f pdf D:\Documents\Tac-Tic\Work\bpas\bpas_back\public\officeDoc\document_1701104372.docx';
        $tt = str_replace('/', '\\', $tt);
//        Log::debug("TT : " . $tt);

        /*

        $process = new Process(array($tt));

        $process->run();
        if (!$process->isSuccessful()) {
            Log::debug("Error In process");
            throw new ProcessFailedException($process);
        }

        */
        try {
            //code...
            //$result = Process::run($tt);
            //$process = new Process(['D:\Installs\LibreOffice\program\python.exe', 'D:\unoconv-master\unoconv', '-f', 'pdf', 'D:\Documents\Tac-Tic\Work\bpas\bpas_back\public\officeDoc\document_1701432142.docx']);
            $process = new Process(['D:\Installs\LibreOffice\program\python.exe', 'D:\unoconv-master\unoconv', '-f', 'pdf', 'D:\Documents\Tac-Tic\Work\bpas\bpas_back\public\officeDoc\document_1701432214.docx']);
            $process->run();
            if ($process->isSuccessful()) {
//                Log::debug("*** Successful Process");
            } else {
//                Log::debug("***Error In process  ");
            }
//            Log::debug("done process");
            //Log::debug( $result->output());
        } catch (\Throwable $th) {
            //throw $th;
//            Log::debug('error');
//            Log::debug($th->getMessage());
        }


        //dd("works");
        //dd($tt);
        // Move the generated PDF file to the desired location
        //File::move(public_path('templateletter/' . time()/*$doc->id*/ . '.pdf'), $pdfPath);


        // Return the path to the newly created PDF file
        $resp = [
            "path" => $pdfPath,
            "name" => $pdfFilename
        ];
//        Log::debug("RESP:");
//        Log::debug($resp);

        return $resp; // response()->download($pdfPath, $pdfFilename);

    }

    public function convertWordToPDF($filepath)
    {
        try {
            /*
            // Set the PDF Engine Renderer Path
        $domPdfPath = base_path('vendor/dompdf/dompdf');
        \PhpOffice\PhpWord\Settings::setPdfRendererPath($domPdfPath);
        \PhpOffice\PhpWord\Settings::setPdfRendererName('DomPDF');

        //Load word file
        $Content = \PhpOffice\PhpWord\IOFactory::load($filepath);

        //Save it into PDF
        $PDFWriter = \PhpOffice\PhpWord\IOFactory::createWriter($Content,'PDF');
        $PDFWriter->save(public_path('officeDoc/new-result.pdf'));
        Log::debug( 'File has been successfully converted');

        */

            // Load .docx file content
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($filepath);

            // Save the document as HTML
            $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'HTML');
            $writer->save(public_path('officeDoc/test1.html'));

            // Convert HTML to PDF using Dompdf
            $dompdf = new DomPDF();
            $dompdf->loadHtml(file_get_contents(public_path('officeDoc/test1.html')));

            $dompdf->setPaper('A4', 'letter');
            $dompdf->render();

            // Save the PDF
            $output = $dompdf->output();
            file_put_contents(public_path('officeDoc/test1.pdf'), $output);
        } catch (Error $e) {
            Log::debug($e);
        }
    }

    public function generateAttestation(Request $request)
    {
        $request->validate([
            'id' => 'required',
            'etablissement_id' => 'required',
            'status' => 'required',
        ]);
        // if status not refuse and not annuler, the amount and issue number are required
        /*if($request->status != "refuse"){
            $request->validate([
                'montant' => 'required|numeric',
                'num_emission' => 'required|numeric'
            ]);
        }*/
        $attestation = Attestation::findOrFail($request->id);

        // using just Attestation Non-Boursier

        if ($attestation->status != "prete") {
            $attestation->update([
                'status' => "en_cours_de_traitement",
                'date_enregistrement_bureau_ordre' => date("Y-m-d"),
                'etablissement_id' => $request->etablissement_id,
                'montant' => isset($request['montant']) ? $request->montant : $attestation->montant,
                'num_emission' => isset($request['num_emission']) ? $request->num_emission : $attestation->num_emission,
                'date_mandat' => isset($request['date_mandat']) ? $request->date_mandat : $attestation->date_mandat,
            ]);
        }


        $date = $attestation->date_enregistrement_bureau_ordre;
        $filigrane = $attestation->attestationType->filigrane;
        $office = Office::find($attestation->office_id);
        $annee = $attestation->year; //. "-" . ($attestation->year +1 );

        if ($attestation->langue == "francais") {
            $title = Str::upper($attestation->attestationType->title_fr);
            if ($attestation->attestationType->id == 1) { // attestation de non-boursier
                # code...
                $parag = "Ne bénéficie pas d'une bourse universitaire ";
            } elseif ($attestation->attestationType->id == 2) { // attestation de bourse
                # code...
                $parag = "Bénéficie d'une bourse universitaire ";
            } elseif ($attestation->attestationType->id == 3) { // attestation de prêt
                # code...
                $parag = "Bénéficie d'un prêt universitaire  d'un montant de " . $attestation->montant . " dinars";
            } elseif ($attestation->attestationType->id == 4) { // attestation non prêt non bourse
                # code...
                $parag = "Ne bénéficie pas d'une bourse universitaire et d'un prêt ";
            } elseif ($attestation->attestationType->id == 5) { // attestation prolongation de validité
                # code...
            }

            $etablissement = Etablissement::find($attestation->etablissement_id)->name_fr;

            $etablissement = str_replace("&", " ", $etablissement);
            $etablissement = str_replace(">", " ", $etablissement);
            $etablissement = str_replace("<", " ", $etablissement);
            $etablissement = str_replace("'", " ", $etablissement);
            $etablissement = str_replace('"', " ", $etablissement);

            $gouvernorat = $office->gouvernorat;
            $officeName = $office->name;

            $month_name = array("", "Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre");

            $split = preg_split('/-/', $date);
            $year = $split[0];
            $month = round($split[1]);
            $day = round($split[2]);
            $date = $day . '  ' . $month_name[$month] . ' ' . $year;

            // set name
            $student = $attestation->student;

            if (isset($student->name)) {
                $name = @$student->name;
            } else {
                if (isset($student->name_ar)) {
                    $name = @$student->name_ar;
                } else {
                    # code...
                    return response()->json("Nom etudiant vide", 404);
                }
            }

            // set document type ( cin / matricule )
            if ($student->type == "etranger") {
                $cin = @$student->matricule;
                $document = "du matricule";
            } else {
                $cin = @$student->cin;
                $document = "de la carte d'identité nationale";
            }

            $html = view('attestationFr', compact('officeName', 'filigrane', 'title', 'name', 'cin', 'document', 'annee', 'etablissement', 'parag', 'gouvernorat', 'date'))->render();
        }
        elseif ($attestation->langue == "arabe") {
            # code...
            $title = Str::upper($attestation->attestationType->title_ar);

            if ($attestation->attestationType->id == 1) { // attestation de non-boursier
                # code...
                $parag = "لا ينتفع بمـنحـة جامعية ";
            } elseif ($attestation->attestationType->id == 2) { // attestation de bourse
                # code...
                $parag = "ينتفع بمـنحـة جامعية ";
            } elseif ($attestation->attestationType->id == 3) { // attestation de prêt
                # code...
                $parag = "انتفع(ت) بقرض جامعي قيمته " . $attestation->montant . " دينار. ";
            } elseif ($attestation->attestationType->id == 4) { // attestation non prêt non bourse
                # code...
                $parag = "لا ينتفع بمنحة جامعية و قرض ";
            } elseif ($attestation->attestationType->id == 5) { // attestation prolongation de validité
                # code...
                $parag = "";
            }

            $etablissement = Etablissement::find($attestation->etablissement_id)->name_ar;

            $etablissement = str_replace("&", " ", $etablissement);
            $etablissement = str_replace(">", " ", $etablissement);
            $etablissement = str_replace("<", " ", $etablissement);
            $etablissement = str_replace("'", " ", $etablissement);
            $etablissement = str_replace('"', " ", $etablissement);

            $gouvernorat = $office->gouvernorat_ar;
            $officeName = $office->name_ar;

            $month_name = array("", "جانفي", "فيفري", "مارس", "أفريل", "ماي", "جوان", "جويلية", "أوت", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر");

            $split = preg_split('/-/', $date);
            $year = $split[0];
            $month = round($split[1]);
            $day = round($split[2]);
            $date = $day . '  ' . $month_name[$month] . ' ' . $year;

            // set name
            $student = $attestation->student;

            if (isset($student->name_ar)) {
                $name = @$student->name_ar;
            } else {
                if (isset($student->name)) {
                    $name = @$student->name;
                } else {
                    # code...
                    return response()->json("Nom etudiant vide", 404);
                }
            }

            // set document type ( cin / matricule )
            if ($student->type == "etranger") {
                $cin = @$student->matricule;
                $document = "المعرف الوحيد";
            } else {
                $cin = @$student->cin;
                $document = "بطاقة التعريف الوطنية";
            }

            if ($attestation->attestationType->id == 5) {
                $datemandat = $attestation->date_mandat;
                $number = $attestation->num_emission;
                $montant = $attestation->montant;
                $html = view('attestationProlongationAr', compact('officeName', 'filigrane', 'name', 'cin', 'document', 'datemandat', 'number', 'montant', 'gouvernorat', 'date'));
            } else {
                $html = view('attestationAr2', compact('officeName', 'filigrane', 'title', 'name', 'cin', 'document', 'annee', 'etablissement', 'parag', 'gouvernorat', 'date'));

            }
        }

        // Setup a filename
        $documentFileName = "attestation" . time() . ".pdf";

        // Create the mPDF document
        $doc = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => '3',
            'margin_top' => '20',
            'margin_bottom' => '20',
            'margin_footer' => '2',
        ]);

        // Set some header informations for output
        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $documentFileName . '"'
        ];
        $doc->WriteHTML($html);


        // Save PDF on your public storage
        Storage::put($documentFileName, $doc->Output($documentFileName, "S"));
        // Get file back from storage with the give header informations

        return response()->download(storage_path('app').'/'.$documentFileName, $documentFileName, $header)->deleteFileAfterSend(true); //

    }

    public function generateAttestationNoChanges(Request $request)
    {
        $request->validate([
            'id' => 'required',
            //'etablissement_id' => 'required',
            //'status' => 'required',
        ]);
        // if status not refuse and not annuler, the amount and issue number are required
        /*if($request->status != "refuse"){
            $request->validate([
                'montant' => 'required|numeric',
                'num_emission' => 'required|numeric'
            ]);
        }*/
        $attestation = Attestation::findOrFail($request->id);

        // using just Attestation Non-Boursier

        // if ($attestation->status != "prete") {
        //     $attestation->update([
        //         'status' => "en_cours_de_traitement",
        //         'date_enregistrement_bureau_ordre' => date("Y-m-d"),
        //         'etablissement_id' => $request->etablissement_id,
        //         'montant' => isset($request['montant']) ? $request->montant : $attestation->montant,
        //         'num_emission' => isset($request['num_emission']) ? $request->num_emission : $attestation->num_emission,
        //         'date_mandat' => isset($request['date_mandat']) ? $request->date_mandat: $attestation->date_mandat,
        //     ]);
        // }


        $date = $attestation->date_enregistrement_bureau_ordre;
        $office = Office::find($attestation->office_id);
        $annee = $attestation->year; //. "-" . ($attestation->year +1 );
        $filigrane = $attestation->attestationType->filigrane;


        if ($attestation->langue == "francais") {
            $title = Str::upper($attestation->attestationType->title_fr);
            if ($attestation->attestationType->id == 1) { // attestation de non-boursier
                # code...
                $parag = "Ne bénéficie pas d'une bourse universitaire ";
            } elseif ($attestation->attestationType->id == 2) { // attestation de bourse
                # code...
                $parag = "Bénéficie d'une bourse universitaire ";
            } elseif ($attestation->attestationType->id == 3) { // attestation de prêt
                # code...
                $parag = "Bénéficie d'un prêt universitaire  d'un montant de " . $attestation->montant . " dinars";
            } elseif ($attestation->attestationType->id == 4) { // attestation non prêt non bourse
                # code...
                $parag = "Ne bénéficie pas d'une bourse universitaire et d'un prêt ";
            } elseif ($attestation->attestationType->id == 5) { // attestation prolongation de validité
                # code...
            }

            $etablissement = Etablissement::find($attestation->etablissement_id)->name_fr;

            $etablissement = str_replace("&", " ", $etablissement);
            $etablissement = str_replace(">", " ", $etablissement);
            $etablissement = str_replace("<", " ", $etablissement);
            $etablissement = str_replace("'", " ", $etablissement);
            $etablissement = str_replace('"', " ", $etablissement);

            $gouvernorat = $office->gouvernorat;
            $officeName = $office->name;

            $month_name = array("", "Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre");

            $split = preg_split('/-/', $date);
            $year = $split[0];
            $month = round($split[1]);
            $day = round($split[2]);
            $date = $day . '  ' . $month_name[$month] . ' ' . $year;

            // set name
            $student = $attestation->student;

            if (isset($student->name)) {
                $name = @$student->name;
            } else {
                if (isset($student->name_ar)) {
                    $name = @$student->name_ar;
                } else {
                    # code...
                    return response()->json("Nom etudiant vide", 404);
                }
            }

            // set document type ( cin / matricule )
            if ($student->type == "etranger") {
                $cin = @$student->matricule;
                $document = "du matricule";
            } else {
                $cin = @$student->cin;
                $document = "de la carte d'identité nationale";
            }

            $html = view('attestationFr', compact('filigrane', 'officeName', 'title', 'name', 'cin', 'document', 'annee', 'etablissement', 'parag', 'gouvernorat', 'date'))->render();
        }
        elseif ($attestation->langue == "arabe") {
            # code...
            $title = Str::upper($attestation->attestationType->title_ar);

            if ($attestation->attestationType->id == 1) { // attestation de non-boursier
                # code...
                $parag = "لا ينتفع بمـنحـة جامعية ";
            } elseif ($attestation->attestationType->id == 2) { // attestation de bourse
                # code...
                $parag = "ينتفع بمـنحـة جامعية ";
            } elseif ($attestation->attestationType->id == 3) { // attestation de prêt
                # code...
                $parag = "انتفع(ت) بقرض جامعي قيمته " . $attestation->montant . " دينار. ";
            } elseif ($attestation->attestationType->id == 4) { // attestation non prêt non bourse
                # code...
                $parag = "لا ينتفع بمنحة جامعية و قرض ";
            } elseif ($attestation->attestationType->id == 5) { // attestation prolongation de validité
                # code...
                $parag = "";
            }

            $etablissement = Etablissement::find($attestation->etablissement_id)->name_ar;

            $etablissement = str_replace("&", " ", $etablissement);
            $etablissement = str_replace(">", " ", $etablissement);
            $etablissement = str_replace("<", " ", $etablissement);
            $etablissement = str_replace("'", " ", $etablissement);
            $etablissement = str_replace('"', " ", $etablissement);

            $gouvernorat = $office->gouvernorat_ar;
            $officeName = $office->name_ar;

            $month_name = array("", "جانفي", "فيفري", "مارس", "أفريل", "ماي", "جوان", "جويلية", "أوت", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر");

            $split = preg_split('/-/', $date);
            $year = $split[0];
            $month = round($split[1]);
            $day = round($split[2]);
            $date = $day . '  ' . $month_name[$month] . ' ' . $year;

            // set name
            $student = $attestation->student;

            if (isset($student->name_ar)) {
                $name = @$student->name_ar;
            } else {
                if (isset($student->name)) {
                    $name = @$student->name;
                } else {
                    # code...
                    return response()->json("Nom etudiant vide", 404);
                }
            }

            // set document type ( cin / matricule )
            if ($student->type == "etranger") {
                $cin = @$student->matricule;
                $document = "المعرف الوحيد";
            } else {
                $cin = @$student->cin;
                $document = "بطاقة التعريف الوطنية";
            }

            if ($attestation->attestationType->id == 5) {
                $datemandat = $attestation->date_mandat;
                $number = $attestation->num_emission;
                $montant = $attestation->montant;
                $html = view('attestationProlongationAr', compact('filigrane', 'officeName', 'name', 'cin', 'document', 'datemandat', 'number', 'montant', 'gouvernorat', 'date'));
            } else {
                $html = view('attestationAr2', compact('filigrane', 'officeName', 'title', 'name', 'cin', 'document', 'annee', 'etablissement', 'parag', 'gouvernorat', 'date'));

            }
        }

        // Setup a filename
        $documentFileName = "attestation" . time() . ".pdf";

        // Create the mPDF document
        $doc = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => '3',
            'margin_top' => '20',
            'margin_bottom' => '20',
            'margin_footer' => '2',
        ]);

        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $documentFileName . '"'
        ];
        $doc->WriteHTML($html);


        // Save PDF on your public storage
        Storage::put($documentFileName, $doc->Output($documentFileName, "S"));
        // Get file back from storage with the give header informations

        return response()->download(storage_path('app').'/'.$documentFileName, $documentFileName, $header)->deleteFileAfterSend(true); //

    }

    public function document($html)
    {
        // Setup a filename
        $documentFileName = "attestation.pdf";

        // Create the mPDF document
        $document = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => '3',
            'margin_top' => '20',
            'margin_bottom' => '20',
            'margin_footer' => '2',
        ]);

        // Set some header informations for output
        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $documentFileName . '"'
        ];

        //$document->WriteHTML(view('attestationAr2', compact('msg')));
        $document->WriteHTML($html);

        // Save PDF on your public storage
        Storage::put($documentFileName, $document->Output($documentFileName, "S"));
        // Get file back from storage with the give header informations
        return Storage::download($documentFileName, 'Request', $header); //
    }

    public function getAttestationDocument(Request $request)
    {

        $request->validate([
            'id' => 'required',
        ]);
        $doc =DocumentsAttestationUpload::findOrFail($request->id);
        if (!$doc) {
            return response()->json('Document not found',404);
        }
        $filename = $doc->attached_file;

        if (!Storage::exists('uploads/attestations/' . $doc->attestation->id.'/'. $filename)) {
            return response()->json(Storage::url('uploads/attestations/' . $doc->attestation->id.'/'. $filename), 404);
        }
        return Storage::download('uploads/attestations/' . $doc->attestation->id.'/'. $filename, $filename);

    }


    public function exportExcel(Request $request)
    {
        $export = new AttestationExport($request);
        return Excel::download($export, 'Reclamation.xlsx');
    }
}
