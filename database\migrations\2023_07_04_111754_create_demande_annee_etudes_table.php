<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('demande_annee_etudes', function (Blueprint $table) {
            $table->id();

            $table->integer('demande_id');
            $table->integer('user_id')->nullable();
            $table->integer('filiere_id')->nullable();
            $table->string('code_etab')->nullable();
            $table->string('code_diplome')->nullable();
            $table->integer('annee_etude')->nullable();
            $table->integer('annee_universitaire_id')->nullable();

            $table->integer('resultat_id')->nullable();
            $table->string('moyenne')->nullable();
            $table->string('credit')->nullable();

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('demande_annee_etudes');
    }
};
