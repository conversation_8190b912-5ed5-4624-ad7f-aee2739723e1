<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Http\Requests\StoreDocumentClassificationRequest;
use App\Http\Resources\DocumentsAttestationResource;
use App\Models\AttestationType;
use App\Models\Classification;
use App\Models\DocumentsAttestation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use League\CommonMark\Node\Block\Document;

class DocumentsAttestationController extends Controller
{
    protected $cache_seconds = 900;

    // $sourceFilePath = public_path("officeDoc/notes.docx");
//    public function __construct()
//    {
        //        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
//    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('DocumentsAttestation', $this->cache_seconds, function () {
            return DocumentsAttestationResource::collection(DocumentsAttestation::query()->get());
        });

    }

    /**
     * Display a listing of the resource.
     *
     * @param AttestationType $attestationType
     * @return AnonymousResourceCollection
     */
    public function byAttestationType(AttestationType $attestationType): AnonymousResourceCollection
    {
        return DocumentsAttestationResource::collection(DocumentsAttestation::where('attestation_type_id', $attestationType->id)->with('attestationType')->get());

    }


    /**
     * Store a newly created resource in storage.
     *
     * @param StoreDocumentClassificationRequest $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            //code...

            $request->validate([
                'code' => 'required|string',
                'title' => 'required|string',
                'title_ar' => 'required|string',
                'title_fr' => 'required|string',
            ]);

            Cache::forget('DocumentsAttestation');
            Helpers::clearCacheIdp(['AttestationType','DocumentsAttestation']);

            DocumentsAttestation::create([
                'code' => $request->code,
                'title' => $request->title,
                'title_ar' => $request->title_ar,
                'title_fr' => $request->title_fr,

            ]);

            return response("created", 200);
        } catch (\Throwable $th) {
            //throw $th;
            return $th->getMessage();
        }

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Request $request
     * @return Response
     */
    public function edit(Request $request)
    {
        try {
            $request->validate([
                'id' => 'required',
                "code" => 'required|string',
                "title" => 'required|string',
                "title_ar" => 'required|string',
                "title_fr" => 'required|string',
            ]);


            Cache::forget('DocumentsAttestation');
            Helpers::clearCacheIdp(['AttestationType','DocumentsAttestation']);

            $document = DocumentsAttestation::findOrFail($request->id)->update([
                'code' => $request->code,
                'title' => $request->title,
                'title_ar' => $request->title_ar,
                'title_fr' => $request->title_fr,

            ]);
            return response("success", 201);
        } catch (\Throwable $th) {
            //throw $th;
            return $th->getMessage();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateDocumentClassificationRequest $request
     * @param DocumentClassificationResource $documentClassification
     * @return Response
     */
    public function update(Request $request)
    {
        //dd("update works");

        $data = $request->validated();

        //$documentClassification->update($data);

        return response("success", 201);
    }

    /**
     * Remove the specified resource from storage.
     *
     */
    public function destroy(DocumentsAttestation $documents_attestation)
    {
        Cache::forget('DocumentsAttestation');
        Helpers::clearCacheIdp(['AttestationType','DocumentsAttestation']);

        $documents_attestation->delete();
        return response("ok", 204);
    }
}
