<?php
namespace App\Exports;

use App\Models\Etablissement;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class EtablissementImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {

        Validator::make($rows[0]->toArray(),
            [
                'lib_etab_fr' => 'required',
                'lib_etab_ar' => 'required',
                'code_etab' => 'required',
                'code_univ' => 'required',
//                'code_ministre' => 'required',
//                'code_dir_reg' => 'required',
                'code_office' => 'required',
            ],
            [],
            [
                'lib_etab_fr' => '(lib_etab_fr)',
                'lib_etab_ar' => '(lib_etab_ar)',
                'code_etab' => '(code_etab)',
                'code_univ' => '(code_univ)',
//                'code_ministre' => '(code_ministre)',
//                'code_dir_reg' => '(code_dir_reg)',
                'code_office' => '(code_office)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.lib_etab_fr' => 'required|string',
            '*.lib_etab_ar' => 'required|string',
            '*.code_etab' => 'required',
            '*.code_univ' => 'required|numeric',
            '*.code_office' => 'required',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Etablissement::where('code', $row['code_etab'])->first();
            if ($gov){
                $gov->update([
                    'name' => $row['lib_etab_fr'],
                    'name_fr' => $row['lib_etab_fr'],
                    'name_ar' => $row['lib_etab_ar'],
                    'code_univ' => $row['code_univ'],
                    'code_dir_reg' => $row['code_dir_reg'],
                    'code_ministre' => $row['code_ministre'],
                    'code_office' => $row['code_office'],
                ]);
            } else {
                Etablissement::create([
                    'name' => $row['lib_etab_fr'],
                    'name_fr' => $row['lib_etab_fr'],
                    'name_ar' => $row['lib_etab_ar'],
                    'code' => $row['code_etab'],
                    'code_univ' => $row['code_univ'],
                    'code_dir_reg' => $row['code_dir_reg'],
                    'code_ministre' => $row['code_ministre'],
                    'code_office' => $row['code_office'],
                ]);
            }
        }
    }
}
