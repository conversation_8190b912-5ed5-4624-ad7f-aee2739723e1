<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class ReclamationTypeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'title' => $this->title,
            'title_fr' => $this->title_fr,
            'title_ar' => $this->title_ar,
            'fils' => ReclamationTypeResource::collection($this->fils),
            'parent_id' =>$this->parent_id,
            'parent' => $this->parent,
            'created_at' => $this->created_at->format('Y-m-d'),
            'created_at_fr' => $this->created_at->format('d/m/Y H:i'),
        ];
    }
}
