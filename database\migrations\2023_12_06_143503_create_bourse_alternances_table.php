<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bourse_alternances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('annee_universitaire_id');
            $table->unsignedBigInteger('student_id');

            $table->string('nb_mois')->nullable();
            $table->string('nb_decision')->nullable();
            $table->string('nature_decision')->nullable();

            $table->foreign('annee_universitaire_id')
                ->references('id')
                ->on('annee_universitaires');

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bourse_alternances');
    }
};
