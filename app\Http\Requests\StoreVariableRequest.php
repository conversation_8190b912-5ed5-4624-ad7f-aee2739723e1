<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class StoreVariableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'label' => 'nullable|string',
            'label_fr' => 'nullable|string',
            'label_ar' => 'nullable|string',
            'field' => 'nullable|string',
            'code' => 'required|string|alpha_dash|unique:variables,code',
            'value' => 'sometimes|nullable',
        ];
    }
}
