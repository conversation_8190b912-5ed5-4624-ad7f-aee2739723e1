<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('etablissements', function (Blueprint $table) {
            $table->string('code')->change();
            $table->integer('code_ministre')->nullable();
            $table->integer('code_dir_reg')->nullable();
            $table->integer('annee_universitaire')->nullable();
            $table->string('code_office')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('etablissements', function (Blueprint $table) {
            $table->dropColumn('code_ministre');
            $table->dropColumn('code_dir_reg');
            $table->dropColumn('annee_universitaire');
            $table->dropColumn('code_office');
            $table->integer('code')->change();

        });
    }
};
