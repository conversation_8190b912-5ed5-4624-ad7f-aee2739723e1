<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class InternationalStudent extends Model implements Auditable
{
    use Sortable;
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    public $sortable = [
        'num_passport',
        'matricule',
        'name',
        'firstName',
        'annee_bac',
    ];
    public $fillable = [
        'num_passport',
        'matricule',
        'name',
        'firstName',
        'phoneNumber',
        'address',
        'zipCode',
        'dateOfBirth',
        'placeOfBirth',
        'sex',
        'nationality_id',
        'code_etab',
        'filiere_id',
        'foyer',
        'annee_bac',
        'annee_universitaire',
    ];

    protected $casts = [
        'dateOfBirth' => 'date',
    ];

    public function etablissement() : BelongsTo
    {
        return $this->belongsTo(Etablissement::class,"code_etab","code");
    }

    public function filiere() : BelongsTo
    {
        return $this->belongsTo(Filiere::class,"filiere_id","id");
    }

    public function nationality() : BelongsTo
    {
        return $this->belongsTo(Country::class,"nationality_id","id");
    }

    public function anneeBac() : BelongsTo
    {
        return $this->belongsTo(AnneeBac::class,"annee_bac","id");
    }

    public function anneeUniversitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,"annee_universitaire","id");
    }


}
