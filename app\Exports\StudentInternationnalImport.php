<?php
namespace App\Exports;

use App\Models\AnneeBac;
use App\Models\AnneeUniversitaire;
use App\Models\Country;
use App\Models\Filiere;
use App\Models\InternationalStudent;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class StudentInternationnalImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                "passport" => 'required',
                "matricule" => 'required',
                "nom" => 'required',
                "annee_univ" => 'required',
                "nat_iso" => 'required',
                "etablissement" => 'required',
                "annee_bac" => 'required',
                "disp" => 'required',
            ],
            [],
            [
                "passport" => '(passport)',
                "matricule" => '(matricule)',
                "nom" => '(nom)',
                "nat_iso" => '(code pays iso)',
                "annee_univ" => '(annee_univ)',
                "etablissement" => '(etablissement)',
                "annee_bac" => '(annee_bac)',
                "disp" => '(disp)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.passport' => 'required',
            '*.matricule' => 'required|string',
            '*.nom' => 'required|string',
            '*.annee_bac' => 'required|digits:4',
            '*.disp' => 'required|string',
        ])->validate();

        foreach ($rows as $row) {
            $std = InternationalStudent::where('matricule', $row['matricule'])->first();
            $annee_bac_id = AnneeBac::where('title', $row['annee_bac'])?->first()?->id;
            $annee_univ_id = AnneeUniversitaire::where('title', $row['annee_bac'])?->first()?->id;
            $filiere_id = Filiere::where('code', $row['filiere'])?->where('annee_universitaire', $annee_univ_id)?->first()?->id;
            $nationality_id = Country::where('code', substr($row['matricule'], 0, 2))?->first()?->id ??
                Country::where('code', $row['nat_iso'])?->first()?->id;
            $dateNaissance = null;
            if( strlen((string)$row['date_naissance']) === 6 ){
                $yearsNaissance = substr($row['date_naissance'], 4, 2) < 50 ? '20'.substr($row['date_naissance'], 4, 2) : '19'.substr($row['date_naissance'], 4, 2);
                $dateNaissance = $yearsNaissance.'-'.substr($row['date_naissance'], 2, 2).'-'.substr($row['date_naissance'], 0, 2);
            }

            if ($nationality_id &&  $annee_bac_id && $annee_univ_id){
                if ($std){
                    $std->update([
                        'num_passport' => $row['passport'],
                        'matricule' => $row['matricule'],
                        'name' => $row['nom'],
                        'firstName' => $row['prenom'],
                        'phoneNumber' => $row['telephone'],
                        'dateOfBirth' => $dateNaissance,
                        'sex' => $row['genre'],
                        'nationality_id' => $nationality_id,
                        'code_etab' => $row['etablissement'],
                        'filiere_id' => $filiere_id,
                        'foyer' => $row['foyer'],
                        'annee_bac' => $annee_bac_id,
                        'annee_universitaire' => $annee_univ_id,
                    ]);
                }
                else {
                    InternationalStudent::create([
                        'num_passport' => $row['passport'],
                        'matricule' => $row['matricule'],
                        'name' => $row['nom'],
                        'firstName' => $row['prenom'],
                        'phoneNumber' => $row['telephone'],
                        'dateOfBirth' => $dateNaissance,
                        'sex' => $row['genre'],
                        'nationality_id' => $nationality_id,
                        'code_etab' => $row['etablissement'],
                        'filiere_id' => $filiere_id,
                        'foyer' => $row['foyer'],
                        'annee_bac' => $annee_bac_id,
                        'annee_universitaire' => $annee_univ_id,
                    ]);
                }
            }
        }

    }
}
