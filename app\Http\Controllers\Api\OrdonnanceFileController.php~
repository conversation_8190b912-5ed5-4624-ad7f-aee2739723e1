<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreOrdonnanceFileRequest;
use App\Jobs\GenerateOrdonnancePDF;
use App\Models\Mandate;
use App\Models\OrdonnanceFile;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrdonnanceFileController extends Controller
{
    public function index(Request $request)
    {
        $files =  OrdonnanceFile::when(
                $request->has('annee_gestion'),
                function ($query) use ($request) {
                    return $query->where('annee_gestion',  'like', '%' . $request->annee_gestion . '%');
                }
            )
            ->when(
                $request->has('ndec'),
                function ($query) use ($request) {
                    return $query->where('num_dec',  'like', '%' . $request->ndec . '%');
                }
            )
            ->when(
                $request->has('annee_universitaire_id'),
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id',  $request->annee_universitaire_id);
                }
            )
            ->orderBy('id', 'desc')
            ->with('annee_universitaire')
            ->paginate(
                $request->input('perPage') ?? config('constants.pagination'),
            );
        return response()->json($files, 200);
    }

    public function generatePDF(StoreOrdonnanceFileRequest $request)
    {
        $formatted_date = Carbon::createFromFormat('d/m/Y', $request->date_payment)->format('Y-m-d');
        $nbr = Mandate::whereDate(DB::raw('STR_TO_DATE(`date_payement`, "%d%m%y")'), $formatted_date)->count();
        if($nbr==0){
            return response()->json([
                'message'=>"N'existe pas des mandats avec cette date de paiement",
                'errors'=> [
                    "date_payment" => [
                       "N'existe pas des mandats avec cette date de paiement" 
                    ],
                 ] 
            ],422);
        }
        $row=OrdonnanceFile::create([
            'num_dec'  => $request->ndec,
            'annee_gestion'  => $request->annee_gestion,
            'etat' => 0,
            'date_payment' => $request->date_payment,
            'annee_universitaire_id' => $request->annee_universitaire_id
        ]);

        dispatch(new GenerateOrdonnancePDF($request->all(), $row->id));
        return response()->json("success", 200);
    }
}
