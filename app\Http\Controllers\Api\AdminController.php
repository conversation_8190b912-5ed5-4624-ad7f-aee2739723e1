<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAdminRequest;
use App\Http\Requests\UpdateAdminRequest;
use App\Http\Resources\AdminResource;
use App\Models\Admin;
use App\Models\Role;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class AdminController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return AdminResource::collection(Admin::orderBy('id', 'desc')->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreAdminRequest $request
     * @return Response
     */
    public function store(StoreAdminRequest $request): Response
    {
        $data = $request->validated();
        $data['password'] = bcrypt($data['password']);
        $user = Admin::create($data);

        $startDate = $request->start_at ? Carbon::parse($request->start_at) : null;
        $endDate = $request->end_at ? Carbon::parse($request->end_at) : null;

        $role = Role::where('name', $data['role'])->first();
        $user->roles()->attach($role, [
            'start_at' => $startDate,
            'end_at' => $endDate,
        ]);

        return response(new AdminResource($user) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Admin $user
     * @return AdminResource
     */
    public function show(Admin $user): AdminResource
    {
        return new AdminResource($user);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateAdminRequest $request
     * @param Admin $user
     * @return AdminResource
     */
    public function update(UpdateAdminRequest $request, Admin $user): AdminResource
    {
        $data = $request->validated();
        if (isset($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        }
        $user->username = $data['name'];
        $user->update($data);

        return new AdminResource($user);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Admin $user
     * @return Response
     */
    public function destroy(Admin $user): Response
    {
        $user->syncRoles([]);

        $user->delete();

        return response("", 204);
    }
}
