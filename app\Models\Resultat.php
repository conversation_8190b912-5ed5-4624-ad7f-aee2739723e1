<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Resultat extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'active',
        'is_credit',
        'success',
    ];


    protected $casts = [
        'active' => 'boolean',
        'is_credit' => 'boolean',
        'success' => 'boolean',
    ];


    public function etudiantAnneeUniversitaires() : HasMany
    {
        return $this->setConnection( config('database.secondConnection') )->hasMany(EtudiantAnneeUniversitaire::class, 'resultat_id', 'id');
    }
}
