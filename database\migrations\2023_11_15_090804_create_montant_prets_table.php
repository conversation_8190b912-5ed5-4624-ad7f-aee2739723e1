<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('montant_prets', function (Blueprint $table) {
            $table->id();
            $table->string('code_diplome')->nullable();
            $table->integer('annee_etude')->nullable();
            $table->boolean('resultat')->nullable();
            $table->integer('montant')->nullable();
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('montant_prets');
    }
};
