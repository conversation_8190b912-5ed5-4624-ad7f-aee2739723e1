<?php

namespace App\Http\Controllers\Api;

use App\Exports\DiplomeImport;
use App\Exports\DistanceImport;
use App\Exports\EtablissementDiplomeImport;
use App\Exports\EtablissementImport;
use App\Exports\FiliereImport;
use App\Exports\GouvernoratImport;
use App\Exports\LyceeImport;
use App\Exports\MontantPretImport;
use App\Exports\OfficeImport;
use App\Exports\ProfessionImport;
use App\Exports\StudentFromMesImport;
use App\Exports\StudentFromMesModelImport;
use App\Exports\StudentInternationnalImport;
use App\Exports\UniversiteImport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUploadedFileRequest;
use App\Http\Requests\UpdateUploadedFileRequest;
use App\Http\Resources\DocumentPredefiniResource;
use App\Http\Resources\UploadedFileResource;
use App\Jobs\NotifyUserOfCompletedImport;
use App\Models\DocumentClassificationDemande;
use App\Models\UploadedFile;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRowImport;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;

class UploadFileController extends Controller
{

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Application|ResponseFactory|Response|JsonResponse
     */
    public function store(Request $request) : Application|ResponseFactory|Response|JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'disk' => 'required|string',
            'fileName' => 'required|string',
            'file_to_upload'=> 'required|file',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        if( $request->hasFile('file_to_upload') ){
            $file = $request->file('file_to_upload');
            $file->storeAs($request->path,$request->fileName,$request->disk);
        }
        return response('file uploaded' , 201);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return JsonResponse|BinaryFileResponse
     */
    public function getFile(Request $request) : JsonResponse|BinaryFileResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'disk' => 'required|string',
            'fileName' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        if(!Storage::exists($request->path.'/'.$request->fileName)){
            return response()->json(Storage::path($request->path.'/'.$request->fileName), 404);
        }
        Log::info("found file: " . $request->path.'/'.$request->fileName);

//        return Storage::download($request->path.'/'.$request->fileName);
        return response()->file(Storage::path($request->path.'/'.$request->fileName));
    }



}
