<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class RegisteredStudentMicroResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'firstName' => $this->firstName,
            'name' => $this->name,
            'name_ar' => $this->name_ar,
            'email' => $this->email,
            'annee_bac' => $this->annee_bac,
            'num_bac' => $this->num_bac,
            'cin' => $this->cin,
            'matricule' => $this->matricule,
            'num_passport' => $this->num_passport,
            'nationality_id' => $this->nationality_id,
            'country_id' => $this->country_id,
            'code_gouv' => $this->code_gouv,
            'delegation_id' => $this->delegation_id,
            'zipCode' => $this->zipCode,
            'sex' => $this->sex,
            'type' => $this->type,
            'status' => $this->status,
            'etudiant_annee_universitaires' => EtudiantAnneeUniversitairesResource::collection($this->etudiantAnneeUniversitaires),
            'gouvernorat' => $this->gouvernorat,
            'nationality' => $this->nationality,
            'anneeBac' => $this->anneeBac,

        ];
    }
}
