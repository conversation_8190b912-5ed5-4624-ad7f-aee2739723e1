<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreReclamationTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string|unique:classifications,code',
            'title' => 'required|string',
            'title_fr' => 'nullable|string',
            'title_ar' => 'nullable|string',
            'parent_id' => 'nullable|integer',
        ];
    }
}
