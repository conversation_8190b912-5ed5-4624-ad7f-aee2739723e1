<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class VariableResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'label' => $this->label,
            'label_fr' => $this->label_fr,
            'label_ar' => $this->label_ar,
            'code' => $this->code,
            'value' => $this->value,
            'field' => $this->field,
//            'created_at' => $this->created_at->format('Y-m-d'),
        ];
    }
}
