<?php

namespace App\Notifications;

use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class DossierEligibleBourseInsertionNotification extends Notification
{
    use Queueable;

    private Demande $demande;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande)
    {
        $this->demande = $demande;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Agreement in principle for insertion scholarship",
            "subtitle" => "Agreement in principle for insertion scholarship to demand n°: ". $this->demande->code,
            "title_fr" => "Accord de principe pour bourse d'insertion",
            "subtitle_fr" => "Accord de principe pour la bourse d'insertion de la demande n°: " . $this->demande->code,
            "title_ar" => "موافقة مبدئية للحصول على منحة الإدماج ",
            "subtitle_ar" => "موافقة مبدئية للحصول على منحة الإدماج للطلب عدد : " . $this->demande->code,
            "avatarIcon" => "confetti",
            "avatarAlt" => "Dossier",
            "avatarText" => "Dossier",
            "avatarColor" => "success",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
