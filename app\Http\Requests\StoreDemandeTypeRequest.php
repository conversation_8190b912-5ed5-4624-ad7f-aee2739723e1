<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDemandeTypeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'type' => 'required|string',
            'group' => 'nullable|string',
            'code' => 'nullable|string',
            'title_fr' => 'required|string',
            'title_ar' => 'required|string',
            'active' => 'nullable|boolean',
            'bourse' => 'nullable|boolean',
            'aide_sociale' => 'nullable|boolean',
            'bourses_de_stage' => 'nullable|boolean',
            'bourse_insertion' => 'nullable|boolean',
            'pret' => 'nullable|boolean',
            'order' => 'nullable|integer',
            'config' => 'nullable|json',
            'logic' => 'nullable|json',
            'parent_id' => 'nullable|integer',
            'start' => 'nullable|date_format:Y-m-d',
            'end' => 'nullable|date_format:Y-m-d',
            'date_dossier_end' => 'nullable|date_format:Y-m-d',
            'date_complement_end' => 'nullable|date_format:Y-m-d',
            'date_contrat_pret_end' => 'nullable|date_format:Y-m-d',
            'diplomes' => 'array',
            'is_parent_etranger' => 'nullable|boolean',
        ];
    }
    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_parent_etranger' => $this->toBoolean($this->is_parent_etranger),
            'active' => $this->toBoolean($this->active),
            'bourse_insertion' => $this->toBoolean($this->bourse_insertion),
            'pret' => $this->toBoolean($this->pret),
            'bourse' => $this->toBoolean($this->bourse),
            'aide_sociale' => $this->toBoolean($this->aide_sociale),
            'bourses_de_stage' => $this->toBoolean($this->bourses_de_stage),
            'parent_id' => $this->parent_id ?: null,
//            'config' => json_encode($this->config),
//            'logic' => json_encode($this->logic),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }

}
