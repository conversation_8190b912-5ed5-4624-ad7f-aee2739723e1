<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttestationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('attestation_types')->insert([
            'code' => 'attestation_non_boursier',
            'title' => 'Non-Scholarship Certificate',
            'title_fr' => "Attestation De Non Bénéfice d'une Bourse Universitaire",
            'title_ar' => 'شـهـــادة في عدم الانتفاع بمنحة جامعية',
            'langues' => 'bilingue',
            'active' => 1
        ]);

        DB::table('attestation_types')->insert([
            'code' => 'attestation_boursier',
            'title' => 'Scholarship Certificate',
            'title_fr' => 'Attestation De Bourse',
            'title_ar' => 'شـهـــادة في الانتفاع بمنحة جامعية',
            'langues' => 'bilingue',
            'active' => 1
        ]);

        DB::table('attestation_types')->insert([
            'code' => 'attestation_pret',
            'title' => 'Loan Certificate',
            'title_fr' => 'Attestation De Prêt',
            'title_ar' => 'شهادة قرض',
            'langues' => 'bilingue',
            'active' => 1
        ]);

        DB::table('attestation_types')->insert([
            'code' => 'attestation_non_boursier_non_pret',
            'title' => 'Non-scholarship And Non-Loan Certificate',
            'title_fr' => "Attestation De Non Bénéfice d'une Bourse Et d'un Prêt",
            'title_ar' => 'شهادة في عدم الانتفاع بمنحة جامعية و قرض',
            'langues' => 'bilingue',
            'active' => 1
        ]);

        DB::table('attestation_types')->insert([
            'code' => 'prolongation_validite',
            'title' => 'Extension Of Validity',
            'title_fr' => 'Attestation De Prolongation De Validité',
            'title_ar' => 'شهادة تمديد الصلاحية',
            'langues' => 'ar',
            'active' => 1
        ]);
    }
}
