<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class DemandeTypeMicroResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'type' => $this->type,
            'title' => $this->title,
            'title_fr' => $this->title_fr,
            'title_ar' => $this->title_ar,
            'active' => $this->active,
            'bourse_insertion' => $this->bourse_insertion,
            'pret' => $this->pret,
            'parent_id' =>$this->parent_id,
            'group' =>$this->group,
            'parent' => new DemandeTypeMicroResource($this->parent),
            'fils_count' =>$this->fils_count,
        ];
    }
}
