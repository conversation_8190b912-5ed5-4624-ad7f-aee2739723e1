{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1.0", "barryvdh/laravel-dompdf": "^2.0", "carlos-meneses/laravel-mpdf": "^2.1", "doctrine/dbal": "^3.6", "elephantio/elephant.io": "^4.2", "fruitcake/laravel-cors": "^3.0", "guzzlehttp/guzzle": "^7.2", "kwn/number-to-words": "^2.7", "kyslik/column-sortable": "^6.0", "laravel/framework": "^9.19", "laravel/prompts": "^0.3.1", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "league/flysystem-ftp": "^3.0", "league/flysystem-sftp-v3": "^3.28", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.2", "owen-it/laravel-auditing": "^13.5", "petercoles/multilingual-country-list": "^1.2", "phpoffice/phpword": "^1.1", "rap2hpoutre/fast-excel": "^5.5", "spatie/laravel-medialibrary": "^10.10", "spatie/laravel-permission": "^5.10", "spatie/simple-excel": "^3.7"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "laravel/telescope": "^5.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "roave/security-advisories": "dev-latest", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}