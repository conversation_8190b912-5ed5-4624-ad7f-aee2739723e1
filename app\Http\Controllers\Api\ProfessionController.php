<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreProfessionRequest;
use App\Http\Requests\UpdateProfessionRequest;
use App\Http\Resources\ProfessionResource;
use App\Models\Profession;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use JetBrains\PhpStorm\Pure;

class ProfessionController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Profession', $this->cache_seconds, function () {
            return ProfessionResource::collection(Profession::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreProfessionRequest $request
     * @return Response
     */
    public function store(StoreProfessionRequest $request): Response
    {
        Cache::forget('Profession');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $g = Profession::create($data);

        return response(new ProfessionResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Profession $profession
     * @return ProfessionResource
     */
    #[Pure] public function show(Profession $profession): ProfessionResource
    {
        return new ProfessionResource($profession);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateProfessionRequest $request
     * @param Profession $profession
     * @return ProfessionResource
     */
    public function edit(UpdateProfessionRequest $request, Profession $profession): ProfessionResource
    {
        Cache::forget('Profession');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $profession->update($data);

        return new ProfessionResource($profession);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Profession $profession
     * @return Response
     */
    public function destroy(Profession $profession): Response
    {
        Cache::forget('Profession');
        Helpers::clearCacheIdp();

        $profession->delete();

        return response("", 204);
    }
}
