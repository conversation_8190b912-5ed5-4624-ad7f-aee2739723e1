<?php

namespace App\Http\Controllers;

use App\Http\Resources\RectificatifNumeroResource;
use App\Models\RectificatifNumero;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class RectificatifNumeroController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index()
    {
        return RectificatifNumeroResource::collection(RectificatifNumero::with('annee_universitaire')->get());
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //code...
        $request->validate([
            'annee_universitaire_id' => 'required|string',
            'num_decision' => 'required|string',
        ]);

        $rectif = RectificatifNumero::create([
            'annee_universitaire_id' => $request->annee_universitaire_id,
            'num_decision' => $request->num_decision,
        ]);

        return response(new RectificatifNumeroResource($rectif), 201);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\RectificatifNumero  $rectificatifNumero
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'annee_universitaire_id' => 'required|string',
                'num_decision' => 'required|string',
            ]);


            $rectif = RectificatifNumero::findOrFail($request->id)->update([
                'annee_universitaire_id' => $request->annee_universitaire_id,
                'num_decision' => $request->num_decision,
            ]);

        return response("created" , 201);

    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\RectificatifNumero  $rectificatifNumero
     * @return \Illuminate\Http\Response
     */
    public function destroy(RectificatifNumero $rectificatifNumero)
    {
        $rectificatifNumero->delete();

        return response("", 204);
    }
}
