<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreProfessionTypeRequest;
use App\Http\Requests\UpdateProfessionTypeRequest;
use App\Http\Resources\ProfessionTypeResource;
use App\Models\ProfessionType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use JetBrains\PhpStorm\Pure;

class ProfessionTypeController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('ProfessionType', $this->cache_seconds, function () {
            return ProfessionTypeResource::collection(ProfessionType::all());
        });
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(StoreProfessionTypeRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('ProfessionType');
        Helpers::clearCacheIdp();

        $g = ProfessionType::create($data);

        return response(new ProfessionTypeResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param ProfessionType $professionType
     * @return ProfessionTypeResource
     */
    #[Pure] public function show(ProfessionType $professionType): ProfessionTypeResource
    {
        return new ProfessionTypeResource($professionType);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateProfessionTypeRequest $request
     * @param ProfessionType $professionType
     * @return ProfessionTypeResource
     */
    public function edit(UpdateProfessionTypeRequest $request, ProfessionType $professionType): ProfessionTypeResource
    {
        $data = $request->validated();

        Cache::forget('ProfessionType');
        Helpers::clearCacheIdp();

        $professionType->update($data);

        return new ProfessionTypeResource($professionType);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ProfessionType $professionType
     * @return Response
     */
    public function destroy(ProfessionType $professionType): Response
    {
        Cache::forget('ProfessionType');
        Helpers::clearCacheIdp();

        $professionType->delete();

        return response("", 204);
    }
}
