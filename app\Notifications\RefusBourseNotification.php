<?php

namespace App\Notifications;

use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class RefusBourseNotification extends Notification
{
    use Queueable;

    private Demande $demande;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande)
    {
        $this->demande = $demande;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "University scholarship refused.",
            "subtitle" => "University scholarship refused for demand n°: ". $this->demande->code,
            "title_fr" => "Bourse universitaire refusé",
            "subtitle_fr" => "Bourse universitaire refusé pour la demande n°: " . $this->demande->code,
            "title_ar" => "تم رفض طلب المنحة الجامعية",
            "subtitle_ar" => " تم رفض طلب المنحة الجامعية : " . $this->demande->code,
            "avatarIcon" => "report-money",
            "avatarAlt" => "Bourse",
            "avatarText" => "Bourse",
            "avatarColor" => "error",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
