<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class FilsParentByProfessionTypeAndAnneeStatExport implements WithEvents, FromView
{
    protected $data;
    protected $year;
    protected $profession_type_id;
    protected $totals;
    protected $office;
    protected $date_export;

    public function __construct($data, $year, $profession_type_id, $totals, $office)
    {
        $this->data = $data;
        $this->year = $year;
        $this->profession_type_id = $profession_type_id;
        $this->totals = $totals;
        $this->office= $office;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');

    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }


    public function view(): View
    {
        return view('statistiques.filsParentByProfessionType', [
            'data' => $this->data,
            'year' => $this->year,
            'profession_type_id' => $this->profession_type_id,
            'totals' => $this->totals,
            'office' => $this->office,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title

        ]);
    }
}

