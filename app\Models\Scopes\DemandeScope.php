<?php

namespace App\Models\Scopes;

use App\Models\DemandeAnneeEtude;
use App\Models\DemandeType;
use App\Models\DemandeTypeHasRole;
use App\Models\Etablissement;
use App\Models\Office;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;


class DemandeScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $user = Auth::user();
        if($user && !($user->id === 1))
        {
            // Check if the user has an office with a parent_id not null so it's a direction regionale
            if ($user->office && $user->office->parent_id !== null) {
                // get etablissements codes that belong to the user's office
                $codes_etablissements = Etablissement::where('code_dir_reg', $user->office->code)->pluck('code');

                // get demandes ids of demandeAnneeEtude that belong to thse etablissements
//                $demandes_ids = DemandeAnneeEtude::whereIn('code_etab', $codes_etablissements)->max('anneeUniversitaire.title')->pluck('demande_id');

                $ids = DemandeTypeHasRole::whereIn('role_id', $user->roles->pluck('id'))->pluck('demande_type_id');
                $demandes_types_direction_visibility = DemandeType::where('visibility', 'direction')->whereIn('id', $ids)->pluck('id');
//                Log::info(' demandes_types_direction_visibility '.$demandes_types_direction_visibility->implode(','));

                // get only those demandes
                $builder->whereIn('current_code_etab', $codes_etablissements)->whereIn('demande_type_id', $demandes_types_direction_visibility);

            }
            if ($user->office && $user->office->parent_id == null) {
                # get the demandes types visible by the user
                $demandes_types_direction_visibility = DemandeType::all()->pluck('id');
                $builder->whereIn('demande_type_id', $demandes_types_direction_visibility);

            }

            // if user has any of the agent permissions and none of the admin permission regarding demandes
            if($user->hasAnyPermission(['read agent_bourse_universitaire', 'read agent_pret_universitaire', 'read agent_bourse_insertion', 'read agent_bourse_stage','read agent_aide_sociale']) && ! $user->hasAnyPermission(['read admin_bourse_universitaire', 'read admin_pret_universitaire', 'read admin_bourse_insertion', 'read admin_bourse_stage','read admin_aide_sociale'])) // role traitement etat dossier
            {
                $builder->where('etat', '<=', 2); // DOSSIER_EN_ATTENTE / DOSSIER_EN_COURS
            }


        }


    }
}
