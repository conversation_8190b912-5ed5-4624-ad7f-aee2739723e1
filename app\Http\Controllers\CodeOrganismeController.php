<?php

namespace App\Http\Controllers;

use App\Http\Resources\CodeOrganismeResource;
use App\Models\CodeOrganisme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CodeOrganismeController extends Controller
{
    protected $cache_seconds = 900;

        /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Cache::remember('CodeOrganisme', $this->cache_seconds, function () {
            return CodeOrganismeResource::collection(CodeOrganisme::get());
        });
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //code...
        $request->validate([
            'label' => 'required|string',
        ]);

        Cache::forget('CodeOrganisme');

        $code = CodeOrganisme::create([
            'label' => $request->label
        ]);

        return response(new CodeOrganismeResource($code), 201);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\CodeOrganisme  $CodeOrganisme
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
            //code...
            $request->validate([
                'id' => 'required|numeric',
                'label' => 'required|string',
            ]);

        Cache::forget('CodeOrganisme');

            $code = CodeOrganisme::findOrFail($request->id)->update([
                'label' => $request->label,

            ]);

        return response("created" , 201);

    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\CodeOrganisme  $codeOrganisme
     * @return \Illuminate\Http\Response
     */
    public function destroy(CodeOrganisme $codeOrganisme)
    {
        Cache::forget('CodeOrganisme');

        $codeOrganisme->delete();

        return response("", 204);
    }
}
