<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->integer('etat_bourse_stage')->nullable();
            $table->integer('etat_aide_sociale')->nullable();
            $table->text('comment_refus_aide_sociale')->nullable();
            $table->text('comment_refus_bourse_stage')->nullable();
            $table->text('comment_refus_pret')->nullable();
            $table->text('comment_refus_bourse_insertion')->nullable();
            $table->text('comment_refus_bourse')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('etat_bourse_stage');
            $table->dropColumn('etat_aide_sociale');
            $table->dropColumn('comment_refus_aide_sociale');
            $table->dropColumn('comment_refus_bourse_stage');
            $table->dropColumn('comment_refus_pret');
            $table->dropColumn('comment_refus_bourse_insertion');
            $table->dropColumn('comment_refus_bourse');
        });
    }
};
