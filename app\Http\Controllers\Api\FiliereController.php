<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreFiliereRequest;
use App\Http\Requests\UpdateFiliereRequest;
use App\Http\Resources\FiliereResource;
use App\Models\Filiere;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use JetBrains\PhpStorm\Pure;

class FiliereController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Filiere', $this->cache_seconds, function () {
            return FiliereResource::collection(Filiere::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreFiliereRequest $request
     * @return Response
     */
    public function store(StoreFiliereRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Filiere');
        Helpers::clearCacheIdp();

        $g = Filiere::create($data);

        return response(new FiliereResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Filiere $filiere
     * @return FiliereResource
     */
    #[Pure] public function show(Filiere $filiere): FiliereResource
    {
        return new FiliereResource($filiere);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateFiliereRequest $request
     * @param Filiere $filiere
     * @return FiliereResource
     */
    public function edit(UpdateFiliereRequest $request, Filiere $filiere): FiliereResource
    {
        $data = $request->validated();

        Cache::forget('Filiere');
        Helpers::clearCacheIdp();

        $filiere->update($data);

        return new FiliereResource($filiere);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Filiere $filiere
     * @return Response
     */
    public function destroy(Filiere $filiere): Response
    {
        Cache::forget('Filiere');
        Helpers::clearCacheIdp();

        $filiere->delete();

        return response("", 204);
    }
}
