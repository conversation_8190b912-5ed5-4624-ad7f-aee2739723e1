<?php

namespace App\Notifications;

use App\Models\Attestation;
use App\Models\AttestationType;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class AttestationPreteNotification extends Notification
{
    use Queueable;
    private Attestation $attestation;
    private AttestationType $attestationType;


    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Attestation $attestation, AttestationType $attestationType)
    {
        $this->attestation = $attestation;
        $this->attestationType = $attestationType;

        //Log::debug("created new AttestationPreteNotification");
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    /*public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->line('The introduction to the notification.')
                    ->action('Notification Action', url('/'))
                    ->line('Thank you for using our application!');
    }*/

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
            //Log::debug("Sent notification via websocket");

        } catch ( \Exception $e) {
            //Log::debug("cannot send notification via websocket");
        }

        return [
            "title" => "Your certificate is ready",
            "subtitle" => "Your ".$this->attestationType->title." is ready",//n°: ". $this->demande->code,
            "title_fr" => "Votre attestation est prête",
            "subtitle_fr" => "Votre ".$this->attestationType->title_fr." est prête",//de bourse n°: " . $this->demande->code,
            "title_ar" => "شهادتك جاهزة",
            "subtitle_ar" => $this->attestationType->title_ar ." جاهزة " ,//: " . $this->demande->code,
            "avatarIcon" => "confetti",
            "avatarAlt" => "Attestation",
            "avatarText" => "Attestation",
            "avatarColor" => "success",
            "type" => "attestation",
            "target_id" => $this->attestation->id,
            "target" => "attestation",
            "model" => "attestation",
            //"url" => "",
            "url" => "/mes-attestations/"//. $this->attestation->id,

        ];
    }
}
