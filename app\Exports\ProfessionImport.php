<?php
namespace App\Exports;

use App\Models\Profession;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class ProfessionImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'id' => 'required',
                'lib_fr' => 'required',
                'lib_en' => 'required',
                'lib_ar' => 'required',
                'code' => 'required',
                'type_code' => 'required',
            ],
            [],
            [
                'id' => '(ID)',
                'lib_fr' => '(lib_fr)',
                'lib_en' => '(lib_en)',
                'lib_ar' => '(lib_ar)',
                'code' => '(code)',
                'type_code' => '(type_code)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.id' => 'required|integer',
            '*.lib_fr' => 'required|string',
            '*.lib_en' => 'required|string',
            '*.lib_ar' => 'required|string',
            '*.code' => 'required',
            '*.type_code' => 'required',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Profession::where('id', $row['id'])->first();
            if ($gov){
                $gov->update([
                    'code' => $row['code'],
                    'name' => $row['lib_en'],
                    'name_fr' => $row['lib_fr'],
                    'name_ar' => $row['lib_ar'],
                    'type_code' => $row['type_code'],
                ]);
            } else {
                Profession::create([
                    'id'  => $row['id'],
                    'name'  => $row['lib_en'],
                    'name_fr'  => $row['lib_fr'],
                    'name_ar'  => $row['lib_ar'],
                    'code' => $row['code'],
                    'type_code' => $row['type_code'],
                ]);
            }
        }

    }
}
