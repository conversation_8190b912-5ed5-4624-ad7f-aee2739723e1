# Diagramme de la Base de Données

```mermaid
erDiagram
    ANNEE_UNIVERSITAIRES ||--o{ DEMANDES : "possède"
    ANNEE_UNIVERSITAIRES ||--o{ ATTESTATIONS : "possède"
    ANNEE_UNIVERSITAIRES ||--o{ BOURSE_ALTERNANCES : "possède"
    ANNEE_UNIVERSITAIRES ||--o{ MANDATES_FILES : "possède"
    ANNEE_UNIVERSITAIRES ||--o{ DECISIONS_FILES : "possède"
    ANNEE_UNIVERSITAIRES ||--o{ ORDONNANCE_FILES : "possède"
    ANNEE_UNIVERSITAIRES ||--o{ STATS : "possède"
    ANNEE_UNIVERSITAIRES {
        bigint id PK
        string title
        date start
        date end
        int annee_bac
        int smig
    }

    DEMANDES ||--o{ DEMANDE_ANNEE_ETUDES : "possède"
    DEMANDES ||--o{ HISTORIQUES : "possède"

    HISTORIQUES {
        bigint id PK
        string lot
        string situa
        bigint demande_id FK
        timestamp created_at
        timestamp updated_at
        date deleted_at
    }

    DEMANDE_ANNEE_ETUDES {
        bigint id PK
        int demande_id FK
        int user_id FK
        int filiere_id FK
        string code_etab
        string code_diplome
        int annee_etude
        int annee_universitaire_id FK
        int resultat_id FK
        string moyenne
        string credit
        timestamp created_at
        timestamp updated_at
        date deleted_at
    }
    DEMANDES }o--|| DEMANDE_TYPES : "appartient à"
    DEMANDES }o--|| CLASSIFICATIONS : "classifié par"
    DEMANDES {
        bigint id PK
        string code
        string etat
        json config
        bigint user_id
        bigint annee_universitaire_id FK
        bigint demande_type_id FK
        bigint classification_id FK
        string etat_dossier
        string etat_contrat
        string contrat_file
        int etat_bourse
        int etat_bourse_insertion
        int etat_pret
        int etat_bourse_stage
        int etat_aide_sociale
        string lot
    }

    DEMANDE_TYPES ||--o{ CONFIG_DEMANDE_TYPES : "possède"
    DEMANDE_TYPES ||--o{ CLASSIFICATIONS : "possède"
    DEMANDE_TYPES {
        bigint id PK
        date date_dossier_end
        date date_complement_end
        date date_contrat_pret_end
        timestamp created_at
        timestamp updated_at
        date deleted_at
    }

    CLASSIFICATIONS {
        bigint id PK
        string code
        string title
        string title_fr
        string title_ar
        boolean active
        boolean classable
        boolean classable_par_admin
        json config
        bigint parent_id FK
        bigint demande_type_id FK
        boolean document_online
        boolean show_documents
        bigint profession_id FK
        timestamp created_at
        timestamp updated_at
        date deleted_at
    }

    ETABLISSEMENTS ||--o{ ATTESTATIONS : "possède"
    ETABLISSEMENTS ||--o{ BOURSE_ALTERNANCES : "possède"
    ETABLISSEMENTS {
        bigint id PK
        string code
        int code_ministre
        int code_dir_reg
        int annee_universitaire
        string code_office
    }

    ATTESTATIONS }o--|| ATTESTATION_TYPES : "est de type"
    ATTESTATIONS }o--|| STUDENTS : "appartient à"
    ATTESTATIONS {
        bigint id PK
        bigint attestation_types_id FK
        bigint student_id FK
        text detail
        string status
        bigint annee_universitaire_id FK
        bigint etablissement_id FK
        string year
        date date_mandat
        string raison_non_retrait
    }

    ATTESTATION_TYPES ||--o{ ATTEST_TYPE_HAS_DOC_ATTEST : "possède"
    ATTESTATION_TYPES ||--o{ ATTESTATION_TYPE_HAS_OFFICES : "possède"
    ATTESTATION_TYPES {
        bigint id PK
        string code
        string title
        string title_fr
        string title_ar
        bigint parent_id
        string document_file
    }

    STUDENTS ||--o{ DEMANDES : "fait"
    STUDENTS ||--o{ ATTESTATIONS : "possède"
    STUDENTS ||--o{ BOURSE_ALTERNANCES : "possède"

    BOURSE_ALTERNANCES {
        bigint id PK
        bigint annee_universitaire_id FK
        bigint student_id FK
        string nb_mois
        string nb_decision
        string nature_decision
        bigint etablissement_id FK
        string student_name
        string student_cin
        timestamp created_at
        timestamp updated_at
        date deleted_at
    }
    STUDENTS ||--o{ DEMANDE_RECOUVREMENTS : "possède"
    STUDENTS {
        bigint id PK
        string name
        string name_ar
        string firstName
        string role
        string username
        string email
        timestamp email_verified_at
        string password
        boolean status
        string profile_photo
        string num_bac
        int annee_bac
        string cin
        string num_passport
        string matricule
        string phoneNumber
        string address
        string zipCode
        string email_perso
        string code_postal
        string phoneNumber2
        date date_naissance
        string address_naissance
        string type
        string pere
        string mere
        string sex
        int code_gouv
        int nationality_id FK
        int delegation_id FK
        int country_id FK
        int student_from_mes_id FK
        int international_student_id FK
        string remember_token
        timestamp created_at
        timestamp updated_at
        date deleted_at
    }

    MANDATES ||--o{ DEMANDES : "concerne"
    MANDATES {
        bigint id PK
        string date_payement
    }

    MANDATES_FILES }o--|| ANNEE_UNIVERSITAIRES : "appartient à"
    MANDATES_FILES {
        bigint id PK
        string num_dec
        string annee_gestion
        string path
        string date_payment
        string type
        int etat
        bigint annee_universitaire_id FK
    }

    DECISIONS_FILES }o--|| ANNEE_UNIVERSITAIRES : "appartient à"
    DECISIONS_FILES {
        bigint id PK
        string ndec
        string annee_gestion
        string path
        string type
        int etat
        bigint annee_universitaire_id FK
    }

    ORDONNANCE_FILES }o--|| ANNEE_UNIVERSITAIRES : "appartient à"
    ORDONNANCE_FILES {
        bigint id PK
        string num_dec
        string annee_gestion
        string path
        string date_payment
        int etat
        bigint annee_universitaire_id FK
    }

    UPLOADED_FILES {
        bigint id PK
        string type
        string name
        string document_file
        int annee_universitaire
    }

    EXPORTED_FILES {
        bigint id PK
        string type
        string attached_file
        string vue
        string etat
    }

    STATS }o--|| ANNEE_UNIVERSITAIRES : "appartient à"
    STATS {
        bigint id PK
        string num_dec
        string date_payment
        bigint annee_universitaire_id FK
        string tranche
        string nbr_mois
        string title
        string mnt
        string path
    }

    DEMANDE_RECOUVREMENTS }o--|| STUDENTS : "concerne"
    DEMANDE_RECOUVREMENTS {
        bigint id PK
        string type
        bigint student_id FK
        string annee_universitaire
        string status
        int montant
        string commentaire
        string num_quittance
    }

    ADMINS {
        bigint id PK
        string name
        string firstName
        string role
        string username
        string email
        string password
        string profile_photo
        boolean status
    }

    ROLES ||--o{ MODEL_HAS_ROLES : "attribué à"
    ROLES ||--o{ ROLE_HAS_PERMISSIONS : "possède"
    ROLES {
        bigint id PK
        string name
        string guard_name
    }

    PERMISSIONS ||--o{ MODEL_HAS_PERMISSIONS : "attribuée à"
    PERMISSIONS ||--o{ ROLE_HAS_PERMISSIONS : "attribuée à"
    PERMISSIONS {
        bigint id PK
        string name
        string guard_name
    }

    MODEL_HAS_ROLES }o--|| ADMINS : "concerne"
    MODEL_HAS_ROLES {
        bigint role_id FK
        string model_type
        bigint model_id
    }

    MODEL_HAS_PERMISSIONS }o--|| ADMINS : "concerne"
    MODEL_HAS_PERMISSIONS {
        bigint permission_id FK
        string model_type
        bigint model_id
    }

    ROLE_HAS_PERMISSIONS }o--|| ROLES : "concerne"
    ROLE_HAS_PERMISSIONS }o--|| PERMISSIONS : "concerne"
    ROLE_HAS_PERMISSIONS {
        bigint permission_id FK
        bigint role_id FK
    }
```

## Légende

- **PK** : Clé primaire (Primary Key)
- **FK** : Clé étrangère (Foreign Key)
- **||--o{** : Relation "un à plusieurs" (One-to-Many)
- **}o--||** : Relation "plusieurs à un" (Many-to-One)
- **}o--o{** : Relation "plusieurs à plusieurs" (Many-to-Many)

## Notes

Ce diagramme représente les principales tables et relations de la base de données BPAS. En raison de la complexité de la base de données, seules les tables et relations les plus importantes sont représentées.

Les tables principales sont :
- **ANNEE_UNIVERSITAIRES** : Gère les années universitaires
- **DEMANDES** : Stocke les demandes de bourses, prêts et aides sociales
- **ETABLISSEMENTS** : Contient les informations des établissements
- **ATTESTATIONS** : Gère les attestations délivrées aux étudiants
- **STUDENTS** : Stocke les informations des étudiants

Les tables de fichiers incluent :
- **MANDATES_FILES** : Fichiers de mandats
- **DECISIONS_FILES** : Fichiers de décisions
- **ORDONNANCE_FILES** : Fichiers d'ordonnance
- **UPLOADED_FILES** : Fichiers téléchargés
- **EXPORTED_FILES** : Fichiers exportés

Les tables de gestion des permissions comprennent :
- **ADMINS** : Administrateurs du système
- **ROLES** : Rôles des utilisateurs
- **PERMISSIONS** : Permissions du système
- **MODEL_HAS_ROLES** : Association entre modèles et rôles
- **MODEL_HAS_PERMISSIONS** : Association entre modèles et permissions
- **ROLE_HAS_PERMISSIONS** : Association entre rôles et permissions
