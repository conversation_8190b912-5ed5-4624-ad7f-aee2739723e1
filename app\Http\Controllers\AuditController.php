<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\Audit;
use App\Models\ConfigDemandeType;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

class AuditController extends Controller
{
    public function index(Request $request)
    {
        //return response()->json(Audit::latest()->first()->user, 200);

        $audits = Audit::when(
                $request->has('typeId') && $request->typeId != "",
            function ($query) use ($request) {
                return $query->where('auditable_type',  'like', '%\\' . $request->typeId);
            }
        )->when(
                $request->has('auditable_name') && $request->auditable_name != "",
            function ($query) use ($request) {
                return $query->where('auditable_name',  'like', '%' . $request->auditable_name.'%');
            }
        )->when(
            $request->has('name') && $request->name != "",
            function ($query) use ($request) {
                $userIds = Admin::where('name', $request->name)->get()->pluck('id');
                return $query->whereIn('user_id',  $userIds);
            }
        )->when(
            $request->has('userId') && $request->userId != "",
            function ($query) use ($request) {
                //$userIds = Admin::where('id', $request->userId)->get()->pluck('id');
                return $query->where('user_id',  $request->userId);
            }
        )->when(
            $request->has('date') && $request->date != "",
            function ($query) use ($request) {
                $date = Carbon::createFromFormat('Y-m-d', explode('T', $request->date)[0] ) ;

                return $query->whereDate('created_at',$date);
            }
        )->orderBy('created_at', 'desc')
        ->paginate(
            $request->input('perPage') ?? config('constants.pagination'),
        );

        return response()->json($audits, 200);


    }

    public function show()
    {
        $audits = Audit::latest()->get();
        return response()->json($audits);
    }

    public function getTypes(): array
    {
        $allModels = $this->getAllModels();

        return array_values($allModels);
    }

    public function getAllModels(): array
    {
        $ignored = [
            "AttestationTypeHasAttestationTypeDocuments",
            "AttestationTypeHasOffices"
        ];
        $modelList = [];
        $path = app_path() . "/Models";
        $results = scandir($path);

        foreach ($results as $result) {
            if ($result === '.' or $result === '..') continue;
            $filename = $result;

            if (is_dir(app_path() . "/Models/" . $filename)) {
                continue ;//$modelList = array_merge($modelList, getModels($filename));
            }else{
                $modelname = substr($filename,0,-4);

                if(! in_array($modelname, $ignored ))
                {
                    $modelList[] = $modelname;
                }
            }
        }

        return $modelList;
    }

    public function getConfigDemandeTypeAndPrevious($id): array
    {
        $current = ConfigDemandeType::find($id);
        $previous= ConfigDemandeType::find($id-1);

        return [$current, $previous];
    }
}
