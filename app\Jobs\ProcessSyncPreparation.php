<?php

namespace App\Jobs;

use App\Exports\PreparationExport;
use App\Exports\PreparationViewExport;
use App\Models\Admin;
use App\Models\Decision;
use App\Models\Demande;
use App\Models\ExportedFile;
use App\Models\Preparation;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\FastExcel;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Str;
use XLSXWriter;

class ProcessSyncPreparation implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $preparation;

    public function __construct(?Admin $user, ?Preparation $preparation)
    {
        $this->user = $user;
        $this->preparation = $preparation;
    }

    public function handle()
    {

        try {


            $preparation = Preparation::find($this->preparation->id);


            $syncCount = ['decision_favorable' => 0, 'decision_non_favorable' => 0, 'decision_ambigu' => 0];
            if ($preparation->type) {
                $demandes = Demande::where('preparation_' . $preparation->type . '_id', $preparation->id);
                if ($preparation->type === 'bourse') {
                    $demandes = $demandes->where('etat_bourse', '=', Demande::ETAT_BOURSE['SUIVI_PAIEMENT']);
                }
                if ($preparation->type === 'insertion') {
                    $demandes = $demandes->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT']);
                }
                if ($preparation->type === 'pret') {
                    $demandes = $demandes->where('etat_pret', '=', Demande::ETAT_PRET['SUIVI_PAIEMENT']);
                }
                if ($preparation->type === 'aide_sociale') {
                    $demandes = $demandes->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['SUIVI_PAIEMENT']);
                }
                if ($preparation->type === 'stage') {
                    $demandes = $demandes->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['SUIVI_PAIEMENT']);
                }

                if ($demandes->count()) {
                    if ($preparation->type === 'bourse') {
                        $demandes->chunk(9000, function ($dds) use ($preparation, &$syncCount) {
                            foreach ($dds as $demande) {
                                $cin = $demande->user->type === 'tunisien' ? $demande->user->cin : $demande->user->matricule;
                                $decision = Decision::where('cin', $cin)
//                                ->where('lot', $demande->lot)
//                                    ->where('ndec', $preparation->ndec_code)
                                    ->where('type', $preparation->type)
                                    ->where('annee_id', $demande->annee_universitaire_id)->first();
                                if ($decision) {
                                    switch ($decision->situa) {
                                        case 'P':
                                            $demande->etat_bourse = Demande::ETAT_BOURSE['DECISION_FAVORABLE'];
                                            $syncCount['decision_favorable']++;
                                            break;
                                        case '*':
                                            $demande->etat_bourse = Demande::ETAT_BOURSE['DECISION_NON_FAVORABLE'];
                                            $syncCount['decision_non_favorable']++;
                                            break;
                                        default:
                                            $demande->etat_bourse = Demande::ETAT_BOURSE['DECISION_AMBIGU'];
                                            $syncCount['decision_ambigu']++;
                                    }
                                    $demande->natdec_bourse = $decision->natdec;
                                    $demande->save();
                                }
                            }
                        });
                    }
                    if ($preparation->type === 'insertion') {
                        $demandes->chunk(9000, function ($dds) use ($preparation, &$syncCount) {
                            foreach ($dds as $demande) {
                                $cin = $demande->user->type === 'tunisien' ? $demande->user->cin : $demande->user->matricule;
                                $decision = Decision::where('cin', $cin)
//                                  ->where('lot', $demande->lot)
//                                    ->where('ndec', $preparation->ndec_code)
                                  ->where('type', $preparation->type)
                                    ->where('annee_id', $demande->annee_universitaire_id)->first();
                                if ($decision) {
                                    switch ($decision->situa) {
                                        case 'P':
                                            $demande->etat_bourse_insertion = Demande::ETAT_BOURSE_INSERTION['DECISION_FAVORABLE'];
                                            $syncCount['decision_favorable']++;
                                            break;
                                        case '*':
                                            $demande->etat_bourse_insertion = Demande::ETAT_BOURSE_INSERTION['DECISION_NON_FAVORABLE'];
                                            $syncCount['decision_non_favorable']++;
                                            break;
                                        default:
                                            $demande->etat_bourse_insertion = Demande::ETAT_BOURSE_INSERTION['DECISION_AMBIGU'];
                                            $syncCount['decision_ambigu']++;
                                    }
                                    $demande->natdec_insertion = $decision->natdec;
                                    $demande->save();
                                }
                            }
                        });
                    }
                    if ($preparation->type === 'pret') {
                        $demandes->chunk(9000, function ($dds) use ($preparation, &$syncCount) {
                            foreach ($dds as $demande) {
                                $cin = $demande->user->type === 'tunisien' ? $demande->user->cin : $demande->user->matricule;
                                $decision = Decision::where('cin', $cin)
//                                    ->where('lot', $demande->lot)
//                                    ->where('ndec', $preparation->ndec_code)
                                  ->where('type', $preparation->type)
                                    ->where('annee_id', $demande->annee_universitaire_id)->first();
                                if ($decision) {
                                    switch ($decision->situa) {
                                        case 'P':
                                            $demande->etat_pret = Demande::ETAT_PRET['DECISION_FAVORABLE'];
                                            $syncCount['decision_favorable']++;
                                            break;
                                        case '*':
                                            $demande->etat_pret = Demande::ETAT_PRET['DECISION_NON_FAVORABLE'];
                                            $syncCount['decision_non_favorable']++;
                                            break;
                                        default:
                                            $demande->etat_pret = Demande::ETAT_PRET['DECISION_AMBIGU'];
                                            $syncCount['decision_ambigu']++;
                                    }
                                    $demande->natdec_pret = $decision->natdec;
                                    $demande->save();
                                }
                            }
                        });
                    }
                    if ($preparation->type === 'aide_sociale') {
                        $demandes->chunk(9000, function ($dds) use ($preparation, &$syncCount) {
                            foreach ($dds as $demande) {
                                $cin = $demande->user->type === 'tunisien' ? $demande->user->cin : $demande->user->matricule;
                                $decision = Decision::where('cin', $cin)
//                                    ->where('lot', $demande->lot)
//                                    ->where('ndec', $preparation->ndec_code)
                                  ->where('type', $preparation->type)
                                    ->where('annee_id', $demande->annee_universitaire_id)->first();
                                if ($decision) {
                                    switch ($decision->situa) {
                                        case 'P':
                                            $demande->etat_aide_sociale = Demande::ETAT_AIDE_SOCIALE['DECISION_FAVORABLE'];
                                            $syncCount['decision_favorable']++;
                                            break;
                                        case '*':
                                            $demande->etat_aide_sociale = Demande::ETAT_AIDE_SOCIALE['DECISION_NON_FAVORABLE'];
                                            $syncCount['decision_non_favorable']++;
                                            break;
                                        default:
                                            $demande->etat_aide_sociale = Demande::ETAT_AIDE_SOCIALE['DECISION_AMBIGU'];
                                            $syncCount['decision_ambigu']++;
                                    }
                                    $demande->natdec_aide_sociale = $decision->natdec;
                                    $demande->save();
                                }
                            }
                        });
                    }
                    if ($preparation->type === 'stage') {
                        $demandes->chunk(9000, function ($dds) use ($preparation, &$syncCount) {
                            foreach ($dds as $demande) {
                                $cin = $demande->user->type === 'tunisien' ? $demande->user->cin : $demande->user->matricule;
                                $decision = Decision::where('cin', $cin)
//                                    ->where('lot', $demande->lot)
//                                    ->where('ndec', $preparation->ndec_code)
                                  ->where('type', $preparation->type)
                                    ->where('annee_id', $demande->annee_universitaire_id)->first();
                                if ($decision) {
                                    switch ($decision->situa) {
                                        case 'P':
                                            $demande->etat_bourse_stage = Demande::ETAT_BOURSE_STAGE['DECISION_FAVORABLE'];
                                            $syncCount['decision_favorable']++;
                                            break;
                                        case '*':
                                            $demande->etat_bourse_stage = Demande::ETAT_BOURSE_STAGE['DECISION_NON_FAVORABLE'];
                                            $syncCount['decision_non_favorable']++;
                                            break;
                                        default:
                                            $demande->etat_bourse_stage = Demande::ETAT_BOURSE_STAGE['DECISION_AMBIGU'];
                                            $syncCount['decision_ambigu']++;
                                    }
                                    $demande->natdec_stage = $decision->natdec;
                                    $demande->save();
                                }
                            }
                        });
                    }

                    $preparation->etat = Preparation::ETAT['DECISION'];
                    $preparation->save();
                }
            }

            dispatch(new NotifyUserOfCompletedSyncPreparation($this->user, $syncCount,$preparation->ndec_code));

        } catch (\Exception $e) {
            Log::error($e->getFile() . $e->getLine() . $e->getMessage());
        }

    }
}
