<?php

namespace App\Notifications;

use App\Models\Attestation;
use App\Models\AttestationType;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class AttestationRefuseNotification extends Notification
{
    use Queueable;
    private Attestation $attestation;
    private AttestationType $attestationType;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Attestation $attestation, AttestationType $attestationType)
    {
        $this->attestation = $attestation;
        $this->attestationType = $attestationType;
        //Log::debug("created new AttestationPreteNotification");
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    /*public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->line('The introduction to the notification.')
                    ->action('Notification Action', url('/'))
                    ->line('Thank you for using our application!');
    }*/

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
            //Log::debug("Sent notification via websocket");

        } catch ( \Exception $e) {
            //Log::debug("cannot send notification via websocket");
        }

        return [
            "title" => "Your request for certification has been refused",
            "subtitle" => "Your request for ".$this->attestationType->title." has been refused",//n°: ". $this->demande->code,
            "title_fr" => "Votre demande d'attestation a été refusée",
            "subtitle_fr" => "Votre demande de '" . $this->attestationType->title_fr."' a été refusée",//de bourse n°: " . $this->demande->code,
            "title_ar" => "تم رفض طلبك للحصول على شهادة",
            "subtitle_ar" => " تم رفض طلبك للحصول على  ". $this->attestationType->title_ar,//: " . $this->demande->code,
            "avatarIcon" => "traffic-cone",
            "avatarAlt" => "Attestation",
            "avatarText" => "Attestation",
            "avatarColor" => "warning",
            "type" => "attestation",
            "target_id" => $this->attestation->id,
            "target" => "attestation",
            "model" => "attestation",
            //"url" => "",
            "url" => "/mes-attestations/"//. $this->attestation->id,

        ];
    }
}
