<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class ChampPredefini extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    const TYPES = [
        'label',
        'textInput',
        'date',
        'radioBox',
        'checkBox',
        'dropdown',
    ];

    protected $fillable = [
        'label',
        'label_fr',
        'label_ar',
        'name',
        'type',
        'help',
        'help_fr',
        'help_ar',
        'showLabel',
        'required',
        'affectClassification',
        'showInRow',
        'choices',
    ];

    protected $casts = [
        'affectClassification' => 'boolean',
        'required' => 'boolean',
        'showLabel' => 'boolean',
        'showInRow' => 'boolean',
        'choices' => 'json',
    ];

}
