<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('demande_types', function (Blueprint $table) {
            $table->id()->autoIncrement();
            $table->string('code')->nullable();
            $table->string('title');
            $table->string('title_fr');
            $table->string('title_ar');
            $table->boolean('active')->nullable();
            $table->integer('order')->nullable();
            $table->json('config')->nullable();
            $table->json('logic')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();

            $table->timestamps();
            $table->softDeletes();


            $table->foreign('parent_id')
                ->references('id')
                ->on('demande_types') ;
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('demande_types');
    }
};
