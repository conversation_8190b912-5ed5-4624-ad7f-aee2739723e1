<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('ndec');
            $table->string('natdec_bourse')->nullable();
            $table->string('natdec_insertion')->nullable();
            $table->string('natdec_pret')->nullable();
            $table->string('natdec_aide_sociale')->nullable();
            $table->string('natdec_stage')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('ndec')->nullable();
            $table->dropColumn('natdec_bourse');
            $table->dropColumn('natdec_insertion');
            $table->dropColumn('natdec_pret');
            $table->dropColumn('natdec_aide_sociale');
            $table->dropColumn('natdec_stage');

        });
    }
};
