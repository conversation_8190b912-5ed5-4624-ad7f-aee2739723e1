<?php

namespace App\Notifications;

use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class DossierArriveNotification extends Notification
{
    use Queueable;

    private Demande $demande;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande)
    {
        $this->demande = $demande;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Document received.",
            "subtitle" => "Document received for demand n°: ". $this->demande->code,
            "title_fr" => "Dossier reçu pour la demande",
            "subtitle_fr" => "Dossier reçu pour la demande n°: " . $this->demande->code,
            "title_ar" => "تم استلام الملف ",
            "subtitle_ar" => "تم استلام الملف للطلب عدد : " . $this->demande->code,
            "avatarIcon" => "confetti",
            "avatarAlt" => "Dossier",
            "avatarText" => "Dossier",
            "avatarColor" => "info",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
