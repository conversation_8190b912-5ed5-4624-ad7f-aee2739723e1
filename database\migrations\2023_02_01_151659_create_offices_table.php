<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('offices', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->boolean('active')->default(true);
            $table->string('name')->nullable();
            $table->string('name_fr');
            $table->string('name_ar');
            $table->string('adresse')->nullable();
            $table->string('tel')->nullable();
            $table->string('fax')->nullable();
            $table->string('site_web')->nullable();
            $table->string('email')->nullable();
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('offices');
    }
};
