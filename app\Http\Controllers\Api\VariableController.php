<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreVariableRequest;
use App\Http\Requests\UpdateVariableRequest;
use App\Http\Resources\VariableResource;
use App\Models\Variable;
use App\Models\ConfigDemandeType;
use App\Models\DemandeType;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class VariableController extends Controller
{
    protected $cache_seconds = 900;

    public function __construct()
    {
        // $this->middleware('permission:product-create', ['only' => ['create','store']]);
        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Variable', $this->cache_seconds, function () {
            return VariableResource::collection(Variable::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreVariableRequest $request
     * @return Response
     */
    public function store(StoreVariableRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Variable');
        Helpers::clearCacheIdp();

        $variable = Variable::create($data);

        return response(new VariableResource($variable) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateVariableRequest $request
     * @param Variable $variable
     * @return Response
     */
    public function edit(UpdateVariableRequest $request,Variable $variable): Response
    {
        $data = $request->validated();

        Cache::forget('Variable');
        Helpers::clearCacheIdp();

        $variable->update($data);

        return response(new VariableResource($variable) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Variable $variable
     * @return Response
     */
    public function destroy(Variable $variable)
    {
        Cache::forget('Variable');
        Helpers::clearCacheIdp();

        $variable->delete();
        return response("ok", 204);
    }
}
