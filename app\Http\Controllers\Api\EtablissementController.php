<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEtablissementRequest;
use App\Http\Requests\UpdateEtablissementRequest;
use App\Http\Resources\EtablissementResource;
use App\Models\AnneeUniversitaire;
use App\Models\Decision;
use App\Models\Etablissement;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class EtablissementController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Etablissement', $this->cache_seconds, function () {
            return EtablissementResource::collection(Etablissement::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreEtablissementRequest $request
     * @return Response
     */
    public function store(StoreEtablissementRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Etablissement');
        Helpers::clearCacheIdp();

        $etablissement = Etablissement::create($data);
        $etablissement->diplomes()->sync($data['diplomesEtablissements']);

        return response(new EtablissementResource($etablissement) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Etablissement $etablissement
     * @return EtablissementResource
     */
    #[Pure] public function show(Etablissement $etablissement): EtablissementResource
    {
        return new EtablissementResource($etablissement);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateEtablissementRequest $request
     * @param Etablissement $etablissement
     * @return EtablissementResource
     */
    public function edit(UpdateEtablissementRequest $request, Etablissement $etablissement): EtablissementResource
    {
        $data = $request->validated();

        Cache::forget('Etablissement');
        Helpers::clearCacheIdp();

        $etablissement->diplomes()->sync($data['diplomesEtablissements']);
        $etablissement->update($data);

        return new EtablissementResource($etablissement);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Etablissement $etablissement
     * @return Response
     */
    public function destroy(Etablissement $etablissement): Response
    {

        if($etablissement->filieres->count()){
            throw ValidationException::withMessages(["Cet établissement est utilisé par d'autres tables"]);
        }
        Cache::forget('Etablissement');
        Helpers::clearCacheIdp();

        $etablissement->diplomes()->detach();
        $etablissement->delete();

        return response("", 204);
    }

    public function get_boursier_etablissements(Request $request) : JsonResponse
    {
//        Log::info('referer :'.$request->header('referer'));
//        Log::info('Referer :'.$request->header('Referer'));

        try {

            $annee = new AnneeUniversitaire();
//            $anneeUniversitaireCurrent = $annee->setConnection('mysqlx')->where('start', '<=', Carbon::now())->where('end', '>', Carbon::now())->first();
            $anneeUniversitaireCurrent = $annee->setConnection('mysqlx')->where('title', $request->annee_universitaire )->first();
//            $anneeUniversitaireCurrent = AnneeUniversitaire::where('title', $request->annee_universitaire )->first();
//            Log::info($anneeUniversitaireCurrent);
            $etablissement = new Etablissement();
            $etablissement = $etablissement->setConnection('mysqlx')->where('code',$request->etablissementCode)->where('password', $request->password)->first();
//            $etablissement = Etablissement::where('code',$request->etablissementCode)->where('password', $request->password)->first();
            if ($etablissement && $anneeUniversitaireCurrent) {
                $decisions = new Decision();
                $decisions = $decisions->setConnection('mysqlx')
                    ->where('fac', $etablissement->code)
                    ->whereNotIn('type',['aide_sociale', 'insertion'])
                    ->where('annee_id', $anneeUniversitaireCurrent->id)
                    ->get();
                return response()->json([
                    "message" => "success",
                    "etablissement" => $etablissement,
                    "decisions" => $decisions,
                    "anneeUniversitaireCurrent" => $anneeUniversitaireCurrent,
                ], 200);
            }

            return response()->json([
                "message" => "etablissement_introuvable",
                "etablissement" => '',
                "decisions" => '',
                "anneeUniversitaireCurrent" => '',
            ], 200);
        } catch (\Exception $exception) {
            return response()->json([
//                "message" => "etablissement_introuvable",
                "message" => $exception->getMessage(),
                "etablissement" => '',
                "decisions" => '',
                "anneeUniversitaireCurrent" => '',
            ], 500);
        }
    }
}
