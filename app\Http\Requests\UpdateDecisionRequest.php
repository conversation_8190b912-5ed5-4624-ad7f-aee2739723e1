<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDecisionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cin' => 'nullable|string|max:255',
            'catb' => 'nullable|string|max:255',
            'lot' => 'nullable|string|max:255',
            'nom' => 'nullable|string|max:255',
            'datnais' => 'nullable|string|max:255',
            'gouv' => 'nullable|string|max:255',
            'sexe' => 'nullable|string|max:255',
            'profp' => 'nullable|string|max:255',
            'anet' => 'nullable|string|max:255',
            'discip' => 'nullable|string|max:255',
            'fac' => 'nullable|string|max:255',
            'univ' => 'nullable|string|max:255',
            'inf' => 'nullable|string|max:255',
            'sup' => 'nullable|string|max:255',
            'enf' => 'nullable|string|max:255',
            'revp' => 'nullable|string|max:255',
            'revm' => 'nullable|string|max:255',
            'avis' => 'nullable|string|max:255',
            'res' => 'nullable|string|max:255',
            'moy' => 'nullable|string|max:255',
            'natdec' => 'nullable|string|max:255',
            'situa' => 'nullable|string|max:255',
            'mbs' => 'nullable|string|max:255',
            'nmb' => 'nullable|string|max:255',
            'mf' => 'nullable|string|max:255',
            'ndec' => 'nullable|string|max:255',
            'dat' => 'nullable|string|max:255',
            'montanttotal' => 'nullable|string|max:255',
            'pourcentage' => 'nullable|string|max:255',
            'office' => 'nullable|string|max:255'
        ];
    }
}
