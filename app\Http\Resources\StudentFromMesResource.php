<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class StudentFromMesResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'NBAC' => $this->NBAC,
            'CIN' => $this->CIN,
            'NOM_A' => $this->NOM_A,
            'NOM_L' => $this->NOM_L,
            'JJ' => $this->JJ,
            'MM' => $this->MM,
            'AA' => $this->AA,
            'CD_LYC' => $this->CD_LYC,
            'lycee' => $this->lycee,
            'CD_GOUV' => $this->CD_GOUV,
            'gouvernorat' => $this->gouvernorat,
            'SEX' => $this->SEX,
            'PROF' => $this->PROF,
            'profession' => $this->profession,
            'CODE_FILIERE' => $this->CODE_FILIERE,
            'filiere' => $this->filiere,
            'TOUR' => $this->TOUR,
            'annee_bac' => $this->annee_bac,
            'anneeBac' => $this->anneeBac,
            'orientations' => OrientationResource::collection($this->orientations),
            'created_at' => $this->created_at->format('Y-m-d'),
            'date_naissance' => $this->date_naissance?->format('Y-m-d'),
            'date_naissance_format' => $this->date_naissance?->format('d/m/Y'),
        ];
    }
}
