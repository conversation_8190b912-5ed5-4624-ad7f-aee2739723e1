<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAnneeUniversitaireRequest;
use App\Http\Requests\UpdateAnneeUniversitaireRequest;
use App\Http\Resources\AnneeUniversitaireResource;
use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class AnneeUniversitaireController extends Controller
{
    // remember cache for 15 minutes;

    protected $cache_seconds = 900;
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('AnneeUniversitaire', $this->cache_seconds, function () {
            return AnneeUniversitaireResource::collection(AnneeUniversitaire::orderBy('title', 'DESC')->get());
        });

    }
    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(StoreAnneeUniversitaireRequest $request): Response
    {
        Cache::forget('AnneeUniversitaire');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $g = AnneeUniversitaire::create($data);

        return response(new AnneeUniversitaireResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param AnneeUniversitaire $anneeUniversitaire
     * @return AnneeUniversitaireResource
     */
    #[Pure] public function show(AnneeUniversitaire $anneeUniversitaire): AnneeUniversitaireResource
    {
        return new AnneeUniversitaireResource($anneeUniversitaire);
    }
    /**
     * @return Application|ResponseFactory|Response|AnneeUniversitaireResource
     */
    public function currentAnnee(): Application|ResponseFactory|Response|AnneeUniversitaireResource
    {
        $anneeUniversitaire = AnneeUniversitaire::where('start', '<=', Carbon::now())
            ->where('end', '>=', Carbon::now())
            ->first();
        if ($anneeUniversitaire) {
            return new AnneeUniversitaireResource($anneeUniversitaire);
        }
        return response('not found', 201);
    }
    /**
     * @return Application|ResponseFactory|Response|AnneeUniversitaireResource
     */
    public function lastAnnee(): Application|ResponseFactory|Response|AnneeUniversitaireResource
    {
        $anneeUniversitaire = AnneeUniversitaire::where('start', '<=', Carbon::now()->subYear())
            ->where('end', '>=', Carbon::now()->subYear())
            ->first();

        if ($anneeUniversitaire) {
            return new AnneeUniversitaireResource($anneeUniversitaire);
        }
        return response('not found', 201);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param UpdateAnneeUniversitaireRequest $request
     * @param AnneeUniversitaire $anneeUniversitaire
     * @return AnneeUniversitaireResource
     */
    public function edit(UpdateAnneeUniversitaireRequest $request, AnneeUniversitaire $anneeUniversitaire): AnneeUniversitaireResource
    {
        Cache::forget('AnneeUniversitaire');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $anneeUniversitaire->update($data);

        return new AnneeUniversitaireResource($anneeUniversitaire);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param AnneeUniversitaire $anneeUniversitaire
     * @return Response
     */
    public function destroy(AnneeUniversitaire $anneeUniversitaire): Response
    {

        if($anneeUniversitaire->filieres->count()){
            throw ValidationException::withMessages(["Cette anneeUniversitaire est utilisé par d'autres tables"]);
        }

        Cache::forget('AnneeUniversitaire');
        Helpers::clearCacheIdp();

        $anneeUniversitaire->delete();
        return response("", 204);
    }
}
