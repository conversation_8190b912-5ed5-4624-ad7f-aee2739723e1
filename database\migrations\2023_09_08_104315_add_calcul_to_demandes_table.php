<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('type_calcule')->nullable();
            $table->integer('revenu_annuel_pere')->nullable();
            $table->integer('revenu_annuel_mere')->nullable();
            $table->integer('nbr_freres_soeurs')->nullable();
            $table->integer('nbr_freres_soeurs_parraines')->nullable();
            $table->integer('nbr_freres_soeurs_handicapes')->nullable();
            $table->integer('nbr_freres_soeurs_unite')->nullable();
            $table->integer('nbr_freres_soeurs_parraines_unite')->nullable();
            $table->integer('nbr_freres_soeurs_handicapes_unite')->nullable();
            $table->integer('distance')->nullable();
            $table->integer('distance_unite')->nullable();
            $table->integer('revenu_annuel_conjoint')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('type_calcule');
            $table->dropColumn('revenu_annuel_pere');
            $table->dropColumn('revenu_annuel_mere');
            $table->dropColumn('nbr_freres_soeurs');
            $table->dropColumn('nbr_freres_soeurs_parraines');
            $table->dropColumn('nbr_freres_soeurs_handicapes');
            $table->dropColumn('nbr_freres_soeurs_unite');
            $table->dropColumn('nbr_freres_soeurs_parraines_unite');
            $table->dropColumn('nbr_freres_soeurs_handicapes_unite');
            $table->dropColumn('distance');
            $table->dropColumn('distance_unite');
            $table->dropColumn('revenu_annuel_conjoint');

        });
    }
};
