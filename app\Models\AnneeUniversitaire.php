<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class AnneeUniversitaire extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels {
        AuditableTrait::transformAudit as parentTransformAudit;
    }
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    protected $fillable = [
        'active',
        'title',
        'smig',
        'start',
        'end',
        'annee_bac',
        'order',
    ];

    protected $casts = [
        'active' => 'boolean',
        'start' => 'date',
        'end' => 'date',
    ];

    public function filieres() : HasMany
    {
        return $this->hasMany(Filiere::class,'annee_universitaire','id');
    }

    public function anneeBac() : BelongsTo
    {
        return $this->belongsTo(AnneeBac::class,"annee_bac","id");
    }
}
