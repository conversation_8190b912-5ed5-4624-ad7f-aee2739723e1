<?php

namespace App\Exports;

use App\Models\Demande;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Str;

class PreparationMapExport implements FromQuery, WithCustomChunkSize, WithMapping, ShouldQueue, WithHeadings, WithCustomQuerySize
{
    use Exportable;

    private mixed $id;
    private mixed $type;

    public function __construct($id,$type)
    {
        $this->id = $id;
        $this->type = $type;
    }


    public function headings(): array
    {
        return [

            'CIN',
            'CATB',
            'LOT',
            'NOM',
            'DAT_NAISS',
            'GOUV_N',
            'SEXE',
            'PROFP',
            'ANET',
            'DIS',
            'FAC',
            'UNIV',
            'REVP',
            'REVM',
            'AVIS',
            'RES',
            'MOY',
            'DECIS',

        ];
    }

    public function query()
    {
        $type = $this->type;
        if ( $type ) {
            $preparation_type = '';
            switch ($type) {
                case 'bourse':
                    $preparation_type = 'preparation_bourse_id';
                    break;
                case 'insertion':
                    $preparation_type = 'preparation_insertion_id';
                    break;
                case 'pret':
                    $preparation_type = 'preparation_pret_id';
                    break;
                case 'aide_sociale':
                    $preparation_type = 'preparation_aide_sociale_id';
                    break;
                case 'stage':
                    $preparation_type = 'preparation_stage_id';
                    break;
            }
            if ($preparation_type) {
                return Demande::query()->where($preparation_type, $this->id)
                    ->with(['user', 'demandeType', 'anneeUniversitaire', 'professionFinal'])
                    ->without('classificationFinal', 'classification', );
            }
            return null;
        }
        return null;
    }


    public function map($row): array
    {
        $decisTypeObj = ['bourse' => 'B', 'insertion' => 'B', 'pret' => 'P', 'aide_sociale' => 'A', 'stage' => 'S'];

        return [$row->user ? $row->user?->type === 'tunisien' ? $row->user?->cin : $row->user?->matricule : '',
         $row->code_catb ?? '',
         $row->lot,
         $row->user ? $row->user->name . ' ' . ($row->user->firstName ? ' ' . $row->user->firstName : '') : '',
         $row->user ? $row->user->date_naissance ? Carbon::parse($row->user->date_naissance)->format('dmy') : '' : '',
         $row->user ? $row->user->code_gouv : '',
         $row->user ? $row->user->sex : '',
         $row->professionFinal->code,
         $row->demande_last_annee_etude->annee_etude,
         $row->demande_last_annee_etude->code_diplome,
         $row->demande_last_annee_etude->code_etab,
         '6',
         $row->revenu_net > 0 ? Str::padLeft($row->revenu_net, 5, '0'): '00000',
         '00000',
         $decisTypeObj[$this->type],
         $row->demandeType->group === 'nouveau_bachelier' ? '1' : ($row->demande_dernier_annee_etude?->resultat_success ? '1' : '0'),
         $row->demande_dernier_annee_etude?->moyenne ?? '',
         'A'];
    }
    public function chunkSize(): int
    {
        return 500;
    }
    public function querySize(): int
    {
        $type = $this->type;
        if ( $type ) {
            $preparation_type = '';
            switch ($type) {
                case 'bourse':
                    $preparation_type = 'preparation_bourse_id';
                    break;
                case 'insertion':
                    $preparation_type = 'preparation_insertion_id';
                    break;
                case 'pret':
                    $preparation_type = 'preparation_pret_id';
                    break;
                case 'aide_sociale':
                    $preparation_type = 'preparation_aide_sociale_id';
                    break;
                case 'stage':
                    $preparation_type = 'preparation_stage_id';
                    break;
            }
            if ($preparation_type) {
                return Demande::query()->where($preparation_type, $this->id)->count();
            }
            return 0;
        }
        return 0;
    }
}
