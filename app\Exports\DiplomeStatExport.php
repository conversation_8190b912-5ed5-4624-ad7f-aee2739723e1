<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class DiplomeStatExport implements FromView, WithEvents
{
    //protected $bourse;
    //protected $pret;
    //protected $aide_sociale;
    //protected $insertion;
    protected $data;
    protected $office;
    protected $year;
    protected $date_export;

    public function __construct($data, $office, $year /* $bourse, $pret, $aide_sociale, $insertion*/)
    {
        //$this->bourse = $bourse;
        //$this->pret = $pret;
        //$this->aide_sociale = $aide_sociale;
        //$this->insertion = $insertion;
        $this->data = $data;
        $this->office = $office;
        $this->year = $year;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');


    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }

    public function view(): View
    {
        return view('statistiques.statDiplome', [
            //'bourse' => $this->bourse,
            //'pret' => $this->pret,
            //'aide_sociale' => $this->aide_sociale,
            //'insertion' => $this->insertion,
            'data' => $this->data,
            'office' => $this->office,
            'year' => $this->year,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title

        ]);
    }

}

