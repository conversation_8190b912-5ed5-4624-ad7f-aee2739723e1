<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class WaitingUser extends Model implements Auditable
{

    use Sortable;
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.secondConnection'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.secondConnection');
    }

    protected $fillable = [
        'firstName',
        'name',
        'email',
        'password',
        'cin',
        'phoneNumber',
        'address',
        'delegation_id',
        'country_id',
        'username',
        'role',
        'profile_photo',
        'phoneNumber2',
        'annee_bac',
        'num_bac',
        'matricule',
        'num_passport',
        'code_postal',
        'email_perso',
        'date_naissance',
        'type',
        'sex',
        'pere',
        'mere',
        'code_postal',
        'email_perso',
        'code_gouv',

    ];


    public function etudiantAnneeUniversitaires(): HasMany
    {
        return $this->setConnection(config('database.secondConnection'))->hasMany(EtudiantAnneeUniversitaire::class, 'waiting_user_id', 'id');
    }

    public function anneeBac(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(AnneeBac::class, 'annee_bac', 'id');
    }


    public function country(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Country::class);
    }

    public function nationality(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Country::class, 'nationality_id', 'id');
    }

    public function delegation(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Delegation::class);
    }

    public function gouvernorat(): BelongsTo
    {
        return $this->setConnection(config('database.default'))->belongsTo(Gouvernorat::class, 'code_gouv', 'code');
    }

}
