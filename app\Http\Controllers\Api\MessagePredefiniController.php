<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMessagePredefiniRequest;
use App\Http\Requests\UpdateMessagePredefiniRequest;
use App\Http\Resources\MessagePredefiniResource;
use App\Models\DemandeType;
use App\Models\MessagePredefini;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use App\Models\Admin;
use JetBrains\PhpStorm\Pure;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rules\Password;

class MessagePredefiniController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('MessagePredefini', $this->cache_seconds, function () {
            return MessagePredefiniResource::collection(MessagePredefini::all());
        });

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreMessagePredefiniRequest $request
     * @return Response
     */
    public function store(StoreMessagePredefiniRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('MessagePredefini');
        Helpers::clearCacheIdp();

        $g = MessagePredefini::create($data);

        return response(new MessagePredefiniResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param MessagePredefini $messagePredefini
     * @return MessagePredefiniResource
     */
    #[Pure] public function show(MessagePredefini $messagePredefini): MessagePredefiniResource
    {
        return new MessagePredefiniResource($messagePredefini);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateMessagePredefiniRequest $request
     * @param MessagePredefini $messagePredefini
     * @return MessagePredefiniResource
     */
    public function edit(UpdateMessagePredefiniRequest $request, MessagePredefini $messagePredefini): MessagePredefiniResource
    {
        $data = $request->validated();

        Cache::forget('MessagePredefini');
        Helpers::clearCacheIdp();

        $messagePredefini->update($data);

        return new MessagePredefiniResource($messagePredefini);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param MessagePredefini $messagePredefini
     * @return Response
     */
    public function destroy(MessagePredefini $messagePredefini): Response
    {
        Cache::forget('MessagePredefini');
        Helpers::clearCacheIdp();

        $messagePredefini->delete();

        return response("", 204);
    }

}
