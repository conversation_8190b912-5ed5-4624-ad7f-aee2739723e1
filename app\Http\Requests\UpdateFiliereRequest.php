<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateFiliereRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'code_etab' => 'required',
            'code_diplome' => 'required|string',
//            'code' => 'required|integer|unique:filieres,code,'.$this->id,
            'code' => [
                'required',
                Rule::unique('filieres', 'code')->where('annee_universitaire', $this->input('annee_universitaire'))->where('id', !$this->id),
            ],
            'annee_universitaire' => 'required|integer',
        ];
    }


}
