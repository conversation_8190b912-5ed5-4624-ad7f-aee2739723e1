<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->index('preparation_bourse_id','preparation_bourse_id_index');
            $table->index('preparation_insertion_id','preparation_insertion_id_index');
            $table->index('preparation_pret_id','preparation_pret_id_index');
            $table->index('preparation_aide_sociale_id','preparation_aide_sociale_id_index');
            $table->index('preparation_stage_id','preparation_stage_id_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropIndex('preparation_bourse_id');
            $table->dropIndex('preparation_insertion_id');
            $table->dropIndex('preparation_pret_id');
            $table->dropIndex('preparation_aide_sociale_id');
            $table->dropIndex('preparation_stage_id');

        });
    }
};
