<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->index('lot','lot_demandes_index');
        });
        Schema::table('historiques', function (Blueprint $table) {
            $table->index('lot','lot_historiques_index');
            $table->index('situa','situa_index');
        });
        Schema::table('mandates', function (Blueprint $table) {
            $table->index('date_payement','date_payement_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropIndex('lot_demandes_index' );
        });
        Schema::table('historiques', function (Blueprint $table) {
            $table->dropIndex('lot_historiques_index');
            $table->dropIndex('situa_index');
        });
        Schema::table('mandates', function (Blueprint $table) {
            $table->dropIndex('date_payement_index');
        });
    }
};
