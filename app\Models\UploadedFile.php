<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class UploadedFile extends Model implements Auditable
//    implements HasMedia
{
//    use InteractsWithMedia;
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'type',
        'name',
        'document_file',
        'annee_universitaire',
    ];

    public $appends=[
        'document_file_url',
    ];

    public function getDocumentFileUrlAttribute(){
        if ( $this->document_file ) {
            return asset('uploads/uploaded_files/'.$this->type.'/'.$this->document_file);
        }
        return null;
    }
}
