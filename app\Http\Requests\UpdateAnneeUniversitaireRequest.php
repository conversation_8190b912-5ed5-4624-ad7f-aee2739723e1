<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateAnneeUniversitaireRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => 'required|string',
            'smig' => 'required|numeric',
            'start' => 'nullable|date_format:Y-m-d',
            'end' => 'nullable|date_format:Y-m-d',
            'active' => 'required|boolean',
            'annee_bac' => 'required|integer',
        ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }
}
