<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demande_recouvrements', function (Blueprint $table) {
            $table->date('date_paiement')->nullable();
            $table->string('raison_refus')->nullable();
            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demande_recouvrements', function (Blueprint $table) {
            $table->dropColumn('date_paiement');
            $table->dropColumn('raison_refus');
        });
    }
};
