<?php

namespace App\Jobs;

use App\Exports\PreparationExport;
use App\Exports\PreparationViewExport;
use App\Models\Admin;
use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use App\Models\AttestationType;
use App\Models\Demande;
use App\Models\DemandeType;
use App\Models\ExportedFile;
use App\Models\Preparation;
use App\Models\Resultat;
use App\Models\RetraitInscription;
use App\Models\Universite;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\FastExcel;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Str;
use XLSXWriter;

class ProcessDemandeExport implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    private mixed $request;

    public function __construct(?Admin $user, $request)
    {
        $this->user = $user;
        $this->request = $request;
    }

    function demandesGenerator()
    {
        $request = $this->request;
        if ($request['demande_type_code'] === 'bureau_ordre') {
            $demandes = Demande::where(function ($query) {
                $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                    ->orWhere(function ($query) {
                        $query->where('etat', Demande::ETAT['DOSSIER_EN_COURS'])
                            ->where('etat_dossier', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                            ->where('etat_complement', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                            ->whereNotNull('etat_dossier');
                    })
                    ->orWhere(function ($query) {
                        $query->where('etat', Demande::ETAT['PRISE_DE_DECISION'])
                            ->where('etat_contrat', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE']);
                    });
            })->orderBy('id', 'desc');
            $type = null;
            $typeIds = [];
            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
                $demandes = $demandes->whereIn('demande_type_id', $typeIds);
            }

            $demandes = $demandes->when(
                $request['annee_universitaire_id'],
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request['annee_universitaire_id']);
                }
            );
            if ($request['status']) {
                $demandes = $demandes->where('etat', '=', (int)$request['status']);
            }

        }
        elseif ($request['demande_type_code'] === 'guichet') {
            $type = null;

            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }

            $demandes = Demande::orderBy('id', 'desc');
            $typeIds = [];
            if ($type) {
                $typeIds[] = $type->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
                $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');
            }

            if ($request['status']) {
                $pieces = explode("---", $request['status']);
                $group = $pieces[0];
                $key = $pieces[1];
                //status status_dossier status_complement status_contrat status_bourse status_bourse_insertion status_pret status_bourse_stage status_aide_sociale
                if($group === 'status'){
                    $demandes = $demandes->where('etat', '=', $key);
                }
                if($group === 'status_dossier'){
                    $demandes = $demandes->where('etat_dossier', '=', $key);
                }
                if($group === 'status_complement'){
                    $demandes = $demandes->where('etat_complement', '=', $key);
                }
                if($group === 'status_contrat'){
                    $demandes = $demandes->where('etat_contrat', '=', $key);
                }
                if($group === 'status_bourse'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_bourse',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_bourse', '=', $key);
                    }
                }
                if($group === 'status_bourse_insertion'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_bourse_insertion',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_bourse_insertion', '=', $key);
                    }
                }
                if($group === 'status_pret'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_pret',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_pret', '=', $key);
                    }
                }
                if($group === 'status_bourse_stage'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_bourse_stage',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_bourse_stage', '=', $key);
                    }
                }
                if($group === 'status_aide_sociale'){
                    if($key === '4'){
                        $demandes = $demandes->whereIn('etat_aide_sociale',[ 4, 5, 6, 7 ]);
                    } else {
                        $demandes = $demandes->where('etat_aide_sociale', '=', $key);
                    }
                }
            }
            if ($request['annee_id']) {
                $demandes = $demandes->where('annee_universitaire_id', $request['annee_id']);
            }
            if ($request['cin'] || $request['nom']) {
                $demandes = $demandes->where(function (Builder $query) use ($request) {
                    if ($request['cin']) {
                        $query->orWhereHas( 'user' , function (Builder $query) use ($request) {
                            $query->where('cin', 'like', '%' . $request['cin'] . '%');
                        })
                            ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                                $query->where('num_passport', 'like', '%' . $request['cin'] . '%');
                            });
                    }
                    if ($request['nom']) {
                        $query->orWhereHas( 'user' , function (Builder $query) use ($request) {
                            $query->where('name', 'like', '%' . $request['nom'] . '%');
                        })
                            ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                                $query->where('name_ar', 'like', '%' . $request['nom'] . '%');
                            })
                            ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                                $query->where('firstName', 'like', '%' . $request['nom'] . '%');
                            });
                    }
                });
            }
        }
        elseif ($request['demande_type_code'] === 'preparation_paiement') {
            $type = null;
            $typeIds = [];
            if ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret') {
                $type = DemandeType::where('code', 'bourses_universitaires')->first();
                if ($request['type_preparation'] === 'bourse') {
                    $types = DemandeType::whereIn('code', ['int_nv','int_ce', 'int_rnv', 'int_mstr','int_doc'])->pluck('id')->toArray();
                    foreach ( $types as $typeId) {
                        $typeIds[] = $typeId;
                    }
                }
            }
            if ($request['type_preparation'] === 'aide_sociale') {
                $type = DemandeType::where('code', 'aide_sociale')->first();
            }
            if ($request['type_preparation'] === 'stage') {
                $type = DemandeType::where('code', 'bourses_de_stage')->first();
            }
            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');

            if ($request['annee_universitaire_id']) {
                $demandes = $demandes->where('annee_universitaire_id', $request['annee_universitaire_id']);
            }

            if (isset($request['attestation_status']) && $request['attestation_status'] && $request['attestation_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['attestation_status']) && $request['attestation_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['attestation_pret_status']) && $request['attestation_pret_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['attestation_pret_status']) && $request['attestation_pret_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if (isset($request['retrait_status']) && $request['retrait_status'] === "true") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersRetrait);
            }
            if (isset($request['retrait_status']) && $request['retrait_status'] === "false") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersRetrait);
            }

            if (isset($request['type_preparation']) && ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret')) {
                $demandes = $demandes->whereNot(function ($query) {
                    $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                        ->orWhere(function ($query) {
                            $query->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                                ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                                ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                                ->whereNotNull('etat_dossier');
                        })->orWhere(function ($query) {
                            $query->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                                ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                                ->whereNotNull('etat_contrat');
                        });
                });
            }

            $demandes = $demandes->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION']);

            if (isset($request['type_preparation']) && $request['type_preparation']) {
                if ($request['type_preparation'] === 'bourse') {
                    $demandes = $demandes->where('etat_bourse', '=', Demande::ETAT_BOURSE['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'insertion') {
                    $demandes = $demandes->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'pret') {
                    $demandes = $demandes->where('etat_pret', '=', Demande::ETAT_PRET['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'aide_sociale') {
                    $demandes = $demandes->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['ELIGIBLE']);
                }
                if ($request['type_preparation'] === 'stage') {
                    $demandes = $demandes->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['ELIGIBLE']);
                }

            }

            if (isset($request['revenu_net_min']) && $request['revenu_net_min'] != '') {
                $demandes = $demandes->where('revenu_net', '>=', $request['revenu_net_min']);
            }
            if (isset($request['revenu_net_max']) && $request['revenu_net_max'] != '') {
                $demandes = $demandes->where('revenu_net', '<=', $request['revenu_net_max']);
            }

//            if ($request['status']) {
//                $demandes = $demandes->where('etat', '=', $request['status']);
//            }

            $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType');
        }
        elseif ($request['demande_type_code'] === 'suivi_preparation_paiement') {
            $type = null;
            $typeIds = [];
            if ($request['type_preparation'] && ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret')) {
                $type = DemandeType::where('code', 'bourses_universitaires')->first();
                if ($request['type_preparation'] === 'bourse') {
                    $types = DemandeType::whereIn('code', ['int_nv','int_ce', 'int_rnv', 'int_mstr','int_doc'])->pluck('id')->toArray();
                    foreach ( $types as $typeId) {
                        $typeIds[] = $typeId;
                    }
                }
            }
            if ($request['type_preparation'] && $request['type_preparation'] === 'aide_sociale') {
                $type = DemandeType::where('code', 'aide_sociale')->first();
            }
            if ($request['type_preparation'] && $request['type_preparation'] === 'stage') {
                $type = DemandeType::where('code', 'bourses_de_stage')->first();
            }
            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');

            if ($request['attestation_status'] && $request['attestation_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if ($request['attestation_status'] &&  $request['attestation_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_boursier',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if ($request['attestation_pret_status'] && $request['attestation_pret_status'] === "true") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
            }
            if ($request['attestation_pret_status'] &&  $request['attestation_pret_status'] === "false") {
                $attestationTypesNonBoursier = AttestationType::where('non_pret',true)->pluck('id');
                $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
                if ($request['annee_universitaire_id']) {
                    $annee = AnneeUniversitaire::find($request['annee_universitaire_id']);
                    $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status','annulee');
                }

                $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
            }
            if ($request['retrait_status'] && $request['retrait_status'] === "true") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereIn('user_id', $usersRetrait);
            }
            if ($request['retrait_status'] && $request['retrait_status'] === "false") {
                $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request['annee_universitaire_id'])->pluck('student_id');
                $demandes = $demandes->whereNotIn('user_id', $usersRetrait);
            }


            if ($request['type_preparation'] && ($request['type_preparation'] === 'bourse' || $request['type_preparation'] === 'insertion' || $request['type_preparation'] === 'pret')) {
                $demandes = $demandes->whereNot(function ($query) {
                    $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                        ->orWhere(function ($query) {
                            $query->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                                ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                                ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                                ->whereNotNull('etat_dossier');
                        })->orWhere(function ($query) {
                            $query->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                                ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                                ->whereNotNull('etat_contrat');
                        });
                });
            }

            $demandes = $demandes->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION']);

            if ($request['type_preparation']) {
                if ($request['type_preparation'] === 'bourse') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_bourse', '=', Demande::ETAT_BOURSE['SUIVI_PAIEMENT'])
                            ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_FAVORABLE'])
                            ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_AMBIGU']);
                    })
                        ->where('preparation_bourse_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'insertion') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'])
                            ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_FAVORABLE'])
                            ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_AMBIGU']);
                    })
                        ->where('preparation_insertion_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'pret') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_pret', '=', Demande::ETAT_PRET['SUIVI_PAIEMENT'])
                            ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_FAVORABLE'])
                            ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_AMBIGU']);
                    })
                        ->where('preparation_pret_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'aide_sociale') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['SUIVI_PAIEMENT'])
                            ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_FAVORABLE'])
                            ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_AMBIGU']);
                    })
                        ->where('preparation_aide_sociale_id', $request['preparation_id']);
                }
                if ($request['type_preparation'] === 'stage') {
                    $demandes = $demandes->where(function ($q) {
                        $q->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['SUIVI_PAIEMENT'])
                            ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_FAVORABLE'])
                            ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_NON_FAVORABLE'])
                            ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_AMBIGU']);
                    })
                        ->where('preparation_stage_id', $request['preparation_id']);
                }

            }

            if ($request['revenu_net_min'] && $request['revenu_net_min'] != '') {
                $demandes = $demandes->where('revenu_net', '>=', $request['revenu_net_min']);
            }
            if ($request['revenu_net_max'] && $request['revenu_net_max'] != '') {
                $demandes = $demandes->where('revenu_net', '<=', $request['revenu_net_max']);
            }

            $demandes = $demandes->when(
                $request['annee_universitaire_id'],
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request['annee_universitaire_id']);
                }
            );

            $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType');
        }
        else {
            $type = DemandeType::where('code', $request['demande_type_code'])->first();

            if ($request['demande_type_id']) {
                $type = DemandeType::find($request['demande_type_id']);
            }

            $typeIds = [];
            if ($request['demande_type_code'] === 'bourses_universitaires') {
                $types = DemandeType::whereIn('code', ['int_nv','int_ce', 'int_rnv', 'int_mstr','int_doc'])->pluck('id')->toArray();
                foreach ( $types as $typeId) {
                    $typeIds[] = $typeId;
                }
            }
            if ($type) {
                $typeIds[] = $type?->id;
                if ($type?->fils) {
                    foreach ($type->fils as $child) {
                        $typeIds[] = $child->id;
                        if ($child->fils) {
                            foreach ($child->fils as $childd) {
                                $typeIds[] = $childd->id;
                                if ($childd->fils) {
                                    foreach ($childd->fils as $childdd) {
                                        $typeIds[] = $childdd->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
//            return response()->json($type);
            $demandes = Demande::whereIn('demande_type_id', $typeIds)->orderBy('id', 'desc');

            // test refus
            if ($request['refus'] === 'refus') {
                $demandes = $demandes->where('etat', '=', Demande::ETAT['REFUS_PAIEMENT'])->orWhere('etat', '=', Demande::ETAT['DOSSIER_REFUS']);
            }
            else {
                $demandes = $demandes
                    ->whereNot(function ($query) {
                        $query
                            ->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
                            ->orWhere(function ($query) {
                                $query
                                    ->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                                    ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                                    ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                                    ->whereNotNull('etat_dossier');
                            })
                            ->orWhere(function ($query) {
                                $query
                                    ->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                                    ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                                    ->whereNotNull('etat_contrat');
                            });
                    })
                    ->where('etat', '<', Demande::ETAT['PREPARATION_PAIEMENT']);
            }

            // test is_pret
            if ($request['pret'] === 'pret') {
                $demandes = $demandes->where('is_pret', true);
            }
            else {
                $demandes = $demandes->where('is_pret' ,false);
            }
            $demandes = $demandes->when(
                $request['annee_universitaire_id'],
                function ($query) use ($request) {
                    return $query->where('annee_universitaire_id', $request['annee_universitaire_id']);
                }
            );
            if ($request['status']) {
                $demandes = $demandes->where('etat', '=', $request['status']);
            }
        }

        /** Adding Filter query */
        if (isset($request['q']) && $request['q']) {
            $demandes = $demandes->where(function (Builder $query) use ($request) {

                $query->orWhere('code','like', '%' . $request['q'] . '%')
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('name', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('name_ar', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('firstName', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('email', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('cin', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('num_passport', 'like', '%' . $request['q'] . '%');
                    })
                    ->orWhereHas( 'user' , function (Builder $query) use ($request) {
                        $query->where('cin', 'like', '%' . $request['q'] . '%');
                    });
            });
        }
        /** Adding Filter by dates */
        if (isset($request['dates']) &&  count($request['dates'])) {
            $start = Carbon::createFromFormat('d/m/Y', $request['dates'][0]) ;
            $end = Carbon::createFromFormat('d/m/Y', $request['dates'][1]);
            $demandes = $demandes->whereDate('created_at', '>=', $start)->whereDate('created_at', '<=', $end);
        }
        $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType', 'demandeAnneeEtudes');

        foreach ($demandes->cursor() as $d) {
            yield $d;
        }
    }

    public function handle()
    {
        try {
            $request = $this->request;
            $roww = ExportedFile::create([
                'type' => $request['demande_type_code'],
                'vue' => 0,
                'etat' => 'en_cours',
            ]);
            $results = Resultat::all();
            $univs = Universite::all();
            (new FastExcel($this->demandesGenerator()))->export(
                storage::path('uploads/exportedFile/demande_' .  $roww->type . '_' . $roww->id . '.xlsx'),
                function ($demande) use ($results,$univs) {
                    $config = $demande->config;
                    $configArray = json_decode($config, true);
                    $etat_civil = '';
                    foreach ($configArray as $key => $value) {
                        if (strpos($key, 'etat_civil') !== false) {
                            $etat_civil = $value;
                            break;
                        }
                    }
                    $res = $results->where('id', $demande->demande_dernier_annee_etude?->resultat_id)->first();
                    $univ = $univs->where('code', $demande->demande_last_annee_etude?->etablissement?->code_univ)->first();
                    return [
                        'nom' => $demande->user?->name ?? '',
                        'nom_ar' => $demande->user?->name_ar ?? '',
                        'cin'=> $demande->user ? $demande->user?->type === 'tunisien' ? $demande->user?->cin : $demande->user?->matricule : '',
                        'telephone' => $demande->user?->phoneNumber ?? '',
                        'annee_universitaire' => $demande->anneeUniversitaire->title,
                        'demandeType' => $demande->demandeType->title_fr,
                        'classification' => $demande->classification?->title_fr ?? '',
                        'classification_final' => $demande->classificationFinal?->title_fr ?? '',
                        'profession' => $demande->professionFinal?->name_fr ?? '',
                        'code' => $demande->code,
                        'code_catb' => $demande->code_catb,
                        'code_decision' => $demande->code_decision,
                        'etat' => Demande::ETAT_STRING[$demande->etat],
                        'etat_dossier' => $demande->etat_dossier,
                        'etat_complement' => $demande->etat_complement,
                        'lot' => $demande->lot,
                        'DAT_NAISS' => $demande->user?->date_naissance ? Carbon::parse($demande->user->date_naissance)->format('dmy') : '' ,
                        'GOUV_N' => $demande->user?->gouvernorat?->name_fr ?? '' ,
                        'NAT' => $demande->user?->type ?? '' ,
                        'SEX' => $demande->user?->sex ?? '' ,
                        'ETAT_CV' => $etat_civil ,
                        'PROFESSION' => $demande->professionFinal?->name ?? '',
                        'ANET' => $demande->demande_last_annee_etude?->annee_etude ?? '',
                        'DIS' => $demande->demande_last_annee_etude?->code_diplome ?? '',
                        'FAC' => $demande->demande_last_annee_etude?->etablissement?->name ?? '',
                        'RES' => $res?->name ?? '',
                        'MOY' => $demande->demande_dernier_annee_etude?->moyenne ?? '',
                        'Universite' => $univ?->name ?? '',
                        'nbr_freres' => $demande->nbr_freres_soeurs_parraines ?? '',
                        'nbr_freres_etudiants' => $demande->nbr_freres_soeurs ?? '',
                        'nbr_freres_handicapes' => $demande->nbr_freres_soeurs_handicapes ?? '',
                        'DISTANCE' => $demande->distance ?? '',
                        'REV TOTAL NET' => $demande->revenu_net ?? '',
                        'REV Pere' => $demande->revenu_annuel_pere ?? '',
                        'REV Mere' => $demande->revenu_annuel_mere ?? '',
                        'REV Conjoint' => $demande->revenu_annuel_conjoint ?? '',
                        'Annee_bac' => $demande->user?->anneeBac?->title ?? '',
                    ];
                });

            dispatch(new NotifyUserOfCompletedExport($this->user, $roww->type, $roww->id));

        } catch (\Exception $e) {
            Log::error($e->getFile() .' --- '. $e->getLine() .' --- '. $e->getMessage());
        }

    }
}
