<?php

namespace App\Exports;

use App\Models\Reclamation;
use App\Models\Rectificatif;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ReclamationExport implements FromCollection,WithHeadings
{

    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function headings():array{
        return[

            'cin',
            'nom_ar',
            'nom_fr',
            'reclamation_type',
            'detail',
            'response',
            'etat',
        ];
    }

    public function collection()
    {
        $request = $this->request;
        $reclamations =  Reclamation::with('student','reclamationType')->get();
        if(isset($request->q) && isset($request->q)){
            $reclamations = Reclamation::where('detail', 'like', '%' . $request->q . '%')
                ->with('student','reclamationType')->get();
        }


        return $reclamations->map( fn (Reclamation $reclamation) => [
            'cin' => $reclamation->student?->cin ?? '',
            'nom_ar' => $reclamation->student?->name_ar ?? '',
            'nom_fr' => $reclamation->student?->name ?? '',
            'reclamation_type' => $reclamation->reclamationType?->title_fr ?  $reclamation->reclamationType?->title_fr.' / '.$reclamation->reclamationType?->title_ar  : '',
            'detail' => $reclamation->detail ?? '',
            'response' => $reclamation->response ?? '',
            'etat' => $reclamation->etat ?? '',
        ]);
    }
}
