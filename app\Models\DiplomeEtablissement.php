<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class DiplomeEtablissement extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'code_diplome',
        'code_etab',
    ];

    public $timestamps = false;

    public function etablissement(): BelongsTo
    {
        return $this->belongsTo(Etablissement::class,  'code_etab','code');
    }

    public function diplome(): BelongsTo
    {
        return $this->belongsTo(Diplome::class,  'code_diplome','code');
    }
}
