<?php

namespace App\Http\Controllers;

use App\Http\Resources\FicheOrganismeResource;
use App\Models\FicheOrganisme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class FicheOrganismeController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Cache::remember('FicheOrganisme', $this->cache_seconds, function () {
            return FicheOrganismeResource::collection(FicheOrganisme::with("annee_universitaire")->with('code_organisme')->get());
        });
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'organisme' => 'required|string',
            'code_organisme_id' => 'required|numeric',
            'type' => 'required|string',
            'date_emission' => 'required|date',
            'nb_total' => 'required|numeric',
            'montant_total' => 'required|numeric',
            'tax_total' => 'required|numeric',
            'premier' => 'required|numeric',
            'dernier' => 'required|numeric',
            'montant_ttc' => 'required|numeric',
            //'fichier' => 'required|file',
            'annee_universitaire_id' => 'required|numeric',
            'num_decision' => 'required|string',
            //'office' => 'required|string',
        ]);

        Cache::forget('FicheOrganisme');


        if ($request->hasFile('fichier')) {
            $file = $request->file('fichier');
            $file_name = 'ficheOrganisme_' . time() . '.' . $request->file('fichier')->extension();

            // Use putFileAs to store the file with a specific name
            Storage::putFileAs('ficheOrganisme/', $file, $file_name);
        }


        $ficheOrganisme = FicheOrganisme::create([
            'organisme' => $request->organisme,
            'code_organisme_id' => $request->code_organisme_id,
            'type' => $request->type,
            'date_emission' => $request->date_emission,
            'nb_total' => $request->nb_total,
            'montant_total' => $request->montant_total,
            'tax_total' => $request->tax_total,
            'premier' => $request->premier,
            'dernier' => $request->dernier,
            'montant_ttc' => $request->montant_ttc,
            'fichier' => isset($file_name) ? $file_name : '',
            'annee_universitaire_id' => $request->annee_universitaire_id,
            'num_decision' => $request->num_decision,
            'office' => 'C',

        ]);



        return response(new FicheOrganismeResource($ficheOrganisme), 201);

    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FicheOrganisme  $ficheOrganisme
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $request->validate([
            'id' =>'required|numeric',
            'organisme' => 'required|string',
            'code_organisme_id' => 'required|numeric',
            'type' => 'required|string',
            'date_emission' => 'required|date',
            'nb_total' => 'required|numeric',
            'montant_total' => 'required|numeric',
            'tax_total' => 'required|numeric',
            'premier' => 'required|numeric',
            'dernier' => 'required|numeric',
            'montant_ttc' => 'required|numeric',
            //'fichier' => 'required|string',
            'annee_universitaire_id' => 'required|numeric',
            'num_decision' => 'required|string',
            //'office' => 'required|string',

        ]);

        Cache::forget('FicheOrganisme');

        $ficheOrganisme = FicheOrganisme::findOrFail($request->id)->update([
            'organisme' => $request->organisme,
            'code_organisme_id' => $request->code_organisme_id,
            'type' => $request->type,
            'date_emission' => $request->date_emission,
            'nb_total' => $request->nb_total,
            'montant_total' => $request->montant_total,
            'tax_total' => $request->tax_total,
            'premier' => $request->premier,
            'dernier' => $request->dernier,
            'montant_ttc' => $request->montant_ttc,
            //'fichier' => $request->fichier,
            'annee_universitaire_id' => $request->annee_universitaire_id,
            'num_decision' => $request->num_decision,
            'office' => 'C',

        ]);

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FicheOrganisme  $ficheOrganisme
     * @return \Illuminate\Http\Response
     */
    public function destroy(FicheOrganisme $ficheOrganisme)
    {
        Cache::forget('FicheOrganisme');

        $ficheOrganisme->delete();

        return response("", 204);
    }
}
