<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class EtudiantAnneeUniversitairesResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'user_id'=> $this->user_id,
            'waiting_user_id'=> $this->waiting_user_id,
            'filiere_id'=> $this->filiere_id,
            'code_etab'=> $this->code_etab,
            'code_diplome'=> $this->code_diplome,
            'annee_etude'=> $this->annee_etude,
            'annee_universitaire_id'=> $this->annee_universitaire_id,
            'resultat_id'=> $this->resultat_id,
            'moyenne'=> $this->moyenne,
            'credit'=> $this->credit,
//            'code_univ'=> $this->code_univ,

//            'created_at' => $this->created_at->format('Y-m-d'),

            'diplome' => $this->diplome,
            'etablissement' => $this->etablissement,
//            'filiere' => $this->filiere,
            'anneeUniversitaire' => $this->anneeUniversitaire,

        ];
    }
}
