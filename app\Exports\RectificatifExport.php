<?php

namespace App\Exports;

use App\Models\Rectificatif;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RectificatifExport implements FromCollection,WithHeadings
{

    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function headings():array{
        return[
            'DECISION RECTIFICATIF', 'ANNEE UNIVERSITAIRE', 'id','rectificatif_numero_id', 'cin', 'catb', 'lot', 'nom', 'datnais', 'gouv', 'sexe', 'profp', 'anet', 'discip',
            'fac', 'univ', 'inf', 'sup', 'enf', 'revp', 'revm', 'avis', 'res', 'moy', 'natdec',
            'situa', 'mbs', 'nmb', 'mf', 'ndec', 'dat', 'montanttotal', 'pourcentage', 'office',
            'type', 'annee_id', 'created_at', 'updated_at'
        ];
    } 

    public function collection()
    {
        $request = $this->request;

        if(isset($request->year) && isset($request->type)){
            return Rectificatif::join('rectificatif_numeros', 'rectificatifs.rectificatif_numero_id', '=','rectificatif_numeros.id')
                ->join('annee_universitaires', 'rectificatifs.annee_id', '=','annee_universitaires.id')
                ->where('annee_id', $request->year)
                ->where('type', $request->type)
                ->select('rectificatif_numeros.num_decision','annee_universitaires.title','rectificatifs.*')
                ->get();
        } 
        elseif (isset($request->year)) {
            return Rectificatif::join('rectificatif_numeros', 'rectificatifs.rectificatif_numero_id', '=','rectificatif_numeros.id')
                ->join('annee_universitaires', 'rectificatifs.annee_id', '=','annee_universitaires.id')
                ->where('annee_id', $request->year)
                ->select('rectificatif_numeros.num_decision','annee_universitaires.title','rectificatifs.*')
                ->get();
        }
        elseif (isset($request->type)) {
            return Rectificatif::join('rectificatif_numeros', 'rectificatifs.rectificatif_numero_id', '=','rectificatif_numeros.id')
                ->join('annee_universitaires', 'rectificatifs.annee_id', '=','annee_universitaires.id')
                ->where('type', $request->type)
                ->select('rectificatif_numeros.num_decision','annee_universitaires.title','rectificatifs.*')
                ->get();
        }
        else {
            return Rectificatif::join('rectificatif_numeros', 'rectificatifs.rectificatif_numero_id', '=','rectificatif_numeros.id')
                ->join('annee_universitaires', 'rectificatifs.annee_id', '=','annee_universitaires.id')
                ->select('rectificatif_numeros.num_decision','annee_universitaires.title','rectificatifs.*')
                ->get();
        }
    }
}
