<?php

namespace App\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

trait AuditableRelatedModels
{
    public function transformAuditRelatedModels(array $data): array
    {
        foreach ($this->getForeignKeyAttributes() as $foreignKey => $relationConfig) {
            try {
                //code...
                if (Arr::has($data, 'new_values.' . $foreignKey)) {
                    $relationName = key($relationConfig);
                    $attribute = $relationConfig[$relationName];

//                    Log::info('RelationConfig :');
//                    Log::info($relationConfig);

                    if (isset($relationConfig['pivot']) && $relationConfig['pivot']) {
//                        Log::info('Has Pivot');
                        // Auditing for pivot table (many-to-many relationship)
                        $pivotModels = $this->$relationName()->get();
                        $oldModels = $this->getPivotRelatedModels($pivotModels, 'pivot', $attribute);
                        $newModels = $this->getPivotRelatedModels($this->$relationName, 'pivot', $attribute);

                        if ($newModels) {
                            $data['new_values'][$relationName . '_' . $attribute] = $newModels;
                        }
                        if ($oldModels) {
                            $data['old_values'][$relationName . '_' . $attribute] = $oldModels;
                        }
                    } else {
//                        Log::info('No Pivot');
//                        Log::info($foreignKey);
//                        Log::info($relationName);
                        // Auditing for regular related model (belongsTo, hasOne, hasMany, etc.)
                        $oldModel = $this->getRelatedModel($this->getOriginal($foreignKey), $relationName);
                        $newModel = $this->getRelatedModel($this->getAttribute($foreignKey), $relationName);

                        if ($newModel) { // create or update
                            $data['new_values'][$relationName . '_' . $attribute] = $newModel->$attribute;
                        }
                        if ($oldModel) { // delete or update
                            $data['old_values'][$relationName . '_' . $attribute] = $oldModel->$attribute;
                        }
                    }
                }
            } catch (\Throwable $th) {
                Log::error('Auditing failed');
            }

        }

        return $data;
    }

    public function logPivotChanges($relation, $oldIds, $newIds, $relationName, $displayColumn)
    {
        $added = array_diff($newIds, $oldIds);
        $removed = array_diff($oldIds, $newIds);

        if (!empty($added) || !empty($removed)) {

//            Log::debug("setting audit data");
            $auditData = [
                'old_values' => [],
                'new_values' => [],
                'event' => 'updated',
                'auditable_id' => $this->id,
                'auditable_type' => get_class($this),
                'tags' => [],
                'auditable_name' => $this->name ?? $this->title ?? $this->label ?? $this->code ?? $this->num_decision ?? 'none'

            ];

            if (!empty($added)) {
                $auditData['new_values'][$relationName] = json_encode($relation::find($added)->pluck($displayColumn)->toArray());
            }

            if (!empty($removed)) {
                $auditData['old_values'][$relationName] = json_encode($relation::find($removed)->pluck($displayColumn)->toArray());
            }

            $this->audits()->create($auditData);
        }
    }


    protected function getForeignKeyAttributes(): array
    {
        return [
            'etablissement_id' => ['etablissement' => 'name'],
            'annee_universitaire_id' => ['annee_universitaire' => 'title'],
            'student_id' => ['student' => 'name'],
            'office_id' => ['office' => 'name'],
            'annee_bac' => ['anneeBac' => 'title'],
            'attestation_types_id' => ['attestationType' => 'title'],
            'documents_attestations_id' => ['documentsAttestations' => 'title'],

            'code_organisme_id' => ['code_organisme' => 'title'],
            'parent_id' => ['parent' => 'title'],
            'demande_type_id' => ['demandeType' =>'title'],
            'profession_id' => ['profession' =>'name'],
            'user_id' =>  ['user' => 'name'],
            'filiere_id' =>  ['filiere' => 'name'],
            'resultat_id' =>  ['resultat' => 'name'],
            'demande_id' =>  ['demande' => 'code'],
            'role_id' =>  ['role' => 'name'],
            'classification_id' =>  ['classification' => 'name'],
            'waiting_user_id' =>  ['waitingUser' => 'name'],
            'group_id' => ['statGroup' => 'title'],
            'nationality_id' => ['nationality' => 'name'],
            'reclamation_type_id' => ['reclamationType' => 'title'],
            'annee_id' => ['annee_universitaire' => 'title'],
            'rectificatif_numero_id' => ['rectificatif_numero' => 'num_decision'],
            'motif_retrait_inscription_id' => ['motif_retrait_inscription' => 'label'],
            'country_id' => ['country' => 'name'],
            'delegation_id' => ['delegation' => 'name'],



            //'offices' => ['offices' => ['relation' => 'offices', 'display' => 'name', 'pivot' => true]],
            //'documentsAttestations' => ['documentsAttestations' => ['relation' => 'documentsAttestations', 'display' => 'title', 'pivot' => true]],
            //'diplomes' => ['diplomes' => ['relation' => 'diplomes', 'display' => 'name', 'pivot' => true]],


            // Add other foreign keys and their corresponding relation names here
        ];
    }

    protected function getRelatedModel($id, $relationName)
    {
        $relationMethod = $this->$relationName();
        $relatedModel = $relationMethod->getRelated();

        return $relatedModel::find($id);
    }

    protected function getPivotRelatedModels($models, $relationName, $attribute, $pivotKey = null)
    {
        return $models->map(function ($model) use ($attribute, $pivotKey, $relationName) {
            if ($pivotKey && isset($model->$pivotKey)) {
                return $model->$pivotKey->$attribute;
            }
            return $model->$relationName->$attribute;
        })->toArray();
    }


}
