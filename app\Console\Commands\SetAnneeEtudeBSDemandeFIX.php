<?php

namespace App\Console\Commands;

use App\Models\AnneeUniversitaire;
use App\Models\Demande;
use App\Models\DemandeAnneeEtude;
use Illuminate\Console\Command;

class SetAnneeEtudeBSDemandeFIX extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'demande:fix-annee-etude-bs';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Set etudiant_annee_universitaire_id for all demandes';

  /**
   * Execute the console command.
   */
  public function handle()
  {
    $this->info('Starting to update demandes...');

    $count = 0;

    try {
      Demande::chunk(500, function ($demandes) use (&$count) {
        foreach ($demandes as $demande) {
          try {
            if ($demande->demandeType->code !== 'bourses_de_stage') {
              continue;
            }

            $demande->etudiant_annee_universitaire_id = $demande->demande_last_annee_etude->id;
            $demande->save();
            $count++;
          } catch (\Exception $e) {
            $message = "Error processing demande ID {$demande->id} etudiant {$demande->user->name}( {$demande->user->cin} ) etat {$demande->etat} : {$e->getMessage()}";
            $demandeAnneeEtude = DemandeAnneeEtude::with('etablissement', 'anneeUniversitaire')
              ->where('demande_id', $demande->id)
              ->orderBy(
                AnneeUniversitaire::select('title')
                  ->from('annee_universitaires')
                  ->whereColumn('annee_universitaires.id', 'demande_annee_etudes.annee_universitaire_id')
                , 'desc')
              ->skip(1)->take(1)->first();
            if ($demandeAnneeEtude) {
              $demandeAnneeEtude->annee_universitaire_id = $demande->annee_universitaire_id;
              $demandeAnneeEtude->save();
              $demande->etudiant_annee_universitaire_id = $demandeAnneeEtude->id;
              $demande->save();
            }

            $this->error($message); // Console output for command
            \Log::error($message); // Laravel log
            continue; // Continue with next record
          }
        }
      });
    } catch (\Exception $e) {
      $message = "Error in chunk processing: {$e->getMessage()}";
      $this->error($message); // Console output for command
      \Log::error($message); // Laravel log
    }

    $this->info("Completed! Updated {$count} demandes.");
  }
}
