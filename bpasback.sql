-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : localhost
-- <PERSON><PERSON><PERSON><PERSON> le : jeu. 17 avr. 2025 à 13:26
-- Version du serveur : 10.11.3-MariaDB-log
-- Version de PHP : 8.3.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `bpasback`
--

-- --------------------------------------------------------

--
-- Structure de la table `absences`
--

CREATE TABLE `absences` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `student_id` bigint(20) UNSIGNED DEFAULT NULL,
  `etablissement_id` bigint(20) UNSIGNED DEFAULT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED DEFAULT NULL,
  `nb_jours` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `student_name` varchar(255) DEFAULT NULL,
  `student_cin` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `admins`
--

CREATE TABLE `admins` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `firstName` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `office_id` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `annee_bacs`
--

CREATE TABLE `annee_bacs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `annee_universitaires`
--

CREATE TABLE `annee_universitaires` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `start` date NOT NULL,
  `end` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `annee_bac` int(11) NOT NULL DEFAULT 1,
  `order` int(11) DEFAULT NULL,
  `smig` int(11) NOT NULL DEFAULT 5512,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `attestations`
--

CREATE TABLE `attestations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `attestation_types_id` bigint(20) UNSIGNED NOT NULL,
  `student_id` bigint(20) UNSIGNED NOT NULL,
  `detail` text DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED DEFAULT NULL,
  `etablissement_id` bigint(20) UNSIGNED DEFAULT NULL,
  `year` varchar(255) DEFAULT NULL,
  `date_enregistrement_bureau_ordre` date DEFAULT NULL,
  `reference_bureau_ordre` varchar(255) DEFAULT NULL,
  `langue` varchar(255) NOT NULL DEFAULT 'francais',
  `office_id` bigint(20) UNSIGNED DEFAULT NULL,
  `response_date` varchar(255) NOT NULL DEFAULT '',
  `montant` double(8,2) DEFAULT NULL,
  `date_mandat` date DEFAULT NULL,
  `raison_non_retrait` varchar(255) DEFAULT NULL,
  `num_emission` bigint(20) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `attestation_types`
--

CREATE TABLE `attestation_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `document_file` varchar(255) DEFAULT NULL,
  `langues` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `non_boursier` tinyint(1) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `filigrane` varchar(50) DEFAULT NULL,
  `non_pret` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `attestation_type_has_offices`
--

CREATE TABLE `attestation_type_has_offices` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `office_id` bigint(20) UNSIGNED DEFAULT NULL,
  `attestation_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `attest_type_has_doc_attest`
--

CREATE TABLE `attest_type_has_doc_attest` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `attestation_types_id` bigint(20) UNSIGNED DEFAULT NULL,
  `documents_attestations_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `audits`
--

CREATE TABLE `audits` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_type` varchar(255) DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `event` varchar(255) NOT NULL,
  `auditable_type` varchar(255) NOT NULL,
  `auditable_id` bigint(20) UNSIGNED NOT NULL,
  `old_values` text DEFAULT NULL,
  `new_values` text DEFAULT NULL,
  `url` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(1023) DEFAULT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `auditable_name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `bourse_alternances`
--

CREATE TABLE `bourse_alternances` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `student_id` bigint(20) UNSIGNED DEFAULT NULL,
  `nb_mois` varchar(255) DEFAULT NULL,
  `nb_decision` varchar(255) DEFAULT NULL,
  `nature_decision` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `etablissement_id` bigint(20) UNSIGNED DEFAULT NULL,
  `student_name` varchar(255) DEFAULT NULL,
  `student_cin` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `catbs`
--

CREATE TABLE `catbs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `tunisien` tinyint(1) DEFAULT NULL,
  `troisieme_cycle` tinyint(1) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `type_etude` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `champ_predefinis`
--

CREATE TABLE `champ_predefinis` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `label_fr` varchar(255) DEFAULT NULL,
  `label_ar` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `help` varchar(255) DEFAULT NULL,
  `help_fr` varchar(255) DEFAULT NULL,
  `help_ar` varchar(255) DEFAULT NULL,
  `showLabel` tinyint(1) DEFAULT NULL,
  `required` tinyint(1) DEFAULT NULL,
  `affectClassification` tinyint(1) DEFAULT NULL,
  `choices` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `showInRow` tinyint(1) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `classifications`
--

CREATE TABLE `classifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `classable` tinyint(1) DEFAULT NULL,
  `classable_par_admin` tinyint(1) DEFAULT NULL,
  `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
  `demande_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `document_online` tinyint(1) DEFAULT NULL,
  `show_documents` tinyint(1) DEFAULT NULL,
  `profession_id` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `code_organismes`
--

CREATE TABLE `code_organismes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `label` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `config_demande_types`
--

CREATE TABLE `config_demande_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `logic` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `demande_type_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `countries`
--

CREATE TABLE `countries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `code_mes` int(11) NOT NULL DEFAULT 1,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `decisions_files`
--

CREATE TABLE `decisions_files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ndec` varchar(255) NOT NULL,
  `annee_gestion` varchar(255) NOT NULL,
  `path` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `etat` int(11) NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `delegations`
--

CREATE TABLE `delegations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `gouvernorat_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `demandes`
--

CREATE TABLE `demandes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `etat` varchar(255) NOT NULL,
  `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `user_id` bigint(20) NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED DEFAULT NULL,
  `demande_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `classification_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `etat_document` int(11) DEFAULT NULL,
  `etudiant_annee_universitaire_id` int(11) DEFAULT NULL,
  `config_demande_type_id` int(11) DEFAULT NULL,
  `etat_dossier` varchar(255) DEFAULT NULL,
  `comment_incomplete` text DEFAULT NULL,
  `comment_incoherent` text DEFAULT NULL,
  `type_calcule` varchar(255) DEFAULT NULL,
  `revenu_annuel_pere` int(11) DEFAULT NULL,
  `revenu_annuel_mere` int(11) DEFAULT NULL,
  `nbr_freres_soeurs` int(11) DEFAULT NULL,
  `nbr_freres_soeurs_parraines` int(11) DEFAULT NULL,
  `nbr_freres_soeurs_handicapes` int(11) DEFAULT NULL,
  `nbr_freres_soeurs_unite` int(11) DEFAULT NULL,
  `nbr_freres_soeurs_parraines_unite` int(11) DEFAULT NULL,
  `nbr_freres_soeurs_handicapes_unite` int(11) DEFAULT NULL,
  `distance` int(11) DEFAULT NULL,
  `distance_unite` int(11) DEFAULT NULL,
  `revenu_annuel_conjoint` int(11) DEFAULT NULL,
  `etat_bourse` int(11) DEFAULT NULL,
  `etat_bourse_insertion` int(11) DEFAULT NULL,
  `etat_pret` int(11) DEFAULT NULL,
  `nbr_notif_incoherent` int(11) DEFAULT NULL,
  `nbr_notif_incomplete` int(11) DEFAULT NULL,
  `control_fiscal` int(11) DEFAULT NULL,
  `nbr_notif_control_fiscal` int(11) DEFAULT NULL,
  `contrat_file` varchar(255) DEFAULT NULL,
  `etat_complement` varchar(255) DEFAULT NULL,
  `contrat_end_date` date DEFAULT NULL,
  `etat_contrat` varchar(255) DEFAULT NULL,
  `comment_contrat_incomplet` text DEFAULT NULL,
  `control_fiscal_date` date DEFAULT NULL,
  `contrat_montant` int(11) DEFAULT NULL,
  `code_catb` varchar(255) DEFAULT NULL,
  `code_decision` bigint(20) DEFAULT NULL,
  `classification_final_id` int(11) DEFAULT NULL,
  `etat_bourse_stage` int(11) DEFAULT NULL,
  `etat_aide_sociale` int(11) DEFAULT NULL,
  `comment_refus_aide_sociale` text DEFAULT NULL,
  `comment_refus_bourse_stage` text DEFAULT NULL,
  `comment_refus_pret` text DEFAULT NULL,
  `comment_refus_bourse_insertion` text DEFAULT NULL,
  `comment_refus_bourse` text DEFAULT NULL,
  `bs_nb_jour` int(11) DEFAULT NULL,
  `bs_start` date DEFAULT NULL,
  `bs_end` date DEFAULT NULL,
  `preparation_bourse_id` int(11) DEFAULT NULL,
  `preparation_insertion_id` int(11) DEFAULT NULL,
  `preparation_pret_id` int(11) DEFAULT NULL,
  `preparation_aide_sociale_id` int(11) DEFAULT NULL,
  `preparation_stage_id` int(11) DEFAULT NULL,
  `revenu_net` int(11) DEFAULT NULL,
  `lot` varchar(255) DEFAULT NULL,
  `natdec_bourse` varchar(255) DEFAULT NULL,
  `natdec_insertion` varchar(255) DEFAULT NULL,
  `natdec_pret` varchar(255) DEFAULT NULL,
  `natdec_aide_sociale` varchar(255) DEFAULT NULL,
  `natdec_stage` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `type_score` varchar(255) DEFAULT NULL,
  `situation_etudiant` varchar(255) DEFAULT NULL,
  `situation_familiale_dece` varchar(255) DEFAULT NULL,
  `situation_familiale_divorce` varchar(255) DEFAULT NULL,
  `situation_familiale_handicap` varchar(255) DEFAULT NULL,
  `compare_resultat` varchar(255) DEFAULT NULL,
  `score_total` varchar(255) DEFAULT NULL,
  `profession_final_id` int(11) DEFAULT NULL,
  `is_bourse` tinyint(1) NOT NULL DEFAULT 0,
  `is_bourse_insertion` tinyint(1) NOT NULL DEFAULT 0,
  `is_pret` tinyint(1) NOT NULL DEFAULT 0,
  `is_bourse_stage` tinyint(1) NOT NULL DEFAULT 0,
  `is_aide_sociale` tinyint(1) NOT NULL DEFAULT 0,
  `comment_refus` text DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `current_code_etab` varchar(255) DEFAULT NULL,
  `num_bordereau` varchar(255) DEFAULT NULL,
  `centre_control_fiscal` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `demande_annee_etudes`
--

CREATE TABLE `demande_annee_etudes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `demande_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `filiere_id` int(11) DEFAULT NULL,
  `code_etab` varchar(255) DEFAULT NULL,
  `code_diplome` varchar(255) DEFAULT NULL,
  `annee_etude` int(11) DEFAULT NULL,
  `annee_universitaire_id` int(11) DEFAULT NULL,
  `resultat_id` int(11) DEFAULT NULL,
  `moyenne` varchar(255) DEFAULT NULL,
  `credit` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `is_nouveau_bachelier` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `demande_recouvrements`
--

CREATE TABLE `demande_recouvrements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `student_id` bigint(20) UNSIGNED NOT NULL,
  `annee_universitaire` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'en_cours',
  `montant` int(11) NOT NULL DEFAULT 0,
  `commentaire` varchar(255) DEFAULT NULL,
  `num_quittance` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `date_paiement` date DEFAULT NULL,
  `raison_refus` varchar(255) DEFAULT NULL,
  `recette_finance` varchar(255) DEFAULT NULL,
  `quittance` varchar(255) DEFAULT NULL,
  `decision` varchar(255) DEFAULT NULL,
  `date_generation` date DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `demande_types`
--

CREATE TABLE `demande_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `title_fr` varchar(255) NOT NULL,
  `title_ar` varchar(255) NOT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `start` date DEFAULT NULL,
  `end` date DEFAULT NULL,
  `bourse_insertion` tinyint(1) DEFAULT NULL,
  `pret` tinyint(1) DEFAULT NULL,
  `date_dossier_end` date DEFAULT NULL,
  `date_complement_end` date DEFAULT NULL,
  `date_contrat_pret_end` date DEFAULT NULL,
  `group` varchar(255) DEFAULT NULL,
  `bourse` tinyint(1) DEFAULT NULL,
  `aide_sociale` tinyint(1) DEFAULT NULL,
  `bourses_de_stage` tinyint(1) DEFAULT NULL,
  `visibility` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `is_parent_etranger` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `demande_type_has_roles`
--

CREATE TABLE `demande_type_has_roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `demande_type_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `diplomes`
--

CREATE TABLE `diplomes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `nbr_annee_etude` varchar(255) DEFAULT NULL,
  `cycle` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `troisieme_cycle` tinyint(1) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `diplome_demande_types`
--

CREATE TABLE `diplome_demande_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `demande_type_id` varchar(255) NOT NULL,
  `code_diplome` varchar(255) NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `diplome_etablissements`
--

CREATE TABLE `diplome_etablissements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code_etab` varchar(255) NOT NULL,
  `code_diplome` varchar(255) NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `distances`
--

CREATE TABLE `distances` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code_gouv1` int(11) NOT NULL,
  `code_gouv2` int(11) NOT NULL,
  `distance` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `documents_attestations`
--

CREATE TABLE `documents_attestations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `documents_attestation_uploads`
--

CREATE TABLE `documents_attestation_uploads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `attached_file` varchar(255) DEFAULT NULL,
  `etat` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `attestation_id` bigint(20) UNSIGNED DEFAULT NULL,
  `document_attestation_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `document_classifications`
--

CREATE TABLE `document_classifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `classification_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `obligatoire` tinyint(1) DEFAULT NULL,
  `boursier` varchar(255) DEFAULT NULL,
  `resultat` int(11) DEFAULT NULL,
  `document_file` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `document_classification_demandes`
--

CREATE TABLE `document_classification_demandes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `document_file` varchar(255) DEFAULT NULL,
  `etat` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `document_classification_id` bigint(20) UNSIGNED DEFAULT NULL,
  `demande_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `attached_file` varchar(255) DEFAULT NULL,
  `dans_complement` tinyint(1) NOT NULL DEFAULT 0,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `document_predefinis`
--

CREATE TABLE `document_predefinis` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `boursier` varchar(255) DEFAULT NULL,
  `resultat` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `etablissements`
--

CREATE TABLE `etablissements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `code_univ` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `code_ministre` int(11) DEFAULT NULL,
  `code_dir_reg` varchar(255) DEFAULT NULL,
  `code_office` varchar(255) DEFAULT NULL,
  `password` varchar(255) NOT NULL DEFAULT 'password',
  `deleted_at` date DEFAULT NULL,
  `code_gouv` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `exported_files`
--

CREATE TABLE `exported_files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `attached_file` varchar(255) DEFAULT NULL,
  `vue` varchar(255) DEFAULT NULL,
  `etat` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `fiche_organismes`
--

CREATE TABLE `fiche_organismes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `organisme` varchar(255) NOT NULL,
  `code_organisme_id` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  `date_emission` date NOT NULL,
  `nb_total` bigint(20) NOT NULL,
  `montant_total` double(12,3) NOT NULL,
  `tax_total` double(12,3) NOT NULL,
  `premier` bigint(20) NOT NULL,
  `dernier` bigint(20) NOT NULL,
  `montant_ttc` double(12,3) NOT NULL,
  `fichier` varchar(255) DEFAULT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `num_decision` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL,
  `office` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `filieres`
--

CREATE TABLE `filieres` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` int(11) NOT NULL,
  `code_etab` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `annee_universitaire` int(11) DEFAULT NULL,
  `code_diplome` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `gouvernorats`
--

CREATE TABLE `gouvernorats` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `nbr` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `group_conditions`
--

CREATE TABLE `group_conditions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `discip` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`discip`)),
  `anet` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`anet`)),
  `group_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `historiques`
--

CREATE TABLE `historiques` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `cin` varchar(255) DEFAULT NULL,
  `catb` varchar(255) DEFAULT NULL,
  `lot` varchar(255) DEFAULT NULL,
  `nom` varchar(255) DEFAULT NULL,
  `datnais` varchar(255) DEFAULT NULL,
  `gouv` varchar(255) DEFAULT NULL,
  `sexe` varchar(255) DEFAULT NULL,
  `profp` varchar(255) DEFAULT NULL,
  `anet` varchar(255) DEFAULT NULL,
  `discip` varchar(255) DEFAULT NULL,
  `fac` varchar(255) DEFAULT NULL,
  `univ` varchar(255) DEFAULT NULL,
  `inf` varchar(255) DEFAULT NULL,
  `sup` varchar(255) DEFAULT NULL,
  `enf` varchar(255) DEFAULT NULL,
  `revp` varchar(255) DEFAULT NULL,
  `revm` varchar(255) DEFAULT NULL,
  `avis` varchar(255) DEFAULT NULL,
  `res` varchar(255) DEFAULT NULL,
  `moy` varchar(255) DEFAULT NULL,
  `natdec` varchar(255) DEFAULT NULL,
  `situa` varchar(255) DEFAULT NULL,
  `mbs` varchar(255) DEFAULT NULL,
  `nmb` varchar(255) DEFAULT NULL,
  `mf` varchar(255) DEFAULT NULL,
  `ndec` varchar(255) DEFAULT NULL,
  `dat` varchar(255) DEFAULT NULL,
  `montanttotal` varchar(255) DEFAULT NULL,
  `montant` varchar(50) DEFAULT NULL,
  `pourcentage` varchar(255) DEFAULT NULL,
  `office` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `annee_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `international_students`
--

CREATE TABLE `international_students` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `num_passport` varchar(255) NOT NULL,
  `matricule` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `phoneNumber` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `zipCode` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `placeOfBirth` varchar(255) DEFAULT NULL,
  `sex` varchar(255) DEFAULT NULL,
  `nationality_id` varchar(255) DEFAULT NULL,
  `code_etab` varchar(255) DEFAULT NULL,
  `filiere_id` int(11) DEFAULT NULL,
  `foyer` varchar(255) DEFAULT NULL,
  `annee_bac` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `annee_universitaire` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `interval_taxes`
--

CREATE TABLE `interval_taxes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `min` double(8,3) NOT NULL,
  `max` double(8,3) NOT NULL,
  `taxe` double(8,3) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lycees`
--

CREATE TABLE `lycees` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` int(11) NOT NULL,
  `code_gouv` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `mandates`
--

CREATE TABLE `mandates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `cin` varchar(255) DEFAULT NULL,
  `nom` varchar(255) DEFAULT NULL,
  `fac` varchar(255) DEFAULT NULL,
  `univ` varchar(255) DEFAULT NULL,
  `annee` varchar(255) DEFAULT NULL,
  `num_dec` varchar(255) DEFAULT NULL,
  `nbre_mois` varchar(255) DEFAULT NULL,
  `net_a_payer` varchar(255) DEFAULT NULL,
  `num_emission` varchar(255) DEFAULT NULL,
  `date_payement` varchar(255) DEFAULT NULL,
  `date_fin_validite` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED DEFAULT NULL,
  `state` int(11) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `mandates_files`
--

CREATE TABLE `mandates_files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `num_dec` varchar(255) NOT NULL,
  `annee_gestion` varchar(255) NOT NULL,
  `path` varchar(255) NOT NULL,
  `date_payment` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `etat` int(11) NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `message_predefinis`
--

CREATE TABLE `message_predefinis` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `text` longtext NOT NULL,
  `text_fr` longtext NOT NULL,
  `text_ar` longtext NOT NULL,
  `type` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL DEFAULT 'AppModelsAdmin',
  `model_id` bigint(20) UNSIGNED NOT NULL,
  `start_at` timestamp NULL DEFAULT NULL,
  `end_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `montant_prets`
--

CREATE TABLE `montant_prets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code_diplome` varchar(255) DEFAULT NULL,
  `annee_etude` int(11) DEFAULT NULL,
  `resultat` tinyint(1) DEFAULT NULL,
  `montant` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `motif_retrait_inscriptions`
--

CREATE TABLE `motif_retrait_inscriptions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `label` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `offices`
--

CREATE TABLE `offices` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `name` varchar(255) DEFAULT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `tel` varchar(255) DEFAULT NULL,
  `fax` varchar(255) DEFAULT NULL,
  `site_web` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `gouvernorat` varchar(255) DEFAULT NULL,
  `gouvernorat_ar` varchar(255) DEFAULT NULL,
  `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ordonnance_files`
--

CREATE TABLE `ordonnance_files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `num_dec` varchar(255) NOT NULL,
  `annee_gestion` varchar(255) NOT NULL,
  `path` varchar(255) DEFAULT NULL,
  `date_payment` varchar(255) NOT NULL,
  `etat` int(11) NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `orientations`
--

CREATE TABLE `orientations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `student_id` int(11) NOT NULL,
  `code_filiere` int(11) NOT NULL,
  `tour` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `annee_universitaire` int(11) NOT NULL DEFAULT 1,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `demande_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `preparations`
--

CREATE TABLE `preparations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ndec` varchar(255) NOT NULL,
  `etat` varchar(255) NOT NULL,
  `date_envoi` date DEFAULT NULL,
  `criteria` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `professions`
--

CREATE TABLE `professions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `type_code` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `profession_types`
--

CREATE TABLE `profession_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `reclamations`
--

CREATE TABLE `reclamations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `reclamation_type_id` varchar(255) NOT NULL,
  `detail` text DEFAULT NULL,
  `response` text DEFAULT NULL,
  `etat` varchar(255) DEFAULT NULL,
  `document_file` varchar(255) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `document_file2` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `reclamation_types`
--

CREATE TABLE `reclamation_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `rectificatifs`
--

CREATE TABLE `rectificatifs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `rectificatif_numero_id` bigint(20) UNSIGNED DEFAULT NULL,
  `cin` varchar(255) DEFAULT NULL,
  `catb` varchar(255) DEFAULT NULL,
  `lot` varchar(255) DEFAULT NULL,
  `nom` varchar(255) DEFAULT NULL,
  `datnais` varchar(255) DEFAULT NULL,
  `gouv` varchar(255) DEFAULT NULL,
  `sexe` varchar(255) DEFAULT NULL,
  `profp` varchar(255) DEFAULT NULL,
  `anet` varchar(255) DEFAULT NULL,
  `discip` varchar(255) DEFAULT NULL,
  `fac` varchar(255) DEFAULT NULL,
  `univ` varchar(255) DEFAULT NULL,
  `inf` varchar(255) DEFAULT NULL,
  `sup` varchar(255) DEFAULT NULL,
  `enf` varchar(255) DEFAULT NULL,
  `revp` varchar(255) DEFAULT NULL,
  `revm` varchar(255) DEFAULT NULL,
  `avis` varchar(255) DEFAULT NULL,
  `res` varchar(255) DEFAULT NULL,
  `moy` varchar(255) DEFAULT NULL,
  `natdec` varchar(255) DEFAULT NULL,
  `situa` varchar(255) DEFAULT NULL,
  `mbs` varchar(255) DEFAULT NULL,
  `nmb` varchar(255) DEFAULT NULL,
  `mf` varchar(255) DEFAULT NULL,
  `ndec` varchar(255) DEFAULT NULL,
  `dat` varchar(255) DEFAULT NULL,
  `montanttotal` varchar(255) DEFAULT NULL,
  `pourcentage` varchar(255) DEFAULT NULL,
  `office` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `annee_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `rectificatif_numeros`
--

CREATE TABLE `rectificatif_numeros` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `num_decision` varchar(255) NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `resultats`
--

CREATE TABLE `resultats` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `is_credit` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `retrait_inscriptions`
--

CREATE TABLE `retrait_inscriptions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `student_id` bigint(20) UNSIGNED DEFAULT NULL,
  `student_name` varchar(255) DEFAULT NULL,
  `student_cin` varchar(255) DEFAULT NULL,
  `etablissement_id` bigint(20) UNSIGNED DEFAULT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED DEFAULT NULL,
  `motif_retrait_inscription_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `stats`
--

CREATE TABLE `stats` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `num_dec` varchar(255) NOT NULL,
  `date_payment` varchar(255) NOT NULL,
  `annee_universitaire_id` bigint(20) UNSIGNED NOT NULL,
  `tranche` varchar(255) NOT NULL,
  `nbr_mois` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `mnt` varchar(255) DEFAULT NULL,
  `path` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `stat_groups`
--

CREATE TABLE `stat_groups` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `mtm` double(8,3) NOT NULL,
  `mtf` double(8,3) NOT NULL,
  `res` int(11) NOT NULL,
  `order` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `student_from_mes`
--

CREATE TABLE `student_from_mes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `NBAC` varchar(255) DEFAULT NULL,
  `CIN` varchar(255) DEFAULT NULL,
  `NOM_A` varchar(255) DEFAULT NULL,
  `NOM_L` varchar(255) DEFAULT NULL,
  `JJ` varchar(255) DEFAULT NULL,
  `MM` varchar(255) DEFAULT NULL,
  `AA` varchar(255) DEFAULT NULL,
  `CD_LYC` varchar(255) DEFAULT NULL,
  `CD_GOUV` varchar(255) DEFAULT NULL,
  `SEX` varchar(255) DEFAULT NULL,
  `PROF` varchar(255) DEFAULT NULL,
  `CODE_FILIERE` varchar(255) DEFAULT NULL,
  `TOUR` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `annee_bac` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `telescope_entries`
--

CREATE TABLE `telescope_entries` (
  `sequence` bigint(20) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL,
  `batch_id` char(36) NOT NULL,
  `family_hash` varchar(255) DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `telescope_entries_tags`
--

CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) NOT NULL,
  `tag` varchar(255) NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `telescope_monitoring`
--

CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) NOT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `universites`
--

CREATE TABLE `universites` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` int(11) NOT NULL,
  `code_gouv` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_fr` varchar(255) NOT NULL,
  `name_ar` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` date DEFAULT NULL,
  `code_office` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `uploaded_files`
--

CREATE TABLE `uploaded_files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `document_file` varchar(255) DEFAULT NULL,
  `annee_universitaire` int(11) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `variables`
--

CREATE TABLE `variables` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `label_fr` varchar(255) DEFAULT NULL,
  `label_ar` varchar(255) DEFAULT NULL,
  `code` varchar(255) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `field` varchar(255) DEFAULT NULL,
  `deleted_at` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admins_username_unique` (`username`),
  ADD UNIQUE KEY `admins_email_unique` (`email`),
  ADD KEY `admins_office_id_foreign` (`office_id`);

--
-- Index pour la table `annee_bacs`
--
ALTER TABLE `annee_bacs`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `annee_universitaires`
--
ALTER TABLE `annee_universitaires`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `attestations`
--
ALTER TABLE `attestations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `attestations_attestation_types_id_foreign` (`attestation_types_id`),
  ADD KEY `attestations_annee_universitaire_id_foreign` (`annee_universitaire_id`),
  ADD KEY `attestations_etablissement_id_foreign` (`etablissement_id`),
  ADD KEY `attestations_office_id_foreign` (`office_id`);

--
-- Index pour la table `attestation_types`
--
ALTER TABLE `attestation_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `attestation_types_code_unique` (`code`);

--
-- Index pour la table `attestation_type_has_offices`
--
ALTER TABLE `attestation_type_has_offices`
  ADD PRIMARY KEY (`id`),
  ADD KEY `attestation_type_has_offices_office_id_foreign` (`office_id`),
  ADD KEY `attestation_type_has_offices_attestation_type_id_foreign` (`attestation_type_id`);

--
-- Index pour la table `attest_type_has_doc_attest`
--
ALTER TABLE `attest_type_has_doc_attest`
  ADD PRIMARY KEY (`id`),
  ADD KEY `attest_type_has_doc_attest_attestation_types_id_foreign` (`attestation_types_id`),
  ADD KEY `attest_type_has_doc_attest_documents_attestations_id_foreign` (`documents_attestations_id`);

--
-- Index pour la table `audits`
--
ALTER TABLE `audits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `audits_auditable_type_auditable_id_index` (`auditable_type`,`auditable_id`),
  ADD KEY `audits_user_id_user_type_index` (`user_id`,`user_type`);

--
-- Index pour la table `bourse_alternances`
--
ALTER TABLE `bourse_alternances`
  ADD PRIMARY KEY (`id`),
  ADD KEY `bourse_alternances_annee_universitaire_id_foreign` (`annee_universitaire_id`),
  ADD KEY `bourse_alternances_etablissement_id_foreign` (`etablissement_id`);

--
-- Index pour la table `catbs`
--
ALTER TABLE `catbs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `catbs_code_unique` (`code`);

--
-- Index pour la table `champ_predefinis`
--
ALTER TABLE `champ_predefinis`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `classifications`
--
ALTER TABLE `classifications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `classifications_code_unique` (`code`),
  ADD KEY `classifications_parent_id_foreign` (`parent_id`),
  ADD KEY `classifications_demande_type_id_foreign` (`demande_type_id`),
  ADD KEY `classifications_profession_id_foreign` (`profession_id`);

--
-- Index pour la table `code_organismes`
--
ALTER TABLE `code_organismes`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `config_demande_types`
--
ALTER TABLE `config_demande_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `config_demande_types_demande_type_id_foreign` (`demande_type_id`);

--
-- Index pour la table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `decisions_files`
--
ALTER TABLE `decisions_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `decisions_files_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `delegations`
--
ALTER TABLE `delegations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `delegations_gouvernorat_id_foreign` (`gouvernorat_id`);

--
-- Index pour la table `demandes`
--
ALTER TABLE `demandes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `demandes_annee_universitaire_id_foreign` (`annee_universitaire_id`),
  ADD KEY `demandes_demande_type_id_foreign` (`demande_type_id`),
  ADD KEY `demandes_classification_id_foreign` (`classification_id`),
  ADD KEY `preparation_bourse_id_index` (`preparation_bourse_id`),
  ADD KEY `preparation_insertion_id_index` (`preparation_insertion_id`),
  ADD KEY `preparation_pret_id_index` (`preparation_pret_id`),
  ADD KEY `preparation_aide_sociale_id_index` (`preparation_aide_sociale_id`),
  ADD KEY `preparation_stage_id_index` (`preparation_stage_id`),
  ADD KEY `lot_demandes_index` (`lot`);

--
-- Index pour la table `demande_annee_etudes`
--
ALTER TABLE `demande_annee_etudes`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `demande_recouvrements`
--
ALTER TABLE `demande_recouvrements`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `demande_types`
--
ALTER TABLE `demande_types`
  ADD PRIMARY KEY (`id`),
  ADD KEY `demande_types_parent_id_foreign` (`parent_id`);

--
-- Index pour la table `demande_type_has_roles`
--
ALTER TABLE `demande_type_has_roles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `demande_type_has_roles_demande_type_id_foreign` (`demande_type_id`),
  ADD KEY `demande_type_has_roles_role_id_foreign` (`role_id`);

--
-- Index pour la table `diplomes`
--
ALTER TABLE `diplomes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `diplomes_code_unique` (`code`),
  ADD KEY `diplomes_idx_code` (`code`);

--
-- Index pour la table `diplome_demande_types`
--
ALTER TABLE `diplome_demande_types`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `diplome_etablissements`
--
ALTER TABLE `diplome_etablissements`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `distances`
--
ALTER TABLE `distances`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `documents_attestations`
--
ALTER TABLE `documents_attestations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `documents_attestations_code_unique` (`code`);

--
-- Index pour la table `documents_attestation_uploads`
--
ALTER TABLE `documents_attestation_uploads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `documents_attestation_uploads_attestation_id_foreign` (`attestation_id`),
  ADD KEY `documents_attestation_uploads_document_attestation_id_foreign` (`document_attestation_id`);

--
-- Index pour la table `document_classifications`
--
ALTER TABLE `document_classifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `document_classifications_classification_id_foreign` (`classification_id`);

--
-- Index pour la table `document_classification_demandes`
--
ALTER TABLE `document_classification_demandes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `demande_document_classification_id_foreign` (`document_classification_id`),
  ADD KEY `document_classification_demandes_demande_id_foreign` (`demande_id`);

--
-- Index pour la table `document_predefinis`
--
ALTER TABLE `document_predefinis`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `document_predefinis_code_unique` (`code`);

--
-- Index pour la table `etablissements`
--
ALTER TABLE `etablissements`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `exported_files`
--
ALTER TABLE `exported_files`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Index pour la table `fiche_organismes`
--
ALTER TABLE `fiche_organismes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fiche_organismes_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `filieres`
--
ALTER TABLE `filieres`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `gouvernorats`
--
ALTER TABLE `gouvernorats`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `group_conditions`
--
ALTER TABLE `group_conditions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `group_conditions_group_id_foreign` (`group_id`);

--
-- Index pour la table `historiques`
--
ALTER TABLE `historiques`
  ADD PRIMARY KEY (`id`),
  ADD KEY `historiques_annee_id_foreign` (`annee_id`),
  ADD KEY `historiques_idx_res_type_annee_id` (`res`,`type`,`annee_id`),
  ADD KEY `lot_historiques_index` (`lot`),
  ADD KEY `situa_index` (`situa`);

--
-- Index pour la table `international_students`
--
ALTER TABLE `international_students`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `interval_taxes`
--
ALTER TABLE `interval_taxes`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Index pour la table `lycees`
--
ALTER TABLE `lycees`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `mandates`
--
ALTER TABLE `mandates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mandates_annee_universitaire_id_foreign` (`annee_universitaire_id`),
  ADD KEY `mandates_idx_type_annee_id_cin` (`type`,`annee_universitaire_id`,`cin`),
  ADD KEY `date_payement_index` (`date_payement`);

--
-- Index pour la table `mandates_files`
--
ALTER TABLE `mandates_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mandates_files_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `message_predefinis`
--
ALTER TABLE `message_predefinis`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Index pour la table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Index pour la table `montant_prets`
--
ALTER TABLE `montant_prets`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `motif_retrait_inscriptions`
--
ALTER TABLE `motif_retrait_inscriptions`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Index pour la table `offices`
--
ALTER TABLE `offices`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `offices_code_unique` (`code`),
  ADD KEY `offices_parent_id_foreign` (`parent_id`);

--
-- Index pour la table `ordonnance_files`
--
ALTER TABLE `ordonnance_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ordonnance_files_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `orientations`
--
ALTER TABLE `orientations`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `password_resets`
--
ALTER TABLE `password_resets`
  ADD KEY `password_resets_email_index` (`email`);

--
-- Index pour la table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`),
  ADD KEY `permissions_demande_type_id_foreign` (`demande_type_id`);

--
-- Index pour la table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Index pour la table `preparations`
--
ALTER TABLE `preparations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `preparations_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `professions`
--
ALTER TABLE `professions`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `profession_types`
--
ALTER TABLE `profession_types`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `reclamations`
--
ALTER TABLE `reclamations`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `reclamation_types`
--
ALTER TABLE `reclamation_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reclamation_types_code_unique` (`code`);

--
-- Index pour la table `rectificatifs`
--
ALTER TABLE `rectificatifs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rectificatifs_annee_id_foreign` (`annee_id`),
  ADD KEY `rectificatifs_rectificatif_numero_id_foreign` (`rectificatif_numero_id`);

--
-- Index pour la table `rectificatif_numeros`
--
ALTER TABLE `rectificatif_numeros`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rectificatif_numeros_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `resultats`
--
ALTER TABLE `resultats`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `retrait_inscriptions`
--
ALTER TABLE `retrait_inscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `retrait_inscriptions_motif_retrait_inscription_id_foreign` (`motif_retrait_inscription_id`),
  ADD KEY `retrait_inscriptions_etablissement_id_foreign` (`etablissement_id`),
  ADD KEY `retrait_inscriptions_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Index pour la table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Index pour la table `stats`
--
ALTER TABLE `stats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `stats_annee_universitaire_id_foreign` (`annee_universitaire_id`);

--
-- Index pour la table `stat_groups`
--
ALTER TABLE `stat_groups`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `student_from_mes`
--
ALTER TABLE `student_from_mes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `index_student_from_mes_annee_bac` (`annee_bac`),
  ADD KEY `index_student_from_mes_annee_nbac` (`NBAC`),
  ADD KEY `index_student_from_mes_annee_cin` (`CIN`);

--
-- Index pour la table `telescope_entries`
--
ALTER TABLE `telescope_entries`
  ADD PRIMARY KEY (`sequence`),
  ADD UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  ADD KEY `telescope_entries_batch_id_index` (`batch_id`),
  ADD KEY `telescope_entries_family_hash_index` (`family_hash`),
  ADD KEY `telescope_entries_created_at_index` (`created_at`),
  ADD KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`);

--
-- Index pour la table `telescope_entries_tags`
--
ALTER TABLE `telescope_entries_tags`
  ADD PRIMARY KEY (`entry_uuid`,`tag`),
  ADD KEY `telescope_entries_tags_tag_index` (`tag`);

--
-- Index pour la table `telescope_monitoring`
--
ALTER TABLE `telescope_monitoring`
  ADD PRIMARY KEY (`tag`);

--
-- Index pour la table `universites`
--
ALTER TABLE `universites`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `uploaded_files`
--
ALTER TABLE `uploaded_files`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `variables`
--
ALTER TABLE `variables`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `annee_bacs`
--
ALTER TABLE `annee_bacs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `annee_universitaires`
--
ALTER TABLE `annee_universitaires`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `attestations`
--
ALTER TABLE `attestations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `attestation_types`
--
ALTER TABLE `attestation_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `attestation_type_has_offices`
--
ALTER TABLE `attestation_type_has_offices`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `attest_type_has_doc_attest`
--
ALTER TABLE `attest_type_has_doc_attest`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `audits`
--
ALTER TABLE `audits`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `bourse_alternances`
--
ALTER TABLE `bourse_alternances`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `catbs`
--
ALTER TABLE `catbs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `champ_predefinis`
--
ALTER TABLE `champ_predefinis`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `classifications`
--
ALTER TABLE `classifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `code_organismes`
--
ALTER TABLE `code_organismes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `config_demande_types`
--
ALTER TABLE `config_demande_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `decisions_files`
--
ALTER TABLE `decisions_files`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `delegations`
--
ALTER TABLE `delegations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `demandes`
--
ALTER TABLE `demandes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `demande_annee_etudes`
--
ALTER TABLE `demande_annee_etudes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `demande_recouvrements`
--
ALTER TABLE `demande_recouvrements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `demande_types`
--
ALTER TABLE `demande_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `demande_type_has_roles`
--
ALTER TABLE `demande_type_has_roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `diplomes`
--
ALTER TABLE `diplomes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `diplome_demande_types`
--
ALTER TABLE `diplome_demande_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `diplome_etablissements`
--
ALTER TABLE `diplome_etablissements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `distances`
--
ALTER TABLE `distances`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `documents_attestations`
--
ALTER TABLE `documents_attestations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `documents_attestation_uploads`
--
ALTER TABLE `documents_attestation_uploads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `document_classifications`
--
ALTER TABLE `document_classifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `document_classification_demandes`
--
ALTER TABLE `document_classification_demandes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `document_predefinis`
--
ALTER TABLE `document_predefinis`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `etablissements`
--
ALTER TABLE `etablissements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `exported_files`
--
ALTER TABLE `exported_files`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `fiche_organismes`
--
ALTER TABLE `fiche_organismes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `filieres`
--
ALTER TABLE `filieres`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `gouvernorats`
--
ALTER TABLE `gouvernorats`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `group_conditions`
--
ALTER TABLE `group_conditions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `historiques`
--
ALTER TABLE `historiques`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `international_students`
--
ALTER TABLE `international_students`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `interval_taxes`
--
ALTER TABLE `interval_taxes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lycees`
--
ALTER TABLE `lycees`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `mandates`
--
ALTER TABLE `mandates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `mandates_files`
--
ALTER TABLE `mandates_files`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `message_predefinis`
--
ALTER TABLE `message_predefinis`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `montant_prets`
--
ALTER TABLE `montant_prets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `motif_retrait_inscriptions`
--
ALTER TABLE `motif_retrait_inscriptions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `offices`
--
ALTER TABLE `offices`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ordonnance_files`
--
ALTER TABLE `ordonnance_files`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `preparations`
--
ALTER TABLE `preparations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `reclamations`
--
ALTER TABLE `reclamations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `rectificatifs`
--
ALTER TABLE `rectificatifs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `rectificatif_numeros`
--
ALTER TABLE `rectificatif_numeros`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `stats`
--
ALTER TABLE `stats`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `student_from_mes`
--
ALTER TABLE `student_from_mes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
