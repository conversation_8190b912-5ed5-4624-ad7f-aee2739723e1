<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreClassificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string|max:40|unique:classifications,code',
            'title' => 'required|string',
            'title_fr' => 'nullable|string',
            'title_ar' => 'nullable|string',
            'active' => 'nullable|boolean',
            'classable_par_admin' => 'nullable|boolean',
            'classable' => 'nullable|boolean',
            'document_online' => 'nullable|boolean',
            'show_documents' => 'nullable|boolean',
            'config' => 'nullable|json',
            'parent_id' => 'nullable|integer',
            'demande_type_id' => 'nullable|integer',
        ];
    }
    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'classable_par_admin' => $this->toBoolean($this->classable_par_admin),
            'classable' => $this->toBoolean($this->classable),
            'document_online' => $this->toBoolean($this->document_online),
            'show_documents' => $this->toBoolean($this->show_documents),
            'active' => $this->toBoolean($this->active),
            'parent_id' => $this->parent_id ?: null,
            'demande_type_id' => $this->demande_type_id ?: null,
//            'config' => json_encode($this->config),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
