<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StatGroupRequest;
use App\Models\GroupCondition;
use App\Models\StatGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class StatGroupController extends Controller
{
    protected $cache_seconds = 900;

    public function index()
    {
        return Cache::remember('StatGroup', $this->cache_seconds, function () {
            return StatGroup::with('conditions')->orderBy('res','desc')->orderBy('order')->get();
        });
    }

    public function store(StatGroupRequest $request)
    {
        Cache::forget('StatGroup');

        StatGroup::where('res', $request->res)->where('order', '>=', $request->order)->increment('order');
        $statGroup = StatGroup::create([
            'title' => $request->title,
            'mtm' => $request->mtm,
            'mtf' => $request->mtf,
            'res' => $request->res,
            'order' => $request->order
        ]);

        if ($request->conditions) {
            foreach ($request->conditions as $key => $value) {
                GroupCondition::create([
                    'discip' => $value['discip'],
                    'anet' => $value['anet'] ?? null,
                    'group_id' => $statGroup->id,
                ]);
            }
        }

        return response($statGroup, 201);
    }

    public function edit(StatGroupRequest $request, StatGroup $statGroup)
    {
        Cache::forget('StatGroup');

        $originalOrder = $statGroup->order;
        $originalRes = $statGroup->res;

        $statGroup->update([
            'title' => $request->title,
            'mtm' => $request->mtm,
            'mtf' => $request->mtf,
            'res' => $request->res,
            'order' => $request->order
        ]);

        if($originalRes!=$request->res){
            StatGroup::where('res', $request->res)->where('id', '<>', $statGroup->id)->where('order', '>=', $request->order)->increment('order');
            StatGroup::where('res', $originalRes)->where('order', '>=', $originalOrder)->decrement('order');
        }else if ($originalOrder != $request->order) {
            $request->order > $originalOrder ?
                StatGroup::where('res', $request->res)
                ->whereBetween('order', [$originalOrder, $request->order])
                ->where('id', '<>', $statGroup->id)
                ->decrement('order') :
                StatGroup::where('res', $request->res)
                ->whereBetween('order', [$request->order,$originalOrder ])
                ->where('id', '<>', $statGroup->id)
                ->increment('order');
        }

        GroupCondition::where('group_id', $statGroup->id)->delete();

        if ($request->conditions) {
            foreach ($request->conditions as $key => $value) {
                GroupCondition::create([
                    'discip' => $value['discip'],
                    'anet' => $value['anet']??null,
                    'group_id' => $statGroup->id,
                ]);
            }
        }

        return response($statGroup, 200);
    }


    public function destroy(StatGroup $statGroup)
    {
        Cache::forget('StatGroup');

        $deletedOrder = $statGroup->order;

        GroupCondition::where('group_id', $statGroup->id)->delete();

        $statGroup->delete();

        StatGroup::where('order', '>', $deletedOrder)->decrement('order');

        return response("", 204);
    }

}
