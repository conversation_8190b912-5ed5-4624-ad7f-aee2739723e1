<?php
namespace App\Exports;

use App\Models\Universite;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class UniversiteImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'll_uni' => 'required',
                'all_uni' => 'required',
                'cd_uni' => 'required',
                'cd_gouv' => 'required',
            ],
            [],
            [
                'll_uni' => '(ll_uni)',
                'all_uni' => '(all_uni)',
                'cd_uni' => '(cd_uni)',
                'cd_gouv' => '(cd_gouv)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.ll_uni' => 'required|string',
            '*.all_uni' => 'required|string',
            '*.cd_uni' => 'required|numeric',
            '*.cd_gouv' => 'required|numeric',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Universite::where('code', $row['cd_uni'])->first();
            if ($gov){
                $gov->update([
                    'name' => $row['ll_uni'],
                    'name_fr' => $row['ll_uni'],
                    'name_ar' => $row['all_uni'],
                    'code_gouv' => $row['cd_gouv'],
                ]);
            } else {
                Universite::create([
                    'name'  => $row['ll_uni'],
                    'name_fr'  => $row['ll_uni'],
                    'name_ar'  => $row['all_uni'],
                    'code' => $row['cd_uni'],
                    'code_gouv' => $row['cd_gouv'],
                ]);
            }
        }
    }
}
