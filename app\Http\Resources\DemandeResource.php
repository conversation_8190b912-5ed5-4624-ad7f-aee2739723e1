<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class DemandeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'code_catb' => $this->code_catb,
            'code_decision' => $this->code_decision,
            'lot' => $this->lot,

            'etat' => $this->etat,
            'etat_document' => $this->etat_document,
            'etat_dossier' => $this->etat_dossier,
            'etat_complement' => $this->etat_complement,

            'etat_bourse' => $this->etat_bourse,
            'etat_bourse_insertion' => $this->etat_bourse_insertion,
            'etat_pret' => $this->etat_pret,
            'etat_contrat' => $this->etat_contrat,

            'etat_bourse_stage' => $this->etat_bourse_stage,
            'etat_aide_sociale' => $this->etat_aide_sociale,

            'is_bourse' => $this->is_bourse ,
            'is_bourse_insertion' => $this->is_bourse_insertion ,
            'is_pret' => $this->is_pret ,
            'is_aide_sociale' => $this->is_aide_sociale ,
            'is_bourse_stage' => $this->is_bourse_stage ,

            'comment_refus_aide_sociale' => $this->comment_refus_aide_sociale,
            'comment_refus_bourse_stage' => $this->comment_refus_bourse_stage,
            'comment_refus_pret' => $this->comment_refus_pret,
            'comment_refus_bourse_insertion' => $this->comment_refus_bourse_insertion,
            'comment_refus_bourse' => $this->comment_refus_bourse,

            'comment_contrat_incomplet' => $this->comment_contrat_incomplet,
            'control_fiscal' => $this->control_fiscal,
            'control_fiscal_date' => $this->control_fiscal_date?->format('Y-m-d'),
            'control_fiscal_date_fr' => $this->control_fiscal_date?->format('d/m/Y'),
            'nbr_notif_control_fiscal' => $this->nbr_notif_control_fiscal,

            'comment_incomplete' => $this->comment_incomplete,
            'comment_incoherent' => $this->comment_incoherent,
            'nbr_notif_incoherent' => $this->nbr_notif_incoherent,
            'nbr_notif_incomplete' => $this->nbr_notif_incomplete,
            'contrat_file' => $this->contrat_file,
            'contrat_file_url' => $this->contrat_file_url,
            'contrat_end_date' => $this->contrat_end_date?->format('Y-m-d'),
            'contrat_end_date_fr' => $this->contrat_end_date?->format('d/m/Y'),
            'contrat_montant' => $this->contrat_montant,

            'user' => $this->user,
            'config' => $this->config,
            'config_array' => json_decode($this->config, true),
            'annee_universitaire' => $this->anneeUniversitaire,
            'demande_type' => $this->demandeType,
            'config_demande_type' => $this->configDemandeType,
            'classification' => $this->classification,
            'demande_documents_classifications'=> $this->demandeDocumentsClassifications,
            'demande_type_id' => $this->demande_type_id,
            'config_demande_type_id' => $this->config_demande_type_id,
            'classification_final_id' => $this->classification_final_id,
            'classification_id' => $this->classification_id,
            'annee_universitaire_id' => $this->annee_universitaire_id,
            'created_at' => $this->created_at->format('Y-m-d'),
            'created_at_fr' => $this->created_at->format('d/m/Y H:i'),

            'type_calcule' => $this->type_calcule,
            'revenu_annuel_pere' => $this->revenu_annuel_pere,
            'revenu_annuel_mere' => $this->revenu_annuel_mere,
            'nbr_freres_soeurs' => $this->nbr_freres_soeurs,
            'nbr_freres_soeurs_parraines' => $this->nbr_freres_soeurs_parraines,
            'nbr_freres_soeurs_handicapes' => $this->nbr_freres_soeurs_handicapes,
            'nbr_freres_soeurs_unite' => $this->nbr_freres_soeurs_unite,
            'nbr_freres_soeurs_parraines_unite' => $this->nbr_freres_soeurs_parraines_unite,
            'nbr_freres_soeurs_handicapes_unite' => $this->nbr_freres_soeurs_handicapes_unite,
            'distance' => $this->distance,
            'distance_unite' => $this->distance_unite,
            'revenu_annuel_conjoint' => $this->revenu_annuel_conjoint,

            'demande_annee_etudes' => $this->demandeAnneeEtudes,
        ];
    }
}
