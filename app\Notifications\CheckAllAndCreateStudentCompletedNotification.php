<?php

namespace App\Notifications;

use App\Models\ExportedFile;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class CheckAllAndCreateStudentCompletedNotification extends Notification
{
    use Queueable;

    public $nb;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(int $nb)
    {
        $this->nb = $nb;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Verification & creation completed",
            "subtitle" => " Verification & creation completed for students nb = ". $this->nb . " , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Verification et creation Complete ",
            "subtitle_fr" => "Verification et creation des etudiants effectuée nb = ". $this->nb . " , à : "  .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => " تم التحقق والإنشاء ",
            "subtitle_ar" => " تم التحقق والإنشاء للطلاب : " . $this->nb . " في  " . Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "table-down",
            "avatarAlt" => "Export",
            "avatarText" => "Export",
            "avatarColor" => "info",
            "type" => "Verification",
            "target_id" => "",
            "target" => "",
            "model" => "ExportedFile",
            "url" => '',

        ];
    }
}
