<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('demandes', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('etat');
            $table->json('config')->nullable();
            $table->bigInteger('user_id');
            $table->unsignedBigInteger('annee_universitaire_id')->nullable();
            $table->unsignedBigInteger('demande_type_id')->nullable();
            $table->unsignedBigInteger('classification_id')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('annee_universitaire_id')
                ->references('id')
                ->on('annee_universitaires');
            $table->foreign('demande_type_id')
                ->references('id')
                ->on('demande_types');
            $table->foreign('classification_id')
                ->references('id')
                ->on('classifications');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('demandes');
    }
};
