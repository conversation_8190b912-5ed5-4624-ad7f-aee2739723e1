<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StatResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'num_dec' => $this->num_dec,
            'date_payment' => $this->date_payment,
            'annee_universitaire_id' => $this->annee_universitaire_id,
            'tranche' => $this->tranche,
            'nbr_mois' => $this->nbr_mois,
            'title' => $this->title,
            'mnt' => $this->mnt,
            'path' => $this->path,
            'anneeUniversitaire' => $this->anneeUniversitaire,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
