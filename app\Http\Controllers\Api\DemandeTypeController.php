<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DemandeTypeMinResource;
use App\Http\Resources\DemandeTypeResource;
use App\Http\Resources\DemandeTypeTreeResource;
use App\Models\DemandeType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class DemandeTypeController extends Controller
{
    protected $cache_seconds = 900;

    function __construct()
    {
//        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $type = null;
        if ($request->demande_type_id){
            $type = DemandeType::find($request->demande_type_id);
        }
        $demandeTypes = DemandeType::query()->with(['parent', 'diplomesDemandeTypes','configs'])->withCount('fils');
        if ($type) {
            $dfs = DemandeType::query()->has('fils')->where('parent_id', $type->id)->pluck('id');
            $dfss = DemandeType::query()->has('fils')->whereIn('parent_id', $dfs)->pluck('id');
            $dfsss = DemandeType::query()->has('fils')->whereIn('parent_id', $dfss)->pluck('id');


            $demandeTypes = $demandeTypes
                ->orWhereIn('parent_id', array_merge($dfs->toArray(), $dfss->toArray(), $dfsss->toArray(),[$type->id]))
                ->orWhere('id', $type->id);
        }
        return response()->json([
            "data"=> DemandeTypeMinResource::collection($demandeTypes->get()),
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @param string $code
     * @return JsonResponse
     */
    public function indexByParent(string $code): JsonResponse
    {
        return response()->json([
            "data"=> DemandeTypeTreeResource::collection(DemandeType::where('code', $code)->with('fils','configs')->get()),
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function indexArbreFils(): JsonResponse
    {
        $data =  Cache::remember('DemandeTypeArbreFils', $this->cache_seconds, function () {
            return DemandeTypeTreeResource::collection(DemandeType::with('fils','configs')->withCount('fils')->where('parent_id', null)->get());
        });

        return response()->json([
            "data"=> $data,
        ],200);
    }

    /**
     * Display a arbre of the resource.
     *
     * @return JsonResponse
     */
    public function arbre(): JsonResponse
    {
        $typeTree = Cache::remember('DemandeTypeTree', $this->cache_seconds, function () {

            $demandeTypes = DemandeType::select( 'id', 'parent_id', 'title', 'title_fr', 'title_ar', 'code',)->with(['children'])->without('parent','fils')->whereNull(['parent_id'])->get();
            $typeTree = [];
            foreach ($demandeTypes as $type) {
                if ($type) {
                    $typeTree[] = ['id' => $type->id, 'level' => 1, 'title' => $type->title, 'title_fr' => $type->title_fr, 'title_ar' => $type->title_ar, 'parent_id' => $type->parent_id, 'code' => $type->code, 'count_fils'=>$type->children?->count() ?? 0];
                    if ($type?->children) {
                        foreach ($type->children as $child) {
                            $typeTree[] = ['id' => $child->id, 'level' => 2, 'title' => $child->title, 'title_fr' => $child->title_fr, 'title_ar' => $child->title_ar,'parent_id' => $child->parent_id, 'code' => $type->code, 'count_fils'=>$child->children?->count() ?? 0];
                            if ($child->children) {
                                foreach ($child->children as $childd) {
                                    $typeTree[] = ['id' => $childd->id, 'level' => 3, 'title' => $childd->title, 'title_fr' => $childd->title_fr, 'title_ar' => $childd->title_ar,  'parent_id' => $childd->parent_id, 'code' => $type->code, 'count_fils'=>$childd->children?->count() ?? 0];
                                    if ($childd->children) {
                                        foreach ($childd->children as $childdd) {
                                            $typeTree[] = ['id' => $childdd->id, 'level' => 4, 'title' => $childdd->title, 'title_fr' => $childdd->title_fr, 'title_ar' => $childdd->title_ar,  'parent_id' => $childdd->parent_id, 'code' => $type->code, 'count_fils'=>$childdd->children?->count() ?? 0];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

            }

            return $typeTree;
        });


        return response()->json([ "data"=> $typeTree ]);
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $demandeType = DemandeType::where('id',$id)->with(['parent', 'diplomes'])->first();
        return response()->json( $demandeType );
    }

}
