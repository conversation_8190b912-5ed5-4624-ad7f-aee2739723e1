<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class DiplomeDemandeType extends Model
{
    protected $table = 'diplome_demande_types';
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    public function demandeType() : BelongsTo
    {
        return $this->belongsTo(DemandeType::class, 'demande_type_id', 'id');
    }

    public function diplome() : BelongsTo
    {
        return $this->belongsTo(Diplome::class, 'code_diplome', 'code');
    }
}
