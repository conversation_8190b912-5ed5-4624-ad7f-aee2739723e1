<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('classifications', function (Blueprint $table) {
            $table->id()->autoIncrement();
            $table->string('code')->unique();
            $table->string('title');
            $table->string('title_fr')->nullable();
            $table->string('title_ar')->nullable();
            $table->boolean('active')->nullable();
            $table->boolean('classable')->nullable();
            $table->boolean('classable_par_admin')->nullable();
            $table->json('config')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->unsignedBigInteger('demande_type_id')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('parent_id')
                ->references('id')
                ->on('classifications') ;

            $table->foreign('demande_type_id')
                ->references('id')
                ->on('demande_types');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('classifications');
    }
};
