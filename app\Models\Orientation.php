<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Orientation extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'student_id',
        'code_filiere',
        'tour',
        'annee_universitaire',
    ];


    public function filiere() : BelongsTo
    {
        return $this->belongsTo(Filiere::class,"code_filiere","code")
            ->where('annee_universitaire', $this->annee_universitaire);
    }
    public function student() : BelongsTo
    {
        return $this->belongsTo(StudentFromMes::class,"student_id","id");
    }

    public function anneeUniversitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,"annee_universitaire","id");
    }
}
