<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMontantPretRequest;
use App\Http\Requests\UpdateMontantPretRequest;
use App\Http\Resources\MontantPretResource;
use App\Models\MontantPret;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use JetBrains\PhpStorm\Pure;

class MontantPretController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('MontantPret', $this->cache_seconds, function () {
            return MontantPretResource::collection(MontantPret::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreMontantPretRequest $request
     * @return Response
     */
    public function store(StoreMontantPretRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('MontantPret');

        $g = MontantPret::create($data);

        return response(new MontantPretResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param MontantPret $montantPret
     * @return MontantPretResource
     */
    #[Pure] public function show(MontantPret $montantPret): MontantPretResource
    {
        return new MontantPretResource($montantPret);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateMontantPretRequest $request
     * @param MontantPret $montantPret
     * @return MontantPretResource
     */
    public function edit(UpdateMontantPretRequest $request, MontantPret $montantPret): MontantPretResource
    {
        $data = $request->validated();

        Cache::forget('MontantPret');

        $montantPret->update($data);

        return new MontantPretResource($montantPret);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param MontantPret $montantPret
     * @return Response
     */
    public function destroy(MontantPret $montantPret): Response
    {
        Cache::forget('MontantPret');

        $montantPret->delete();

        return response("", 204);
    }
}
