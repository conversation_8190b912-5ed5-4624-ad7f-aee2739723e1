<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class DemandeAnneeEtude extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'user_id',
        'demande_id',
        'filiere_id',
        'code_etab',
        'code_diplome',
        'annee_etude',
        'annee_universitaire_id',
        'resultat_id',
        'moyenne',
        'credit',
        'is_nouveau_bachelier',
    ];

    protected $appends = [
        'resultat_success'
    ];

    public function resultat() : BelongsTo
    {
        return $this->belongsTo(Resultat::class, 'resultat_id', 'id');
    }

    public function getResultatSuccessAttribute()
    {
        return $this->resultat ? $this->resultat->success : false;
    }
    public function demande() : BelongsTo
    {
        return $this->belongsTo(Demande::class,'demande_id','id');
    }
    public function filiere() : BelongsTo
    {
        return $this->belongsTo(Filiere::class,'filiere_id','id');
    }
    public function etablissement() : BelongsTo
    {
        return $this->belongsTo(Etablissement::class,'code_etab','code');
    }
    public function diplome() : BelongsTo
    {
        return $this->belongsTo(Diplome::class,'code_diplome','code');
    }
    public function anneeUniversitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,'annee_universitaire_id','id');
    }
    public function user(): BelongsTo
    {
        return $this->setConnection(config('database.secondConnection'))->belongsTo(User::class,'user_id','id');
    }

    public function annee_universitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,'annee_universitaire_id','id');
    }
}
