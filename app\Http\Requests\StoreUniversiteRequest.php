<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class StoreUniversiteRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'name_fr' => 'required|string|max:100',
            'name_ar' => 'required|string|max:100',
            'code_gouv' => 'required|integer',
            'active' => 'required|boolean',
            'code' => 'required|integer|unique:universites,code',
            ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }
}
