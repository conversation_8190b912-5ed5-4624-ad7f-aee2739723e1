<?php
namespace App\Exports;

use App\Models\Office;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class OfficeImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'lib_fr' => 'required',
                'lib_ar' => 'required',
                'code_office' => 'required',
                'adresse' => 'required',
                'tel' => 'required',
                'fax' => 'required',
                'site_web' => 'required',
                'email' => 'required',
            ],
            [],
            [
                'lib_fr' => '(lib_fr)',
                'lib_ar' => '(lib_ar)',
                'code_office' => '(code_office)',
                'adresse' => '(adresse)',
                'tel' => '(tel)',
                'fax' => '(fax)',
                'site_web' => '(site_web)',
                'email' => '(email)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.lib_fr' => 'required|string',
            '*.lib_ar' => 'required|string',
            '*.code_office' => 'required|string',
            '*.adresse' => 'nullable|string',
            '*.tel' => 'nullable',
            '*.fax' => 'nullable',
            '*.site_web' => 'nullable',
            '*.email' => 'nullable',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Office::where('code', $row['code_office'])->first();
            if ($gov){
                $gov->update([
                    'name'  => $row['lib_fr'],
                    'name_fr'  => $row['lib_fr'],
                    'name_ar'  => $row['lib_ar'],
                    'adresse' => $row['adresse'],
                    'tel' => $row['tel'],
                    'fax' => $row['fax'],
                    'site_web' => $row['site_web'],
                    'email' => $row['email'],
                ]);
            } else {
                Office::create([
                    'name'  => $row['lib_fr'],
                    'name_fr'  => $row['lib_fr'],
                    'name_ar'  => $row['lib_ar'],
                    'adresse' => $row['adresse'],
                    'tel' => $row['tel'],
                    'fax' => $row['fax'],
                    'site_web' => $row['site_web'],
                    'email' => $row['email'],
                    'code' => $row['code_office'],
                ]);
            }
        }
    }
}
