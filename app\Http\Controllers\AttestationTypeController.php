<?php

namespace App\Http\Controllers;

use App\Models\AttestationType;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAttestationTypeRequest;
use App\Http\Requests\StoreDemandeTypeRequest;
use App\Http\Requests\UpdateAttestationTypeRequest;
use App\Http\Requests\UpdateDemandeTypeRequest;
use App\Http\Resources\AttestationTypeResource;
use App\Http\Resources\DemandeTypeResource;
use App\Models\AttestationTypeHasAttestationTypeDocuments;
use App\Models\AttestationTypeHasOffices;
use App\Models\ConfigDemandeType;
use App\Models\DemandeType;
use App\Models\DocumentsAttestation;
use App\Models\Office;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use League\CommonMark\Node\Block\Document;
use App\Helpers\Helpers;

class AttestationTypeController extends Controller
{
    protected $cache_seconds = 900;

    function __construct()
    {
        //        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }
    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index() //: AnonymousResourceCollection
    {
        return Cache::remember('AttestationType', $this->cache_seconds, function () {
            return AttestationTypeResource::collection(AttestationType::all());
        });
        //return AttestationType::with('documentsAttestations')->get();
    }

    public function getDocumentAttestations()
    {
        $results = DocumentsAttestation::select(
            'documents_attestations.title as document_title',
            'documents_attestations.id as document_id',
            'attestation_types.title as type',
            'attestation_types.id as type_id'
        )
            ->join('attest_type_has_doc_attest', 'documents_attestations.id', '=', 'attest_type_has_doc_attest.documents_attestations_id')
            ->join('attestation_types', 'attest_type_has_doc_attest.attestation_types_id', '=', 'attestation_types.id')
            ->get();
        //return AttestationType::with('documentsAttestations')->get();
        return $results;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreAttestationTypeRequest $request
     * @return Response
     */
    public function store(StoreAttestationTypeRequest $request): Response
    {
        $data = $request->validated();
        Cache::forget('AttestationType');
        Helpers::clearCacheIdp(['AttestationType','DocumentsAttestation']);

        $attestationType = AttestationType::create($data);

        return response(new AttestationTypeResource($attestationType), 201);
    }

    /**
     * Display the specified resource.
     *
     * @param AttestationType $attestationType
     * @return AttestationTypeResource
     */
    public function show(AttestationType $attestationType): AttestationTypeResource
    {
        return new AttestationTypeResource($attestationType);
    }


    public function edit(UpdateAttestationTypeRequest $request)
    {
        $data = $request->validated();

        Cache::forget('AttestationType');
        Helpers::clearCacheIdp(['AttestationType','DocumentsAttestation']);
        $attestationType = AttestationType::findOrFail($request->id);
//        return response()->json(collect($data['documents_attestations'])->pluck('id'));




        $attestationType->update($data);
        try {
            //code...
            $data['documentsAttestations'] = $data['documents_attestations'];
            $attestationType->syncPivotAndLog($data, [
                'offices' => ['display' => 'name'],
                'documentsAttestations' => ['display' => 'title']
            ]);

        } catch (\Throwable $th) {
            $attestationType->offices()->sync(collect($data['offices'])->pluck('id'));
            $attestationType->documentsAttestations()->sync(collect($data['documents_attestations'])->pluck('id'));
        }


//        if (isset($request['documents_attestations'])) {
//            $attestationTypeHasDocsByTypeIdDeleted = AttestationTypeHasAttestationTypeDocuments::where('attestation_types_id', $request->id)->delete();
//
//            foreach ($request->documents_attestations as $key => $value) {
//                # code...
//                $id = DocumentsAttestation::where('code', $value)->first()->id;
//
//                //$attestationTypeHasDocs = AttestationTypeHasAttestationTypeDocuments::where('attestation_types_id', $request->id)->where('documents_attestations_id', $id)->first();
//                //if (!$attestationTypeHasDocs) {
//
//                    AttestationTypeHasAttestationTypeDocuments::create([
//                        'attestation_types_id' => $request->id,
//                        'documents_attestations_id' => $id
//                    ]);
//                //}
//            }
//        }
//
//        // todo
//        if(isset($request['offices'])){
//            $attestationTypeHasOfficeByTypeId = AttestationTypeHasOffices::where('attestation_type_id', $request->id)->delete();
//
//            foreach ($request->offices as $key => $value) {
//                # code...
//                    AttestationTypeHasOffices::create([
//                        'attestation_type_id' => $request->id,
//                        'office_id' => $value['id']
//                    ]);
//            }
//        }

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param AttestationType $attestationType
     * @return Response
     */
    public function destroy(AttestationType $attestationType): Response
    {
        Cache::forget('AttestationType');
        Helpers::clearCacheIdp(['AttestationType','DocumentsAttestation']);

        $attestationType->delete();

        return response("", 204);
    }
}
