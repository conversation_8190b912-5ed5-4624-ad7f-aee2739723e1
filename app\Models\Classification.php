<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Classification extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'code',
        'title',
        'title_fr',
        'title_ar',
        'classable',
        'classable_par_admin',
        'document_online',
        'show_documents',
        'active',
        'parent_id',
        'demande_type_id',
        'config',
        'is_boursier',
        'profession_id',
    ];

    protected $casts = [
//        'config' => 'array',
        'active' => 'boolean',
        'document_online' => 'boolean',
        'classable' => 'boolean',
        'classable_par_admin' => 'boolean',
    ];

    public $appends=[
//        'last_demande_type_config',
//    'level'
    ];
    public function demandes(): HasMany
    {
        return $this->hasMany(Demande::class);
    }

    public function documentsClassifications(): HasMany
    {
        return $this->hasMany(DocumentClassification::class);
    }

    public function getLastDemandeTypeConfigAttribute(): ?ConfigDemandeType
    {
        return $this->demandeType?->last_config;
    }

    public function demandeType() : BelongsTo
    {
        return $this->belongsTo(DemandeType::class, 'demande_type_id');
    }

    public function parent() : BelongsTo
    {
        return $this->belongsTo(__CLASS__, 'parent_id');
    }
    public function fils(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id')->with('children')->without('parent','fils');
    }

    public function profession() : BelongsTo
    {
        return $this->belongsTo(Profession::class, 'profession_id');
    }
}
