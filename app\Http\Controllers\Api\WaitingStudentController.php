<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreWaitingStudentRequest;
use App\Http\Requests\UpdateWaitingStudentRequest;
use App\Http\Resources\WaitingStudentResource;
use App\Jobs\NotifyUserOfCompletedVerificationAndCreatStudent;
use App\Jobs\ProcessCheckAllAndCreateStudent;
use App\Mail\ConfirmedStudentMail;
use App\Mail\StudentRefusedMail;
use App\Models\AnneeBac;
use App\Models\AnneeUniversitaire;
use App\Models\Etablissement;
use App\Models\EtudiantAnneeUniversitaire;
use App\Models\InternationalStudent;
use App\Models\StudentFromMes;
use App\Models\User;
use App\Models\WaitingUser;
use App\Rules\InOfficeCenter;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class WaitingStudentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $sort = $request->query('sort', 'asc');
        $page = $request->query('page', 0);
        $pageSize = $request->query('pageSize', 10);
        $sortColumn = $request->query('sortColumn', 'created_at');
        $annee_bac = $request->query('annee_bac', null);
        $annee_universitaire = $request->query('annee_universitaire', null);
        $etablissement_id = $request->query('etablissement_id', null);
        $university_id = $request->query('university_id', null);
        $type = $request->query('student_type', 'tunisien');


        $query =  WaitingUser::with(['nationality', 'etudiantAnneeUniversitaires']);
        if ($type){
            $query->where('type', $type);
        }
        if ($annee_bac){
            $query->where('annee_bac', $annee_bac);
        }
        if ($annee_universitaire){
            $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($annee_universitaire) {
                return $query->where('annee_universitaire_id', $annee_universitaire);
            });
        }
        if ($university_id){
            if ($etablissement_id){
                $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($etablissement_id) {
                    return $query->where('code_etab', $etablissement_id);
                });
            } else {
                $etabIds = Etablissement::where('code_univ', $university_id)->pluck('code');
                $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($etabIds) {
                    $query->whereIn('code_etab', $etabIds);
                });
            }
        }
        else {
            if ($etablissement_id){
                $query->whereHas('etudiantAnneeUniversitaires', function ($query) use ($etablissement_id) {
                    return $query->where('code_etab', $etablissement_id);
                });
            }
        }
        if ($request->q != ""){
            $query->when($request->q, function($q)use($request){
                $q->where('name', 'like', '%'.$request->q.'%')
                    ->orWhere('email', 'like', '%'.$request->q.'%')
                    ->orWhere('name_ar', 'like', '%'.$request->q.'%')
                    ->orWhere('firstName', 'like', '%'.$request->q.'%')
                    ->orWhere('cin', 'like', '%'.$request->q.'%')
                    ->orWhere('num_bac', 'like', '%'.$request->q.'%')
                    ->orWhere('matricule', 'like', '%'.$request->q.'%')
                    ->orWhere('num_passport', 'like', '%'.$request->q.'%');
            });
        }


        $recordsTotal = $query->count();

        $query->sortable([$sortColumn => $sort])->paginate($pageSize, ['*'], 'page', $page);


        $waitingStudent = $query->get();

        return response()->json([
            "params"=> $request->all(),
            "allData"=> [],
            "data"=> WaitingStudentResource::collection($waitingStudent),
            "total"=> $recordsTotal
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index2(): AnonymousResourceCollection
    {
        return WaitingStudentResource::collection(WaitingUser::sortable()->paginate(5));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreWaitingStudentRequest $request
     * @return Response
     */
    public function store(StoreWaitingStudentRequest $request): Response
    {
        $data = $request->validated();
        $r = WaitingUser::create($data);

        return response(new WaitingStudentResource($r) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateWaitingStudentRequest $request
     * @param WaitingUser $waiting_student
     * @return Response
     */
    public function edit(UpdateWaitingStudentRequest $request, WaitingUser $waiting_student): Response
    {
        $data = $request->validated();
        $waiting_student->update($data);
        return response(new WaitingStudentResource($waiting_student) , 201);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $student = WaitingUser::find($id);
        $student->etudiantAnneeUniversitaires()->delete();
        $student->delete();
        return response()->json([
            "result"=> "success"
        ],200);
    }

    public function refuse(Request $request): JsonResponse
    {//dd($request->all());
        $international_student = WaitingUser::find($request->id);

        Mail::to($international_student->email)->send(new StudentRefusedMail($international_student,$request->raison_refus));

        //$international_student->delete();
        return response()->json([
            "result"=> "success"
        ],200);
    }
    public function checkAndCreateStudent(Request $request){


        $annUniversitaire = AnneeUniversitaire::find($request->annee_universitaire_id);
        $isAfterBachlor = $annUniversitaire?->annee_bac == $request->annee_bac;
        $secondConnection = config('database.secondConnection');

        if($request->type === 'tunisien'){
            $student_exist_in_minister_list = StudentFromMes::where('CIN', $request->cin)->where('NBAC', $request->num_bac)->where('CIN', '<>', '99999999')->first();

            if( $student_exist_in_minister_list){
                //existe dans la liste de ministère
                $validator = Validator::make($request->all(), [
                    'email' => ['required', 'email', 'unique:'.$secondConnection.'.users,email'],
                    'cin' => 'required|digits:8|unique:'.$secondConnection.'.users,cin',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'message'=>'Validations fails',
                        'errors'=>$validator->errors()
                    ],422);
                }
                $user = User::create([
                    "cin" => $request->cin,
                    "num_bac" => $request->num_bac,
                    "type" => $request->type,
                    "name" => $request->name,
                    "name_ar" => $request->name_ar,
                    "firstName" => $request->firstName,
                    "username" => $request->email,
                    "email" => $request->email,
                    "email_perso" => $request->email_perso,
                    "password" => $request->password,
                    "phoneNumber" => $request->phoneNumber,
                    "phoneNumber2" => $request->phoneNumber2,
                    "address" => $request->address,
                    "annee_bac" => $request->annee_bac,
                    "code_postal" => $request->code_postal,
                    "country_id" => $request->nationality_id,
                    "nationality_id" => $request->nationality_id,
                    "code_gouv" => $request->code_gouv,
                    "date_naissance" =>  date('Y-m-d', strtotime($request->date_naissance)),
                    "role" => "client",
                    "student_from_mes_id" => $student_exist_in_minister_list->id,
                    "pere" => $request->pere,
                    "mere" => $request->mere,
                    "sex" => $request->sex,
                ]);

                // delete waiting student
                $student = WaitingUser::where('cin', $request->cin)->first();
                $student->delete();


                $etudiantAnneeUniversitaire = EtudiantAnneeUniversitaire::create([
                    "user_id" => $user->id,
                    "filiere_id" => $request->filiere_id,
                    "annee_etude" => $request->annee_etude,
                    "code_diplome" => $request->code_diplome,
                    "code_etab" => $request->code_etab,
                    "annee_universitaire_id" => $request->annee_universitaire_id,
                    "is_nouveau_bachelier" => $isAfterBachlor,
                ]);

                Mail::to($request->email)->send(new ConfirmedStudentMail($request));

                return response()->json("created",201);

            } else {
                return response()->json("not_found",404);
            }
        }
        else {
            $international_student_exist_in_minister_list = InternationalStudent::where('matricule', $request->matricule)->where('num_passport', $request->num_passport)->where('annee_bac', $request->annee_bac)->where('matricule', '<>', '99999999')->first();

            if( $international_student_exist_in_minister_list){
                //existe dans la liste de ministère

                $validator = Validator::make($request->all(), [
                    'email' => ['required', 'email', 'unique:'.$secondConnection.'.users,email'],
                    'cin' => 'required|digits:8|unique:'.$secondConnection.'.users,cin',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'message'=>'Validations fails',
                        'errors'=>$validator->errors()
                    ],422);
                }
                $user = User::create([
                    "matricule" => $request->matricule,
                    "num_passport" => $request->num_passport,
                    "type" => $request->type,
                    "name" => $request->name,
                    "name_ar" => $request->name_ar,
                    "firstName" => $request->firstName,
                    "username" => $request->email,
                    "email" => $request->email,
                    "email_perso" => $request->email_perso,
                    "password" => $request->password,
                    "phoneNumber" => $request->phoneNumber,
                    "phoneNumber2" => $request->phoneNumber2,
                    "address" => $request->address,
                    "annee_bac" => $request->annee_bac,
                    "code_postal" => $request->code_postal,
                    "country_id" => $request->nationality_id,
                    "nationality_id" => $request->nationality_id,
                    "code_gouv" => $request->gouvernorat,
                    "date_naissance" =>  date('Y-m-d', strtotime($request->date_naissance)),
                    "role" => "client",
                    "international_student_id" => $international_student_exist_in_minister_list->id,
                    "pere" => $request->pere,
                    "mere" => $request->mere,
                    "sex" => $request->sex,
                ]);

                $etudiantAnneeUniversitaire = EtudiantAnneeUniversitaire::create([
                    "user_id" => $user->id,
                    "filiere_id" => $request->filiere_id,
                    "annee_etude" => $request->annee_etude,
                    "code_diplome" => $request->code_diplome,
                    "code_etab" => $request->code_etab,
                    "annee_universitaire_id" => $request->annee_universitaire_id,
                    "is_nouveau_bachelier" => $isAfterBachlor,
                ]);

                return response()->json("created",201);
            } else {
                return response()->json("not_found",404);
            }
        }
    }

    public function checkAllAndCreateStudent(){

        dispatch(new ProcessCheckAllAndCreateStudent(auth()->user()));

        return response()->json('ok',200);

    }

    public function signupNewStudent(Request $request){

        $waitingStudent = WaitingUser::find($request->id);
        $secondConnection = config('database.secondConnection');

        if($request->type === 'tunisien'){

            $validator1 = Validator::make($request->all(), [
                'cin' => 'required|digits:8|unique:'.$secondConnection.'.users,cin',
                'num_bac' => 'required|digits:6',
                'name' => 'required|max:50',
                'name_ar' => 'sometimes|nullable|max:50',
                'code_postal' => 'required',
                'annee_bac' => 'required',
                'nationality_id' => 'required',
                'code_gouv' => 'required',
                'email_perso' => 'nullable|email',
                'phoneNumber' => 'required|digits:8',
                'phoneNumber2' => 'nullable|digits:8',
                'address' => 'required',
                'pere' => 'nullable',
                'mere' => 'nullable',
                'filiere_id' => 'nullable',
                'code_etab' => 'required',
                'code_diplome' => 'required',
                'annee_etude' => 'required',
            ]);
            if ($validator1->fails()) {
                return response()->json([
                    'message' => 'Validations fails',
                    'errors' => $validator1->errors()
                ], 422);
            }

            $studentFromMes = StudentFromMes::where('annee_bac', $request->annee_bac)->where('NBAC', $request->num_bac)->first();
            if(!$studentFromMes) {
                $studentFromMes = StudentFromMes::create([
                    'NBAC' => $request->num_bac,
                    'CIN' => $request->cin,
                    'NOM_A' => $request->name_ar,
                    'NOM_L' => $request->name,
                    'CD_LYC' => '',
                    'CD_GOUV' => $request->code_gouv,
                    'PROF' => '',
                    'CODE_FILIERE' => '',
                    'annee_bac' => $request->annee_bac
                ]);
            }
            if($studentFromMes){
                //existe dans la liste de ministère
                $user = User::create([
                    "cin" => $request->cin,
                    "num_bac" => $request->num_bac,
                    "type" => $request->type,
                    "name" => $request->name,
                    "name_ar" => $request->name_ar,
                    "firstName" => $request->firstName,
                    "username" => $request->email,
                    "email" => $request->email,
                    "email_perso" => $request->email_perso,
                    "password" => $waitingStudent->password,
                    "phoneNumber" => $request->phoneNumber,
                    "phoneNumber2" => $request->phoneNumber2,
                    "address" => $request->address,
                    "annee_bac" => $request->annee_bac,
                    "code_postal" => $request->code_postal,
                    "country_id" => $request->nationality_id,
                    "nationality_id" => $request->nationality_id,
                    "code_gouv" => $request->code_gouv,
                    "date_naissance" =>  date('Y-m-d', strtotime($request->date_naissance)),
                    "role" => "client",
                    "student_from_mes_id" => $studentFromMes->id,
                    "pere" => $request->pere,
                    "mere" => $request->mere,
                    "sex" => $request->sex,
                ]);

                $etudiantAnneeUniversitaires = $waitingStudent?->etudiantAnneeUniversitaires;
                /** @var EtudiantAnneeUniversitaire $etudiantAnneeUniversitaire */
                foreach ($etudiantAnneeUniversitaires as $etudiantAnneeUniversitaire){
                    $etudiantAnneeUniversitaire->update([
                        'user_id' => $user->id,
                        'waiting_user_id' => null,
                        'filiere_id' => $request->filiere_id,
                        'code_etab' => $request->code_etab,
                        'code_diplome' => $request->code_diplome,
                        'annee_etude' => $request->annee_etude,
                    ]);
                }

                Mail::to($request->email)->send(new ConfirmedStudentMail($user));

                // delete waiting student
                $waitingStudent->delete();
                $student = WaitingUser::where('cin', $request->cin);
                $student->delete();

                return response()->json("created",201);

            } else {
                return response()->json("not_found",404);
            }
        }
        else {
            $validator1 = Validator::make($request->all(), [
                'num_passport' => 'required',
                'matricule' => 'required|max:8|min:8|unique:'.$secondConnection.'.users,matricule',
                'name' => 'required|max:20',
                'firstName' => 'sometimes|nullable|max:20',
                'code_postal' => 'required',
                'annee_bac' => 'required',
                'nationality_id' => 'required',
                'code_gouv' => 'sometimes|nullable',
                'email_perso' => 'nullable|email',
                'phoneNumber' => 'required|digits:8',
                'phoneNumber2' => 'nullable|digits:8',
                'address' => 'required',
                'pere' => 'nullable',
                'mere' => 'nullable',
                'filiere_id' => 'nullable',
                'code_etab' => 'required',
                'code_diplome' => 'required',
                'annee_etude' => 'required',
            ]);
            if ($validator1->fails()) {
                return response()->json([
                    'message' => 'Validations fails',
                    'errors' => $validator1->errors()
                ], 422);
            }
            $international_student_exist_in_minister_list = StudentFromMes::where('annee_bac', $request->annee_bac)->where('NBAC', $request->num_bac)->first();
            if(!$international_student_exist_in_minister_list) {
                $international_student_exist_in_minister_list = InternationalStudent::create([
                    'num_passport'=>$request->num_passport,
                    'matricule' => $request->matricule,
                    'name' => $request->name,
                    'firstName' => $request->firstName,
                    'phoneNumber' => $request->phoneNumber,
                    'address' => $request->address,
                    'zipCode' => $request->code_postal,
                    'dateOfBirth' => $request->date_naissance,
                    'placeOfBirth' => '',
                    'sex' => $request->sex,
                    'nationality_id' => $request->nationality_id,
                    'code_etab'=>$request->code_etab,
                    'filiere_id'=>$request->filiere_id,
                    'foyer',
                    'annee_bac'=>$request->annee_bac,
                    'annee_universitaire'=>$request->annee_universitaire_id,
                ]);
            }


            if( $international_student_exist_in_minister_list){
                //existe dans la liste de ministère
                $user = User::create([
                    "matricule" => $request->matricule,
                    "num_passport" => $request->num_passport,
                    "type" => $request->type,
                    "name" => $request->name,
                    "name_ar" => $request->name_ar,
                    "firstName" => $request->firstName,
                    "username" => $request->email,
                    "email" => $request->email,
                    "email_perso" => $request->email_perso,
                    "password" => $waitingStudent->password,
                    "phoneNumber" => $request->phoneNumber,
                    "phoneNumber2" => $request->phoneNumber2,
                    "address" => $request->address,
                    "annee_bac" => $request->annee_bac,
                    "code_postal" => $request->code_postal,
                    "country_id" => $request->nationality_id,
                    "nationality_id" => $request->nationality_id,
                    "code_gouv" => $request->gouvernorat,
                    "date_naissance" =>  date('Y-m-d', strtotime($request->date_naissance)),
                    "role" => "client",
                    "international_student_id" => $international_student_exist_in_minister_list->id,
                    "pere" => $request->pere,
                    "mere" => $request->mere,
                    "sex" => $request->sex,
                ]);

                $etudiantAnneeUniversitaires = $waitingStudent?->etudiantAnneeUniversitaires;
                foreach ($etudiantAnneeUniversitaires as $etudiantAnneeUniversitaire){
                    $etudiantAnneeUniversitaire->update([
                        'user_id' => $user->id,
                        'waiting_user_id' => null,
                        'filiere_id' => $request->filiere_id,
                        'code_etab' => $request->code_etab,
                        'code_diplome' => $request->code_diplome,
                        'annee_etude' => $request->annee_etude,
                    ]);
                }

                Mail::to($request->email)->send(new ConfirmedStudentMail($user));

                // delete waiting student
                $waitingStudent->delete();
                $student = WaitingUser::where('matricule', $request->matricule);
                $student->delete();

                return response()->json("created",201);
            } else {
                return response()->json("not_found",404);
            }
        }
    }

    public function checkStudent(Request $request)
    {
        if($request->type === 'tunisien'){
            $student_exist_in_minister_list = StudentFromMes::where('CIN', $request->cin)->where('NBAC', $request->num_bac)->where('CIN', '<>', '99999999')->first();

            if( $student_exist_in_minister_list){

                return response()->json("created",201);
            } else {
                return response()->json("not_found",404);
            }
        }
        else {
            $international_student_exist_in_minister_list = InternationalStudent::where('matricule', $request->matricule)->where('num_passport', $request->num_passport)->where('annee_bac', $request->annee_bac)->where('matricule', '<>', '99999999')->first();

            if( $international_student_exist_in_minister_list){

                return response()->json("created",201);
            } else {
                return response()->json("not_found",404);
            }
        }
    }

}
