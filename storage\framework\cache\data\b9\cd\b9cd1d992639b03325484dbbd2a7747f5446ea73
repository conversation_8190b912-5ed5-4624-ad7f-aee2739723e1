1748264810O:58:"Illuminate\Http\Resources\Json\AnonymousResourceCollection":8:{s:8:"resource";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:25:{i:0;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:50;s:4:"name";s:6:"ARIANA";s:7:"name_fr";s:6:"ARIANA";s:7:"name_ar";s:12:"أريانة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"12";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:50;s:4:"name";s:6:"ARIANA";s:7:"name_fr";s:6:"ARIANA";s:7:"name_ar";s:12:"أريانة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"12";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:1;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:51;s:4:"name";s:6:"AUTRES";s:7:"name_fr";s:6:"AUTRES";s:7:"name_ar";s:8:"أخرى";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"99";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:51;s:4:"name";s:6:"AUTRES";s:7:"name_fr";s:6:"AUTRES";s:7:"name_ar";s:8:"أخرى";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"99";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:2;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:52;s:4:"name";s:4:"BEJA";s:7:"name_fr";s:4:"BEJA";s:7:"name_ar";s:8:"باجة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"31";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:52;s:4:"name";s:4:"BEJA";s:7:"name_fr";s:4:"BEJA";s:7:"name_ar";s:8:"باجة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"31";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:3;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:53;s:4:"name";s:9:"BEN AROUS";s:7:"name_fr";s:9:"BEN AROUS";s:7:"name_ar";s:13:"بن عروس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"13";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:53;s:4:"name";s:9:"BEN AROUS";s:7:"name_fr";s:9:"BEN AROUS";s:7:"name_ar";s:13:"بن عروس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"13";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:4;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:54;s:4:"name";s:7:"BIZERTE";s:7:"name_fr";s:7:"BIZERTE";s:7:"name_ar";s:10:"بنزرت";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"21";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:54;s:4:"name";s:7:"BIZERTE";s:7:"name_fr";s:7:"BIZERTE";s:7:"name_ar";s:10:"بنزرت";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"21";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:5;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:55;s:4:"name";s:5:"GABES";s:7:"name_fr";s:5:"GABES";s:7:"name_ar";s:8:"قابس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"64";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:55;s:4:"name";s:5:"GABES";s:7:"name_fr";s:5:"GABES";s:7:"name_ar";s:8:"قابس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"64";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:6;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:56;s:4:"name";s:5:"GAFSA";s:7:"name_fr";s:5:"GAFSA";s:7:"name_ar";s:8:"قفصة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"52";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:56;s:4:"name";s:5:"GAFSA";s:7:"name_fr";s:5:"GAFSA";s:7:"name_ar";s:8:"قفصة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"52";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:7;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:57;s:4:"name";s:8:"JENDOUBA";s:7:"name_fr";s:8:"JENDOUBA";s:7:"name_ar";s:12:"جندوبة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"32";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:57;s:4:"name";s:8:"JENDOUBA";s:7:"name_fr";s:8:"JENDOUBA";s:7:"name_ar";s:12:"جندوبة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"32";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:8;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:58;s:4:"name";s:8:"KAIROUAN";s:7:"name_fr";s:8:"KAIROUAN";s:7:"name_ar";s:16:"القيروان";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"81";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:58;s:4:"name";s:8:"KAIROUAN";s:7:"name_fr";s:8:"KAIROUAN";s:7:"name_ar";s:16:"القيروان";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"81";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:9;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:59;s:4:"name";s:9:"KASSERINE";s:7:"name_fr";s:9:"KASSERINE";s:7:"name_ar";s:14:"القصرين";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"43";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:59;s:4:"name";s:9:"KASSERINE";s:7:"name_fr";s:9:"KASSERINE";s:7:"name_ar";s:14:"القصرين";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"43";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:10;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:60;s:4:"name";s:6:"KEBILI";s:7:"name_fr";s:6:"KEBILI";s:7:"name_ar";s:8:"قبلي";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"61";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:60;s:4:"name";s:6:"KEBILI";s:7:"name_fr";s:6:"KEBILI";s:7:"name_ar";s:8:"قبلي";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"61";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:11;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:61;s:4:"name";s:6:"LE KEF";s:7:"name_fr";s:6:"LE KEF";s:7:"name_ar";s:10:"الكاف";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"41";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:61;s:4:"name";s:6:"LE KEF";s:7:"name_fr";s:6:"LE KEF";s:7:"name_ar";s:10:"الكاف";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"41";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:12;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:62;s:4:"name";s:6:"MAHDIA";s:7:"name_fr";s:6:"MAHDIA";s:7:"name_ar";s:14:"المهدية";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"82";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:62;s:4:"name";s:6:"MAHDIA";s:7:"name_fr";s:6:"MAHDIA";s:7:"name_ar";s:14:"المهدية";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"82";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:13;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:63;s:4:"name";s:7:"MANOUBA";s:7:"name_fr";s:7:"MANOUBA";s:7:"name_ar";s:10:"منوبة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"15";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:63;s:4:"name";s:7:"MANOUBA";s:7:"name_fr";s:7:"MANOUBA";s:7:"name_ar";s:10:"منوبة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"15";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:14;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:64;s:4:"name";s:7:"MEDNINE";s:7:"name_fr";s:7:"MEDNINE";s:7:"name_ar";s:10:"مدنين";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"63";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:64;s:4:"name";s:7:"MEDNINE";s:7:"name_fr";s:7:"MEDNINE";s:7:"name_ar";s:10:"مدنين";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"63";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:15;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:65;s:4:"name";s:8:"MONASTIR";s:7:"name_fr";s:8:"MONASTIR";s:7:"name_ar";s:16:"المنستير";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"83";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:65;s:4:"name";s:8:"MONASTIR";s:7:"name_fr";s:8:"MONASTIR";s:7:"name_ar";s:16:"المنستير";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"83";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:16;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:66;s:4:"name";s:6:"NABEUL";s:7:"name_fr";s:6:"NABEUL";s:7:"name_ar";s:8:"نابل";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"91";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:66;s:4:"name";s:6:"NABEUL";s:7:"name_fr";s:6:"NABEUL";s:7:"name_ar";s:8:"نابل";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"91";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:17;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:67;s:4:"name";s:4:"SFAX";s:7:"name_fr";s:4:"SFAX";s:7:"name_ar";s:10:"صفاقس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"71";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:67;s:4:"name";s:4:"SFAX";s:7:"name_fr";s:4:"SFAX";s:7:"name_ar";s:10:"صفاقس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"71";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:18;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:68;s:4:"name";s:11:"SIDI BOUZID";s:7:"name_fr";s:11:"SIDI BOUZID";s:7:"name_ar";s:19:"سيدي بوزيد";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"51";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:68;s:4:"name";s:11:"SIDI BOUZID";s:7:"name_fr";s:11:"SIDI BOUZID";s:7:"name_ar";s:19:"سيدي بوزيد";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"51";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:19;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:69;s:4:"name";s:7:"SILIANA";s:7:"name_fr";s:7:"SILIANA";s:7:"name_ar";s:12:"سليانة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"42";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:69;s:4:"name";s:7:"SILIANA";s:7:"name_fr";s:7:"SILIANA";s:7:"name_ar";s:12:"سليانة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"42";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:20;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:70;s:4:"name";s:6:"SOUSSE";s:7:"name_fr";s:6:"SOUSSE";s:7:"name_ar";s:8:"سوسة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"84";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:70;s:4:"name";s:6:"SOUSSE";s:7:"name_fr";s:6:"SOUSSE";s:7:"name_ar";s:8:"سوسة";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"84";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:21;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:71;s:4:"name";s:9:"TATAOUINE";s:7:"name_fr";s:9:"TATAOUINE";s:7:"name_ar";s:12:"تطاوين";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"62";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:71;s:4:"name";s:9:"TATAOUINE";s:7:"name_fr";s:9:"TATAOUINE";s:7:"name_ar";s:12:"تطاوين";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"62";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:22;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:72;s:4:"name";s:6:"TOZEUR";s:7:"name_fr";s:6:"TOZEUR";s:7:"name_ar";s:8:"توزر";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"53";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:72;s:4:"name";s:6:"TOZEUR";s:7:"name_fr";s:6:"TOZEUR";s:7:"name_ar";s:8:"توزر";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"53";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:23;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:73;s:4:"name";s:5:"TUNIS";s:7:"name_fr";s:5:"TUNIS";s:7:"name_ar";s:8:"تونس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"11";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:73;s:4:"name";s:5:"TUNIS";s:7:"name_fr";s:5:"TUNIS";s:7:"name_ar";s:8:"تونس";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"11";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}i:24;O:38:"App\Http\Resources\GouvernoratResource":4:{s:8:"resource";O:22:"App\Models\Gouvernorat":36:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"bpasBack.gouvernorats";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:74;s:4:"name";s:9:"ZAGHOUANE";s:7:"name_fr";s:9:"ZAGHOUANE";s:7:"name_ar";s:10:"زغوان";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"14";s:6:"active";i:1;s:10:"deleted_at";N;}s:11:" * original";a:10:{s:2:"id";i:74;s:4:"name";s:9:"ZAGHOUANE";s:7:"name_fr";s:9:"ZAGHOUANE";s:7:"name_ar";s:10:"زغوان";s:3:"nbr";N;s:10:"created_at";s:19:"2023-09-15 07:59:20";s:10:"updated_at";s:19:"2023-09-15 07:59:20";s:4:"code";s:2:"14";s:6:"active";i:1;s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:6:"active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:7:"name_fr";i:2;s:7:"name_ar";i:3;s:3:"nbr";i:4;s:4:"code";i:5;s:6:"active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:21:" * excludedAttributes";a:0:{}s:10:"auditEvent";N;s:14:"auditCustomOld";N;s:14:"auditCustomNew";N;s:13:"isCustomEvent";b:0;s:16:" * forceDeleting";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:12:"preserveKeys";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}s:4:"with";a:0:{}s:10:"additional";a:0:{}s:8:"collects";s:38:"App\Http\Resources\GouvernoratResource";s:10:"collection";r:2;s:29:" * preserveAllQueryParameters";b:0;s:18:" * queryParameters";N;s:12:"preserveKeys";b:1;}