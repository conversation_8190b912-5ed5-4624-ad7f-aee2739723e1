<?php
namespace App\Exports;

use App\Models\Gouvernorat;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\HeadingRowImport;
use Validator;
use Illuminate\Support\Collection;

class GouvernoratImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'l_gouv' => 'required',
                'al_gouv' => 'required',
                'cd_gouv' => 'required',
            ],
            [],
            [
                'l_gouv' => '(l_gouv)',
                'al_gouv' => '(al_gouv)',
                'cd_gouv' => '(cd_gouv)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.l_gouv' => 'required|string',
            '*.al_gouv' => 'required|string',
            '*.cd_gouv' => 'required|numeric',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Gouvernorat::where('code', $row['cd_gouv'])->first();
            if ($gov){
                $gov->update([
                    'name' => $row['l_gouv'],
                    'name_fr' => $row['l_gouv'],
                    'name_ar' => $row['al_gouv'],
                ]);
            } else {
                Gouvernorat::create([
                    'name'  => $row['l_gouv'],
                    'name_fr'  => $row['l_gouv'],
                    'name_ar'  => $row['al_gouv'],
                    'code' => $row['cd_gouv'],
                ]);
            }
        }
    }
}
