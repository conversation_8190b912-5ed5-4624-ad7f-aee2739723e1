<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class FicheOrganismeStatExport implements FromView, WithEvents
{
    protected $data;
    protected $totals;
    protected $office;
    protected $yearStart;
    protected $yearEnd;
    protected $date_export;

    public function __construct(Collection $data, $totals, $office, $yearStart, $yearEnd)
    {
        $this->data = $data;
        $this->totals = $totals;
        $this->office = $office;
        $this->yearStart = $yearStart;
        $this->yearEnd = $yearEnd;
        $this->totals = $totals;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');

    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }

    public function view(): View
    {
        return view('statistiques.ficheOrganismeStat', [
            'data' => $this->data,
            'totals' => $this->totals,
            'office' => $this->office,
            'yearStart' => $this->yearStart,
            'yearEnd' => $this->yearEnd,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title
        ]);
    }

}
