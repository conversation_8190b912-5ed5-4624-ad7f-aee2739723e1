<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMandateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'type' => 'required|string',
            'annee_universitaire_id' => 'required|numeric',
            'date_payment' => 'required|string',
            'file' => 'required|mimes:csv',
        ];
    }
}
