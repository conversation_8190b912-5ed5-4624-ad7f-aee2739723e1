<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DatabaseBackupEtudiant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:backup-etudiant';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automating Daily Backups etudiant';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (! Storage::exists('backup')) {
            Storage::makeDirectory('backup');
        }

        $filename = "backup-etudiant-" . Carbon::now()->format('Y-m-d') . ".sql.gz";

        $command = "mysqldump --column-statistics=0 --user=" . config('database.connections.mysql2.username') ." --password=" . config('database.connections.mysql2.password')
            . " --host=" . config('database.connections.mysql2.host') . " " . config('database.connections.mysql2.database')
            . " --ignore-table=". config('database.connections.mysql2.database') .".personal_access_tokens"

            . "  | gzip > " . storage_path() . "/app/backup/" . $filename;

        $returnVar = NULL;
        $output  = NULL;

        exec($command, $output, $returnVar);
    }
}
