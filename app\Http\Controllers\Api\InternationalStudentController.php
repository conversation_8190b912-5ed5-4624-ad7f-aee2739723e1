<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreInternationalStudentRequest;
use App\Http\Requests\UpdateInternationalStudentRequest;
use App\Http\Resources\InternationalStudentResource;
use App\Models\Etablissement;
use App\Models\Filiere;
use App\Models\InternationalStudent;
use App\Models\Universite;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class InternationalStudentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $sort = $request->query('sort', 'asc');
        $page = $request->query('page', 0);
        $pageSize = $request->query('pageSize', 10);
        $sortColumn = $request->query('sortColumn', 'CIN');
        $annee_bac = $request->query('annee_bac', null);
        $annee_universitaire = $request->query('annee_universitaire', null);
        $etablissement_id = $request->query('etablissement_id', null);
        $universite_id = $request->query('university_id', null);


        $query =  InternationalStudent::with(['nationality', 'etablissement', 'filiere']);
        if ($annee_bac){
            $query->where('annee_bac', $annee_bac);
        }
        if ($annee_universitaire){
            $query->where('annee_universitaire', $annee_universitaire);
        }
        if ($etablissement_id){
//            $filieresCodes = Filiere::where('code_etab', $etablissement_id)->pluck('id');
//            $query->whereIn('filiere_id', $filieresCodes);
            $query->where('code_etab', $etablissement_id);
        } else {
            if ($universite_id){
                $universite = Universite::where( 'code',$universite_id)?->first()  ?? '';
                $etablissementCodes = Etablissement::where('code_univ', $universite->code)->pluck('code');
//                $filieresCodes = Filiere::whereIn('code_etab', $etablissementCodes)->pluck('id');
//                $query->whereIn('filiere_id', $filieresCodes);
                $query->whereIn('code_etab', $etablissementCodes);
            }
        }
        if ($request->q != ""){
            $query->when($request->q, function($q)use($request){
                $q->where('num_passport', 'like', '%'.$request->q.'%')
                    ->orWhere('matricule', 'like', '%'.$request->q.'%')
                    ->orWhere('name', 'like', '%'.$request->q.'%')
                    ->orWhere('firstName', 'like', '%'.$request->q.'%');
            });
        }


        $recordsTotal = $query->count();

        $query->sortable([$sortColumn => $sort])->paginate($pageSize, ['*'], 'page', $page);


        $internationalStudent = $query->get();

        return response()->json([
            "params"=> $request->all(),
            "allData"=> [],
            "data"=> InternationalStudentResource::collection($internationalStudent),
            "total"=> $recordsTotal
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index2(): AnonymousResourceCollection
    {
        return InternationalStudentResource::collection(InternationalStudent::sortable()->paginate(5));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreInternationalStudentRequest $request
     * @return Response
     */
    public function store(StoreInternationalStudentRequest $request): Response
    {
        $data = $request->validated();
        $documentPredefini = InternationalStudent::create($data);

        return response(new InternationalStudentResource($documentPredefini) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateInternationalStudentRequest $request
     * @param InternationalStudent $international_students
     * @return Response
     */
    public function edit(UpdateInternationalStudentRequest $request, InternationalStudent $international_students): Response
    {
        $data = $request->validated();
        $international_students->update($data);
        return response(new InternationalStudentResource($international_students) , 201);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $international_students = InternationalStudent::find($id);
        $international_students->delete();
        return response()->json([
            "result"=> "success"
        ],200);
    }
}
