<?php
namespace App\Exports;

use App\Models\Distance;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;

class DistanceImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        foreach ($rows as $row) {
            foreach ($row as $key => $item) {
                if ($item !== null && $key !== 'gouv'){
                    $d = Distance::where('code_gouv1', $row['gouv'])->where('code_gouv2', $key)->first();
                    if ($d) {
                        $d->update([
                            'code_gouv1' => $row['gouv'],
                            'code_gouv2' => $key,
                            'distance' => $item,
                        ]);
                    } else {
                        Distance::create([
                            'code_gouv1' => $row['gouv'],
                            'code_gouv2' => $key,
                            'distance' => $item,
                        ]);
                    }
                }
            }
        }
    }
}
