<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class OfficeResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'name_fr' => $this->name_fr,
            'name_ar' => $this->name_ar,
            'code' => $this->code,
            'adresse' => $this->adresse,
            'tel' => $this->tel,
            'fax' => $this->fax,
            'site_web' => $this->site_web,
            'email' => $this->email,
            'active' => $this->active,
            'parent_id' => $this->parent_id,
            'gouvernorat_ar' => $this->gouvernorat_ar,
            'gouvernorat' => $this->gouvernorat,

            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
