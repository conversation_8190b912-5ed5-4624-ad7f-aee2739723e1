<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAnneeBacRequest;
use App\Http\Requests\UpdateAnneeBacRequest;
use App\Http\Resources\AnneeBacResource;
use App\Models\AnneeBac;
use App\Models\ExportedFile;
use App\Models\UploadedFile;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExportedFileController extends Controller
{

    public function index()
    {
        return response()->json(ExportedFile::orderBy('id', 'DESC')->get());
    }

//    public function destroy(ExportedFile $exportedFile): Response
//    {
//        $exportedFile->delete();
//        return response("", 204);
//    }

    public function getExportedFile(Request $request): StreamedResponse|JsonResponse
    {
        $request->validate([
            'id' => 'required',
        ]);

        $doc =ExportedFile::findOrFail($request->id);
        if (!$doc) {
            return response()->json('Document not found',404);
        }

        $filename = $doc->attached_file;
        $path= 'uploads/exportedFile/'. $filename;

        if (!Storage::exists($path)) {
            return response()->json(Storage::path($path), 404);
        }
        return Storage::download($path, $filename);

    }
}
