<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('preparations', function (Blueprint $table) {
            $table->id();
            $table->string('ndec');
            $table->string('etat');
            $table->date('date_envoi')->nullable();
            $table->json('criteria')->nullable();
            $table->unsignedBigInteger('annee_universitaire_id');
            $table->timestamps();
            $table->foreign('annee_universitaire_id','preparations_annee_universitaire_id_foreign')
            ->references('id')
            ->on('annee_universitaires');
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('preparations');
    }
};
