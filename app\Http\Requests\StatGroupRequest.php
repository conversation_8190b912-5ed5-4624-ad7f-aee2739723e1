<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StatGroupRequest extends FormRequest
{


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => 'required|string',
            'mtm' => 'required|numeric',
            'mtf' => 'required|numeric',
            'res' => 'required|integer',
            'order' => 'required|integer',
        ];
    }
}
