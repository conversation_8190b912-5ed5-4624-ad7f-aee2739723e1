<?php

namespace App\Models;

use App\Exports\FiliereImport;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Kyslik\ColumnSortable\Sortable;
use OwenIt\Auditing\Contracts\Auditable;

class StudentFromMes extends Model implements Auditable
{
    use Sortable;
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    public $sortable = [
        'NBAC',
        'CIN',
        'NOM_A',
        'NOM_L',
        'CD_LYC',
        'CD_GOUV',
        'PROF',
        'CODE_FILIERE',
        'annee_bac'
    ];

//    protected $with = [
//        'country','delegation'
//    ];
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'NBAC',
        'CIN',
        'NOM_A',
        'NOM_L',
        'JJ',
        'MM',
        'AA',
        'CD_LYC',
        'CD_GOUV',
        'SEX',
        'PROF',
        'CODE_FILIERE',
        'TOUR',
        'annee_bac',
        'updated_at',
        'created_at',
        'deleted_at',
    ];

    public function gouvernorat() : BelongsTo
    {
        return $this->belongsTo(Gouvernorat::class,"CD_GOUV","code");
    }

    public function anneeBac() : BelongsTo
    {
        return $this->belongsTo(AnneeBac::class,"annee_bac","id");
    }

    public function lycee() : BelongsTo
    {
        return $this->belongsTo(Lycee::class,"CD_LYC","code");
    }

    public function filiere() : BelongsTo
    {
        return $this->belongsTo(Filiere::class,"CODE_FILIERE","code");
    }
//
//    public function etablissement() : BelongsTo
//    {
//        return $this->belongsTo(Etablissement::class)->where('code', $this->filiere?->code_etab);
//    }

    public function profession() : BelongsTo
    {
        return $this->belongsTo(Profession::class,"PROF","code");
    }

    public $appends=[
        'date_naissance',
    ];

    public function orientations() : HasMany
    {
        return $this->hasMany(Orientation::class,'student_id','id');
    }

    public function getDateNaissanceAttribute(){
        if($this->JJ && $this->MM && $this->AA){
            if ($this->AA >50){
                $aa = "19".$this->AA;
            } else {
                $aa = "20".sprintf("%02d", $this->AA);
            }
            $myDate = $this->JJ.'/'.$this->MM.'/'.$aa;

            return Carbon::createFromFormat('d/m/Y', $myDate);
        }

        return null;

    }
}
