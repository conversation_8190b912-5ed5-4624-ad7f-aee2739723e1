<?php

namespace App\Http\Controllers;

use App\Models\DemandeRecouvrement;
use App\Http\Requests\StoreDemandeRecouvrementRequest;
use App\Http\Requests\UpdateDemandeRecouvrementRequest;
use App\Http\Resources\DemandeRecouvrementResource;
use App\Notifications\DemandeRecouvrementAttentePaiementNotification;
use App\Notifications\DemandeRecouvrementPayeeNotification;
use App\Notifications\DemandeRecouvrementRefuserNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Notification;
use Illuminate\Support\Facades\Storage;
use Mpdf\Mpdf;

class DemandeRecouvrementController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        if($request->has("type")){
            return DemandeRecouvrementResource::collection(DemandeRecouvrement::where('type', $request->type)->with("student")->orderBy('demande_recouvrements.created_at', 'desc')->get());
        }else {
            return DemandeRecouvrementResource::collection(DemandeRecouvrement::with("student")->get());
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreDemandeRecouvrementRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreDemandeRecouvrementRequest $request)
    {

        $request->validate([
            'student_id' => 'required|numeric',
            'annee_universitaire' => 'required|string',
            'type' => 'required|string',
        ]);

        $demandeRecouvrement = DemandeRecouvrement::create([
            'student_id' => $request->student_id,
            'annee_universitaire' => $request->annee_universitaire,
            'type' => $request->type,
            'status' => $request->has('status') ? $request->status : "en_cours",
            'montant' => $request->has('montant') ? $request->montant : 0,
            'commentaire' => $request->has('commentaire') ? $request->commentaire : null,
            'num_quittance' => $request->has('num_quittance') ? $request->num_quittance : null,
            'date_paiement' => $request->has('date_paiement') ? $request->date_paiement : null,
            'raison_refus' => $request->has('raison_refus') ? $request->raison_refus : null,
            'recette_finance' => $request->has('recette_finance') ? $request->recette_finance : null,

        ]);

        if ($demandeRecouvrement->status == "payer") {
            Notification::send([$demandeRecouvrement->student], new DemandeRecouvrementPayeeNotification($demandeRecouvrement));
        }
        if ($demandeRecouvrement->status == "attente_paiement") {
            Notification::send([$demandeRecouvrement->student], new DemandeRecouvrementAttentePaiementNotification($demandeRecouvrement));
        }
        if ($demandeRecouvrement->status == "refused") {
            Notification::send([$demandeRecouvrement->student], new DemandeRecouvrementRefuserNotification($demandeRecouvrement));
        }

    return response(new DemandeRecouvrementResource($demandeRecouvrement), 201);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateDemandeRecouvrementRequest  $request
     * @param  \App\Models\DemandeRecouvrement  $demandeRecouvrement
     * @return \Illuminate\Http\Response
     */
    public function edit(UpdateDemandeRecouvrementRequest $request)
    {
        $request->validate([
            'student_id' => 'required|numeric',
            'annee_universitaire' => 'required|string',
            'type' => 'required|string',
        ]);

        $demandeRecouvrement = DemandeRecouvrement::findOrFail($request->id);

        $demandeRecouvrement->update([
            'student_id' => $request->student_id,
            'annee_universitaire' => $request->annee_universitaire,
            'type' => $request->type,

            'status' => $request->has('status') ? $request->status : $demandeRecouvrement->status,
            'montant' => $request->has('montant') ? $request->montant : $demandeRecouvrement->montant,
            'commentaire' => $request->has('commentaire') ? $request->commentaire : $demandeRecouvrement->commentaire,
            'num_quittance' => $request->has('num_quittance') ? $request->num_quittance : $demandeRecouvrement->num_quittance,
            'date_paiement' => $request->has('date_paiement') ? $request->date_paiement : $demandeRecouvrement->date_paiement,
            'raison_refus' => $request->has('raison_refus') ? $request->raison_refus : $demandeRecouvrement->raison_refus,
            'recette_finance' => $request->has('recette_finance') ? $request->recette_finance : $demandeRecouvrement->recette_finance,
            'decision' => $request->has('decision') ? $request->decision : $demandeRecouvrement->decision,
            'date_generation' => $request->has('date_generation') ? Carbon::parse($request->date_generation)->format('Y-m-d H:i:s'): $demandeRecouvrement->date_generation,

        ]);

        $demandeRecouvrements = DemandeRecouvrement::findOrFail($request->id);

        if ($demandeRecouvrements->status == "payer") {
            Notification::send([$demandeRecouvrements->student], new DemandeRecouvrementPayeeNotification($demandeRecouvrements));
        }
        if ($demandeRecouvrements->status == "attente_paiement") {
            Notification::send([$demandeRecouvrements->student], new DemandeRecouvrementAttentePaiementNotification($demandeRecouvrements));
        }
        if ($demandeRecouvrements->status == "refused") {
            Notification::send([$demandeRecouvrements->student], new DemandeRecouvrementRefuserNotification($demandeRecouvrements));
        }

        return response("created" , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\DemandeRecouvrement  $demandeRecouvrement
     * @return \Illuminate\Http\Response
     */
    public function destroy(DemandeRecouvrement $demandeRecouvrement)
    {
        $demandeRecouvrement->delete();

        return response("", 204);
    }

    public function generatePdf(Request $request)
    {
        $request->validate([
            'id' => 'required|numeric',
            //'num_quittance' => 'required'
        ]);

        $demandeRecouvrement = DemandeRecouvrement::findOrFail($request->id);

        $demandeRecouvrement->num_quittance = $request->num_quittance;
        $demandeRecouvrement->montant = $request->montant;
        $demandeRecouvrement->commentaire = $request->commentaire;
        $demandeRecouvrement->date_generation = Carbon::parse($request->date_generation)->format('Y-m-d H:i:s');
        $demandeRecouvrement->save();

        if ($demandeRecouvrement->type == 'pret') {
            $request->validate([
                'decision' => 'required|string',
                'recette_finance' => 'required',

            ]);

            $demandeRecouvrement->decision = $request->decision;
            $demandeRecouvrement->recette_finance = $request->recette_finance;
            $demandeRecouvrement->save();

        }
        $name = $demandeRecouvrement->student['name_ar'] ? $demandeRecouvrement->student->name_ar : $demandeRecouvrement->student->name;
        $cin = $demandeRecouvrement->student->type == 'tunisien' ? $demandeRecouvrement->student->cin : $demandeRecouvrement->student->matricule;
        $annee= $demandeRecouvrement->annee_universitaire;
        $montant = $demandeRecouvrement->montant;
        $recette_finance = $demandeRecouvrement->recette_finance;
        $decision = $demandeRecouvrement->decision;
        $date=  $demandeRecouvrement->date_generation ? Carbon::parse($demandeRecouvrement->date_generation)->format('Y-m-d') : Carbon::today()->format('Y-m-d');

        $month_name = array("", "جانفي", "فيفري", "مارس", "أفريل", "ماي", "جوان", "جويلية", "أوت", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر");

        $split = preg_split('/-/', $date);
        $year = $split[0];
        $month = round($split[1]);
        $day = round($split[2]);
        $date = $day . '  ' . $month_name[$month] . ' ' . $year;


        $template = $demandeRecouvrement->type == 'pret' ? 'demandeRecouvrementPret' : 'demandeRecouvrementBourse';

        $html = view($template, compact('name', 'cin', 'annee','date', 'montant', 'recette_finance', 'decision'))->render();

        // Setup a filename
        $documentFileName = "demande_recouvrement".time().".pdf";

        // Create the mPDF document
        $doc = new Mpdf( [
            //'mode' => 'utf-8',
            //'format' => 'A4',
            //'margin_header' => '3',
            //'margin_top' => '20',
            //'margin_bottom' => '20',
            //'margin_footer' => '2',
        ]);

        // Set some header informations for output
        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="'.$documentFileName.'"'
        ];

        $doc->WriteHTML($html);

        // Save PDF on your public storage
        Storage::put($documentFileName, $doc->Output($documentFileName, "S"));
        // Get file back from storage with the give header informations

        return Storage::download($documentFileName, 'Request', $header);

    }
}
