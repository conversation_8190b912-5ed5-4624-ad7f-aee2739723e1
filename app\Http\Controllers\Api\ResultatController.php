<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ResultatResource;
use App\Models\Resultat;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use JetBrains\PhpStorm\Pure;

class ResultatController extends Controller
{
    /**
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return ResultatResource::collection(Resultat::all());
    }

    /**
     * @param Resultat $resultat
     * @return ResultatResource
     */
    #[Pure] public function show(Resultat $resultat): ResultatResource
    {
        return new ResultatResource($resultat);
    }


}
