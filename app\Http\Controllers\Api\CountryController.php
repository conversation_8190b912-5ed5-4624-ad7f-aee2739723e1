<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCountryRequest;
use App\Http\Requests\UpdateCountryRequest;
use App\Http\Resources\CountryResource;
use App\Models\Country;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use JetBrains\PhpStorm\Pure;

class CountryController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Country', $this->cache_seconds, function () {
            return CountryResource::collection(Country::all()->sortBy(fn($item) => $item->id !== 227)->values()->all());
        });

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreCountryRequest $request
     * @return Response
     */
    public function store(StoreCountryRequest $request): Response
    {
        Cache::forget('Country');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $g = Country::create($data);

        return response(new CountryResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Country $Country
     * @return CountryResource
     */
    #[Pure] public function show(Country $Country): CountryResource
    {
        return new CountryResource($Country);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateCountryRequest $request
     * @param Country $Country
     * @return CountryResource
     */
    public function update(UpdateCountryRequest $request, Country $Country): CountryResource
    {
        Cache::forget('Country');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $Country->update($data);

        return new CountryResource($Country);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param UpdateCountryRequest $request
     * @param Country $anneeUniversitaire
     * @return CountryResource
     */
    public function edit(UpdateCountryRequest $request, Country $Country): CountryResource
    {
        Cache::forget('Country');
        Helpers::clearCacheIdp();

        $data = $request->validated();
        $Country->update($data);

        return new CountryResource($Country);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param Country $Country
     * @return Response
     */
    public function destroy(Country $Country): Response
    {

        if($Country->internationalStudents->count()){
            throw ValidationException::withMessages(["Ce pays est utilisé par d'autres tables"]);
        }

        Cache::forget('Country');
        Helpers::clearCacheIdp();

        $Country->delete();

        return response("", 204);
    }
}
