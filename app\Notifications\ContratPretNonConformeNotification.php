<?php

namespace App\Notifications;

use App\Models\Demande;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class ContratPretNonConformeNotification extends Notification
{
    use Queueable;

    private Demande $demande;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Demande $demande)
    {
        $this->demande = $demande;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }

        return [
            "title" => "Non-compliant university loan contract document",
            "subtitle" => "Non-compliant university loan contract document for demand n°: ". $this->demande->code,
            "title_fr" => "Document contrat prêt universitaire non conforme",
            "subtitle_fr" => "Document contrat prêt universitaire non conforme pour la demande n°: " . $this->demande->code,
            "title_ar" => "وثيقة عقد القرض الجامعي غير المتوافق ",
            "subtitle_ar" => " وثيقة عقد القرض الجامعي غير المتوافق للطلب : " . $this->demande->code,
            "avatarIcon" => "report-money",
            "avatarAlt" => "Contrat",
            "avatarText" => "Contrat",
            "avatarColor" => "warning",
            "type" => "dossier",
            "target_id" => $this->demande->id,
            "target" => "demande",
            "model" => "demande",
            "url" => "",
//            "url" => "mes-demandes/". $this->demande->id,

        ];
    }
}
