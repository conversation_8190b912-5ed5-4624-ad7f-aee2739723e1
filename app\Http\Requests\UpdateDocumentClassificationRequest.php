<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDocumentClassificationRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $methode = $this->method();
        if ($methode === 'PUT') {
            return [
                'code' => 'required|string',
                'title' => 'required|string',
                'title_fr' => 'required|string',
                'title_ar' => 'required|string',
                'obligatoire' => 'nullable|boolean',
                'classification_id' => 'required|integer',
                'boursier' => 'nullable|string|sometimes',
                'resultat' => 'nullable|integer|sometimes',
                'document_file'=> 'nullable|sometimes|mimes:png,jpg,jpeg,csv,txt,xlx,xls,xlsx,pdf',
            ];
        }

        return [
            'code' => 'required|string',
            'title' => 'sometimes|required|string',
            'title_fr' => 'sometimes|nullable|string',
            'title_ar' => 'sometimes|nullable|string',
            'obligatoire' => 'sometimes|nullable|boolean',
            'classification_id' => 'sometimes|required|integer',
            'boursier' => 'nullable|string|sometimes',
            'resultat' => 'nullable|integer|sometimes',
            'document_file'=> 'nullable|sometimes|mimes:png,jpg,jpeg,csv,txt,xlx,xls,xlsx,pdf',
        ];
    }
    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'obligatoire' => $this->toBoolean($this->obligatoire)
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
