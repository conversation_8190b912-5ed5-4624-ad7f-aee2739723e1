<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateGouvernoratRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'name_fr' => 'required|string',
            'name_ar' => 'required|string',
            'active' => 'required|boolean',
            'nbr' => 'sometimes|nullable|numeric|between:0,50',
            'code' => 'required|integer|unique:gouvernorats,code,'.$this->id,
            'distances.*.code_gouv2' => 'required|distinct:strict',
            'distances.*.distance' => 'required|numeric',

        ];
    }


    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }
}
