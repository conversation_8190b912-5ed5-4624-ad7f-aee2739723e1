<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Models\Role as ModelsRole;

class Role extends ModelsRole implements Auditable
{
    use HasFactory;
    use \OwenIt\Auditing\Auditable;

    public function demande_types() : HasMany
    {
        return $this->hasMany(DemandeTypeHasRole::class,"role_id","id");
    }
}
