<?php

namespace App\Models;

use App\Models\Scopes\DemandeTypeScope;
use App\Traits\AuditableRelatedModels;
use App\Traits\PivotSyncAndAudit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class DemandeType extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable,PivotSyncAndAudit; /*AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }*/
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        //$data = $this->parentTransformAudit($data);
        //$data = $this->transformAuditRelatedModels($data);
        return $data;
    }

//    protected static function booted()
//    {
//        static::addGlobalScope(new DemandeTypeScope());
//    }
    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    const CODE_LIST = [
        'bourses_universitaires' => 'bourses_universitaires',
        'nouveau_bachelier' => 'nouveau_bachelier',
        'en_cours_d_etude' => 'en_cours_d_etude',
        'renouvellement' => 'renouvellement',
        'doctorat' => 'doctorat',
        'master' => 'master',
        'aide_sociale' => 'aide_sociale',
        'bourses_de_stage' => 'bourses_de_stage',
    ];
    const TYPE_LIST = [
        'tunisien' => 'tunisien',
        'etranger' => 'etranger',
    ];

    protected $fillable = [
        'code',
        'type',
        'group',
        'title',
        'title_fr',
        'title_ar',
        'order',
        'active',
        'bourse',
        'bourse_insertion',
        'pret',
        'bourses_de_stage',
        'aide_sociale',
        'parent_id',
        'start',
        'end',
        'date_dossier_end',
        'date_complement_end',
        'date_contrat_pret_end',
        'is_parent_etranger',
        'visibility'
    ];

    protected $with = [
        'parent',
    ];
    protected $casts = [
        'start' => 'date',
        'end' => 'date',
        'date_dossier_end' => 'date',
        'date_complement_end' => 'date',
        'date_contrat_pret_end' => 'date',
        //        'config' => 'array',
        //        'logic' => 'array',
        'active' => 'boolean',
        'pret' => 'boolean',
        'bourse_insertion' => 'boolean',
        'bourse' => 'boolean',
        'aide_sociale' => 'boolean',
        'bourses_de_stage' => 'boolean',
    ];

    public $appends=[
        'last_config',
        'root_parent_code',
    ];

    public function demandes(): HasMany
    {
        return $this->hasMany(Demande::class);
    }

    public function getRootParentCodeAttribute(): string
    {
        $root = $this;
        while ($root->parent) {
            $root = $root->parent;
        }
        return $root->code;
    }

    public function configs(): HasMany
    {
        return $this->hasMany(ConfigDemandeType::class, 'demande_type_id');
    }

    public function getLastConfigAttribute(): ?ConfigDemandeType
    {
        return ConfigDemandeType::where('demande_type_id', $this->id)->orderBy('id', 'desc')->first();
        //        return $this->configs?->last();
    }

    public function classifications(): HasMany
    {
        return $this->hasMany(Classification::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(__CLASS__, 'parent_id');
    }

    public function fils(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id');
    }

    public function diplomes(): BelongsToMany
    {
        return $this->belongsToMany(Diplome::class, 'diplome_demande_types', 'demande_type_id', 'code_diplome','id', 'code');
    }

    public function diplomesDemandeTypes(): HasMany
    {
        return $this->HasMany(DiplomeDemandeType::class,  'demande_type_id','id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id')->with('children')->without('parent','fils','configs');
    }
}
