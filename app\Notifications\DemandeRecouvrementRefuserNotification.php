<?php

namespace App\Notifications;

use App\Models\DemandeRecouvrement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Socket;

class DemandeRecouvrementRefuserNotification extends Notification
{
    use Queueable;
    private DemandeRecouvrement $demandeRecouvrement;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(DemandeRecouvrement $demandeRecouvrement)
    {
        $this->demandeRecouvrement = $demandeRecouvrement;

    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    /*public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->line('The introduction to the notification.')
                    ->action('Notification Action', url('/'))
                    ->line('Thank you for using our application!');
    }*/

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
            //Log::debug("Sent notification via websocket");

        } catch ( \Exception $e) {
            //Log::debug("cannot send notification via websocket");
        }

        return [
            "title" => "Refused request for recovery!",
            "subtitle" => "Refused request for recovery",//n°: ". $this->demande->code,
            "title_fr" => "Demande de recouverment refusée",
            "subtitle_fr" => "Votre demande de recouvrement a été refusée",//de bourse n°: " . $this->demande->code,
            "title_ar" => "رفض طلب الاسترداد",
            "subtitle_ar" => "رفض طلب الاسترداد" ,//: " . $this->demande->code,
            "avatarIcon" => "report-money",
            "avatarAlt" => "DemandeRecouvrement",
            "avatarText" => "DemandeRecouvrement",
            "avatarColor" => "error",
            "type" => "demanderecouvrement",
            "target_id" => $this->demandeRecouvrement->id,
            "target" => "demanderecouvrement",
            "model" => "demanderecouvrement",
            //"url" => "",
            "url" => "/demande_recouvrement/".$this->demandeRecouvrement->type

        ];
    }
}
