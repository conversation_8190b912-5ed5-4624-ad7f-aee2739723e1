<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttestationTypeHasOfficesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 1,
            'attestation_type_id' => 5
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 1,
            'attestation_type_id' => 2
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 2,
            'attestation_type_id' => 2
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 3,
            'attestation_type_id' => 2
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 1,
            'attestation_type_id' => 3
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 3,
            'attestation_type_id' => 1
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 4,
            'attestation_type_id' => 1
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 1,
            'attestation_type_id' => 1
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 2,
            'attestation_type_id' => 1
        ]);

        DB::table('attestation_type_has_offices')->insert([
            'office_id' => 1,
            'attestation_type_id' => 4
        ]);
    }
}
