<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bourse_alternances', function (Blueprint $table) {
            $table->string('student_name')->nullable();
            $table->string('student_cin')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bourse_alternances', function (Blueprint $table) {
            $table->dropColumn('student_name');
            $table->dropColumn('student_cin');
        });
    }
};
