<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('document_classification_demandes', function (Blueprint $table) {
            $table->id();
            $table->string('document_file')->nullable();
            $table->string('etat')->nullable();
            $table->boolean('active')->nullable();
            $table->unsignedBigInteger('document_classification_id')->nullable();
            $table->unsignedBigInteger('demande_id')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('document_classification_id','demande_document_classification_id_foreign')
                ->references('id')
                ->on('document_classifications');
            $table->foreign('demande_id')
                ->references('id')
                ->on('demandes');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('documents_classifications_demandes');
    }
};
