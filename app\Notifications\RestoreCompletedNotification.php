<?php

namespace App\Notifications;

use App\Models\ExportedFile;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class RestoreCompletedNotification extends Notification
{
    use Queueable;

    public $syncCount;

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
        } catch ( \Exception $e) {
        }
//        $syncCount = ['decision_favorable' => 0, 'decision_non_favorable' => 0, 'decision_ambigu' => 0];

        return [
            "title" => "Restore Completed",
            "subtitle" => " Restore Completed , at : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_fr" => "Restore Complete ",
            "subtitle_fr" => " Restore Complete à : " .  Carbon::now()->format('d/m/y H:i:s'),
            "title_ar" => "اكتملت الاستعادة",
            "subtitle_ar" => " اكتملت الاستعادة : " .  Carbon::now()->format('d/m/y H:i:s'),
            "avatarIcon" => "recycle",
            "avatarAlt" => "Restore",
            "avatarText" => "Restore",
            "avatarColor" => "info",
            "type" => "Restore",
            "target_id" => '',
            "target" => '',
            "model" => "Preparation",
            "url" => '',

        ];
    }
}
