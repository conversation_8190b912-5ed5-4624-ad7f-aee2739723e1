<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('type_score')->nullable();
            $table->string('situation_etudiant')->nullable();
            $table->string('situation_familiale_dece')->nullable();
            $table->string('situation_familiale_divorce')->nullable();
            $table->string('situation_familiale_handicap')->nullable();
            $table->string('compare_resultat')->nullable();
            $table->string('score_total')->nullable();
            $table->integer('profession_final_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('type_score');
            $table->dropColumn('situation_etudiant');
            $table->dropColumn('situation_familiale_dece');
            $table->dropColumn('situation_familiale_divorce');
            $table->dropColumn('situation_familiale_handicap');
            $table->dropColumn('compare_resultat');
            $table->dropColumn('score_total');
            $table->dropColumn('profession_final_id');
        });
    }
};
