<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DatabaseBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:backup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automating Daily Backups';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (! Storage::exists('backup')) {
            Storage::makeDirectory('backup');
        }

        $filename = "backup-admin-" . Carbon::now()->format('Y-m-d') . ".sql.gz";

        $command = "mysqldump --column-statistics=0 --user=" . config('database.connections.mysql.username') ." --password=" . config('database.connections.mysql.password')
            . " --host=" . config('database.connections.mysql.host') . " " . config('database.connections.mysql.database')
          . " --ignore-table=". config('database.connections.mysql.database') .".personal_access_tokens"
//            . " --ignore-table=". config('database.connections.mysql.database') .".historiques --ignore-table=". config('database.connections.mysql.database') .".audits --ignore-table=". config('database.connections.mysql.database') .".mandates --ignore-table=". config('database.connections.mysql.database') .".orientations --ignore-table=". config('database.connections.mysql.database') .".student_from_mes"
            . "  | gzip > " . storage_path() . "/app/backup/" . $filename;

        $returnVar = NULL;
        $output  = NULL;

        exec($command, $output, $returnVar);
    }
}
