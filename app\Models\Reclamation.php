<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class Reclamation extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'reclamation_type_id',
        'detail',
        'response',
        'etat',
        'document_file',
        'document_file2',
        'student_id',
    ];

    public $appends=[
        'document_file_url',
        'document_file_url2',
    ];

    public function getDocumentFileUrlAttribute(){
        if ( $this->document_file ) {
            return asset('uploads/document_reclamation/'.$this->id.'/'.$this->document_file);
        }
        return null;
    }

    public function getDocumentFileUrl2Attribute(){
        if ( $this->document_file2 ) {
            return asset('uploads/document_reclamation/'.$this->id.'/'.$this->document_file2);
        }
        return null;
    }
    public function student() : BelongsTo
    {
        return $this->setConnection( config('database.secondConnection') )->belongsTo(User::class, 'student_id');
    }

    public function reclamationType() : BelongsTo
    {
        return $this->belongsTo(ReclamationType::class, 'reclamation_type_id');
    }
}
