<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Catb extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }
    protected $fillable = [
        'code',
        'name',
        'tunisien',
        'troisieme_cycle',
        'active',
        'type_etude',
    ];

    protected $casts = [
        'active' => 'boolean',
        'troisieme_cycle' => 'boolean',
        'tunisien' => 'boolean',
    ];



    public function demandes() : HasMany
    {
        return $this->hasMany(Demande::class,'code_catb','code');
    }
}
