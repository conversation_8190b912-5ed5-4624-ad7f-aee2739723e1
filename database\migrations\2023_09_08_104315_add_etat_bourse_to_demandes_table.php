<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->integer('etat_bourse')->nullable();
            $table->integer('etat_bourse_insertion')->nullable();
            $table->integer('etat_pret')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('etat_bourse');
            $table->dropColumn('etat_bourse_insertion');
            $table->dropColumn('etat_pret');

        });
    }
};
