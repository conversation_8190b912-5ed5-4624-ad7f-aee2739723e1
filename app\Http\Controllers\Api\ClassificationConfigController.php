<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\ClassificationMicroResource;
use App\Http\Resources\ClassificationResource;
use App\Models\Classification;
use App\Http\Requests\StoreClassificationRequest;
use App\Http\Requests\UpdateClassificationRequest;
use App\Models\DemandeType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class ClassificationConfigController extends Controller
{
    public function __construct()
    {
//        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $type = null;
        if ($request->demande_type_id){
            $type = DemandeType::find($request->demande_type_id);
        }
        $typeIds = [];
        $classifications = Classification::with('parent', 'demandeType','profession');
        if ($type) {
            $typeIds[] = $type->id;
            if ($type->fils) {
                foreach ($type->fils as $child) {
                    $typeIds[] = $child->id;
                    if ($child->fils) {
                        foreach ($child->fils as $childd) {
                            $typeIds[] = $childd->id;
                            if ($childd->fils) {
                                foreach ($childd->fils as $childdd) {
                                    $typeIds[] = $childdd->id;
                                }
                            }
                        }
                    }
                }
            }
            $classifications = $classifications->whereIn('demande_type_id', $typeIds);
        }
        if ($request->filled('active') && ($request->active == 1 || $request->active == 0)) {
            $classifications = $classifications->where('active', (bool)$request->active);
        }
        if ($request->filled('classable') && ($request->classable == 1 || $request->classable == 0)) {
            $classifications = $classifications->where('classable', (bool)$request->classable);
        }
        if ($request->filled('classable_par_admin') && ($request->classable_par_admin == 1 || $request->classable_par_admin == 0)) {
            $classifications = $classifications->where('classable_par_admin', (bool)$request->classable_par_admin);
        }
//        return ClassificationResource::collection($classifications->get());
        return response()->json([
//            "allData"=> ClassificationResource::collection($allData->get()),
            "data"=> ClassificationMicroResource::collection($classifications->get()),
        ],200);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param StoreClassificationRequest $request
     * @return Response
     */
    public function store(StoreClassificationRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();

        $classification = Classification::create($data);

        return response(new ClassificationResource($classification) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateClassificationRequest $request
     * @param Classification $classification
     * @return Response
     */
    public function edit(UpdateClassificationRequest $request,Classification $classification): Response
    {
        $data = $request->validated();
        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        //Helpers::clearCacheIdp();

        $classification->update($data);

        return response(new ClassificationResource($classification) , 201);

    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateClassificationRequest $request
     * @param Classification $classification
     * @return Response
     */
    public function editDocuments(UpdateClassificationRequest $request,Classification $classification): Response
    {
        $data = $request->validated();

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();

        $classification->update($data);

        return response(new ClassificationResource($classification) , 201);

    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateClassificationRequest $request
     * @param Classification $classification
     * @return Response
     */
    public function update(UpdateClassificationRequest $request, Classification $classification): Response
    {
        $data = $request->validated();

        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();

        $classification->update($data);

        return response(new ClassificationResource($classification) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Classification $classification
     * @return Response
     */
    public function destroy(Classification $classification)
    {
        Cache::forget('Classification');
        Cache::forget('ClassificationTree');
        Helpers::clearCacheIdp();

        $classification->delete();
        return response("ok", 204);
    }
}
