<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreChampPredefiniRequest;
use App\Http\Requests\UpdateChampPredefiniRequest;
use App\Http\Resources\ChampPredefiniResource;
use App\Models\ChampPredefini;
use App\Models\ConfigDemandeType;
use App\Models\DemandeType;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class ChampPredefiniController extends Controller
{
    protected $cache_seconds = 900;

    public function __construct()
    {
        // $this->middleware('permission:product-create', ['only' => ['create','store']]);
        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('ChampPredefini', $this->cache_seconds, function () {
            return ChampPredefiniResource::collection(ChampPredefini::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreChampPredefiniRequest $request
     * @return Response
     */
    public function store(StoreChampPredefiniRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('ChampPredefini');
        Helpers::clearCacheIdp();

        $champPredefini = ChampPredefini::create($data);

        return response(new ChampPredefiniResource($champPredefini) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateChampPredefiniRequest $request
     * @param ChampPredefini $champPredefini
     * @return Response
     */
    public function edit(UpdateChampPredefiniRequest $request,ChampPredefini $champPredefini): Response
    {
        $data = $request->validated();

        Cache::forget('ChampPredefini');
        Helpers::clearCacheIdp();

        $demandesTypes = DemandeType::all();


        foreach ($demandesTypes as $demandeType) {
            $config = json_decode($demandeType->lastConfig?->config, true);
            if ($config){
                foreach ($config as $keyRow => $valueRow) {
                    if ($valueRow['type'] === 'row') {
                        if (isset($valueRow['children']) && count($valueRow['children'])) {
                            foreach ($valueRow['children'] as $keyColumn => $valueColumn) {
                                if ($valueColumn['type'] === 'column') {
                                    if (isset($valueColumn['children']) && count($valueColumn['children'])) {
                                        foreach ($valueColumn['children'] as $keyComponent => $valueComponent) {
                                            if ($valueComponent['type'] === 'component') {
                                                if (isset($valueComponent['config']['champPredefiniId']) &&
                                                    count($valueComponent['config']) &&
                                                    $valueComponent['config']['champPredefiniId'] == $champPredefini->id)
                                                {
                                                    if ($data['type'] === 'dropdown') {
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['name'] = $data['name'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label'] = $data['label'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_fr'] = $data['label_fr'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_ar'] = $data['label_ar'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['showLabel'] = $data['showLabel'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['required'] = $data['required'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['affectClassification'] = $data['affectClassification'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['choices'] = $data['choices'];
                                                    }
                                                    if ($data['type'] === 'radioBox') {
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['name'] = $data['name'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label'] = $data['label'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_fr'] = $data['label_fr'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_ar'] = $data['label_ar'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['showLabel'] = $data['showLabel'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['required'] = $data['required'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['affectClassification'] = $data['affectClassification'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['choices'] = $data['choices'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['showInRow'] = $data['showInRow'];
                                                    }
                                                    if ($data['type'] === 'label') {
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['name'] = $data['name'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label'] = $data['label'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_fr'] = $data['label_fr'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_ar'] = $data['label_ar'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['help'] = $data['help'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['help_ar'] = $data['help_ar'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['help_fr'] = $data['help_fr'];
                                                    }
                                                    if ($data['type'] === 'date' || $data['type'] === 'textInput') {
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['name'] = $data['name'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label'] = $data['label'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_fr'] = $data['label_fr'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['label_ar'] = $data['label_ar'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['showLabel'] = $data['showLabel'];
                                                        $config[$keyRow]['children'][$keyColumn]['children'][$keyComponent]['config']['required'] = $data['required'];
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
                ConfigDemandeType::create(
                    ['config' => json_encode($config), 'logic' => $demandeType->lastConfig->logic, 'demande_type_id' => $demandeType->id]
                );
//                $demandeType->config = json_encode($config);
//                $demandeType->save();
            }
        }

        $champPredefini->update($data);

        return response(new ChampPredefiniResource($champPredefini) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ChampPredefini $champ_predefini
     * @return Response
     */
    public function destroy(ChampPredefini $champ_predefini)
    {
        Cache::forget('ChampPredefini');
        Helpers::clearCacheIdp();

        $champ_predefini->delete();
        return response("ok", 204);
    }
}
