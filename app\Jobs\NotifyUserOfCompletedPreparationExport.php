<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Models\ExportedFile;
use App\Notifications\ExportCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedPreparationExport
{
    use Queueable, SerializesModels;

    public $user;
    public $type;
    public $id;
    public $ndec_code;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(?Admin $user, ?string $type , ?int $id , ?string $ndec_code)
    {
        $this->user = $user;
        $this->type = $type;
        $this->id = $id;
        $this->ndec_code = $ndec_code;
    }

    public function handle()
    {

        if($this->id != null){
            $row = ExportedFile::find($this->id);
            $row->etat = 'termine';
            $row->attached_file = 'preparation_' . $this->ndec_code.'_'.$this->type . '_' .$this->id.'.xlsx';
            $row->save();
            $this->user->notify(new ExportCompletedNotification($row));
        }
    }
}
