<?php

namespace App\Models;

use App\Traits\AuditableRelatedModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;

class ReclamationType extends Model implements Auditable
{
    use AuditableTrait, AuditableRelatedModels{
        AuditableTrait::transformAudit as parentTransformAudit;
    }

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'code',
        'title',
        'title_fr',
        'title_ar',
        'parent_id',
    ];

    public function parent() : BelongsTo
    {
        return $this->belongsTo(__CLASS__, 'parent_id');
    }
    public function fils(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id');
    }
}
