<?php
namespace App\Exports;

use App\Models\DiplomeEtablissement;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class EtablissementDiplomeImport implements ToCollection, WithHeadingRow
{
    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'code_etab' => 'required',
                'code_diplome' => 'required',
            ],
            [],
            [
                'code_etab' => '(code_etab)',
                'code_diplome' => '(code_diplome)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.code_etab' => 'required|string',
            '*.code_diplome' => 'required|string',
        ])->validate();

        foreach ($rows as $row) {
            $gov = DiplomeEtablissement::where('code_diplome', $row['code_diplome'])->where('code_etab', $row['code_etab'])->first();
            if (!$gov){
                DiplomeEtablissement::create([
                    'code_diplome' => $row['code_diplome'],
                    'code_etab' => $row['code_etab'],
                ]);
            }
        }
    }
}
