# BPAS - Système de Gestion des Bourses, Prêts et Aides Sociales

## Aperçu du Projet

BPAS (Bourses, Prêts et Aides Sociales) est une application web développée avec <PERSON> (backend) et React (frontend) qui permet la gestion complète des bourses, prêts et aides sociales pour les étudiants. Le système offre une plateforme centralisée pour la soumission, le traitement et le suivi des demandes d'aide financière, ainsi que la gestion des attestations, des mandats et des décisions.

## Aspects Généraux

Le système BPAS est conçu pour répondre aux besoins des différents acteurs impliqués dans le processus d'attribution des aides financières aux étudiants :

- **Étudiants** : Peuvent soumettre des demandes d'aide financière, suivre l'état de leurs demandes, télécharger des documents et recevoir des notifications.
- **Administrateurs** : Peuvent gérer les demandes, traiter les dossiers, générer des rapports et des statistiques, et gérer les utilisateurs.
- **Établissements** : Peuvent consulter les informations relatives à leurs étudiants et aux aides accordées.

Le système prend en charge plusieurs types d'aides financières :
- Bourses d'études
- Bourses d'insertion
- Bourses de stage
- Prêts étudiants
- Aides sociales

## Guide d'Installation

### Prérequis

- PHP 8.1 ou supérieur
- Composer
- Node.js et npm
- MySQL ou MariaDB
- Serveur web (Apache, Nginx)
- Git

### Étapes d'Installation

1. **Cloner le dépôt**

```bash
git clone [URL_DU_DEPOT]
cd bpas_back
```

2. **Installer les dépendances PHP**

```bash
composer install
```

3. **Configurer l'environnement**

```bash
cp .env.example .env
php artisan key:generate
```

4. **Configurer la base de données**

Modifiez le fichier `.env` pour configurer la connexion à la base de données :

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bpas
DB_USERNAME=root
DB_PASSWORD=
```

5. **Migrer et alimenter la base de données**

```bash
php artisan migrate
php artisan db:seed
```

6. **Configurer le stockage**

```bash
php artisan storage:link
```

7. **Installer les dépendances JavaScript**

```bash
npm install
npm run build
```

8. **Démarrer le serveur de développement**

```bash
php artisan serve
```

9. **Configurer le serveur Socket.io (pour les notifications en temps réel)**

```bash
cd socket
npm install
npm start
```

## Fonctionnalités Clés

### Gestion des Utilisateurs
- Inscription et authentification des utilisateurs
- Gestion des rôles et des permissions
- Profils utilisateurs avec informations personnelles et académiques

### Gestion des Demandes
- Soumission de demandes d'aide financière
- Suivi de l'état des demandes
- Téléchargement et gestion des documents requis
- Traitement des demandes par les administrateurs
- Classification des demandes par type et état

### Gestion des Attestations
- Génération d'attestations pour les étudiants
- Différents types d'attestations disponibles
- Téléchargement et impression des attestations

### Gestion des Mandats et Décisions
- Création et gestion des mandats de paiement
- Enregistrement des décisions d'attribution
- Suivi des paiements et des échéances

### Importation et Exportation de Données
- Importation de données à partir de fichiers Excel
- Exportation de rapports et de statistiques
- Génération de documents au format PDF et Word

### Statistiques et Rapports
- Tableaux de bord avec indicateurs clés
- Rapports statistiques par année, établissement, type d'aide, etc.
- Visualisation graphique des données

### Notifications
- Notifications en temps réel via Socket.io
- Notifications par email
- Centre de notifications dans l'application

## Architecture Technique

### Backend
- **Framework** : Laravel 9
- **Base de données** : MySQL/MariaDB
- **API** : RESTful API avec Laravel Sanctum pour l'authentification
- **Traitement des fichiers** : Spatie Media Library, Laravel Excel
- **Génération de PDF** : mPDF, DomPDF
- **Notifications** : Socket.io, Laravel Notifications

### Frontend (séparé)
- **Framework** : React / NextJs
- **Gestion d'état** : Redux
- **UI** : Material UI 5.11.4
- **Communication API** : Axios

### Sécurité
- Authentification basée sur les tokens (Laravel Sanctum)
- Gestion des rôles et permissions (Spatie Laravel Permission)
- Validation des données côté serveur
- Protection CSRF
- Journalisation des audits (Laravel Auditing)

## Structure de la Base de Données

Le système utilise une base de données relationnelle avec plus de 50 tables. Les principales tables incluent :

- **Users** : Informations des utilisateurs (étudiants, administrateurs)
- **Demandes** : Demandes d'aide financière
- **Attestations** : Attestations générées pour les étudiants
- **Mandates** : Mandats de paiement
- **Decisions** : Décisions d'attribution d'aide
- **Annee_Universitaires** : Années universitaires
- **Etablissements** : Établissements d'enseignement
- **Documents** : Documents associés aux demandes

Pour une description détaillée de la structure de la base de données, consultez le fichier `structure_base_de_donnees.md`.

## Configuration

### Variables d'Environnement

Le système utilise plusieurs variables d'environnement pour sa configuration. Les principales sont :

- `APP_NAME` : Nom de l'application
- `APP_ENV` : Environnement (local, production)
- `DB_CONNECTION`, `DB_HOST`, `DB_PORT`, `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD` : Configuration de la base de données
- `MAIL_*` : Configuration du serveur de messagerie
- `SOCKET_URL`, `SOCKET_NOTIFY` : Configuration du serveur Socket.io

### Système de Fichiers

Le système utilise plusieurs disques de stockage :

- `local` : Stockage local pour les fichiers temporaires
- `public` : Stockage public accessible via le web
- `second_disk` : Disque secondaire pour certains types de fichiers

## Maintenance et Support

### Journalisation

Le système utilise Laravel Telescope pour la journalisation et le débogage en environnement de développement.

### Sauvegarde

Il est recommandé de configurer des sauvegardes régulières de la base de données et des fichiers téléchargés.

### Mise à Jour

Pour mettre à jour le système :

```bash
git pull
composer install
php artisan migrate
npm install
npm run build
php artisan optimize:clear
```

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.
