<?php

namespace App\Http\Requests;

use App\Rules\InOfficeCenter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;

class StoreWaitingStudentRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $secondConnection = config('database.secondConnection');
        if ($this->type === 'tunisien') {
            //    branch tunisien
            return [
                'cin' => 'required|digits:8|unique:'.$secondConnection.'users',
                'num_bac' => 'required|digits:6',
                'name' => 'required|max:50',
                'name_ar' => 'sometimes|nullable|max:50',
                'code_postal' => 'required',
                'annee_bac' => 'required',
                'nationality_id' => 'required',
                'code_gouv' => 'required',
                'email_perso' => 'nullable|email',
                'phoneNumber' => 'required|digits:8',
                'phoneNumber2' => 'nullable|min:8',
                'address' => 'required',
                'pere' => 'nullable',
                'mere' => 'nullable',
                'filiere_id' => 'nullable',
                'code_etab' => ['required'],
                'etablissement' => ['required', new InOfficeCenter],
                'annee_etude' => 'required',
                'code_diplome' => 'required',

                'annee_universitaire_id' => 'required',
            ];

        } else {
            //    branch etranger

            return [
                'num_passport' => 'required',
                'matricule' => 'required|max:8|min:8|unique:'.$secondConnection.'users,matricule',
                'name' => 'required|max:40',
                'firstName' => 'sometimes|nullable|max:40',
                'code_postal' => 'required',
                'annee_bac' => 'required',
                'nationality_id' => 'required',
                'code_gouv' => 'sometimes|nullable',
                'email_perso' => 'nullable|email',
                'phoneNumber' => 'required|digits:8',
                'phoneNumber2' => 'nullable|min:8',
                'address' => 'required',
                'pere' => 'nullable',
                'mere' => 'nullable',
                'filiere_id' => 'nullable',
                'code_etab' => ['required'],
                'etablissement' => ['required', new InOfficeCenter],
                'annee_etude' => 'required',
                'code_diplome' => 'required',
                'annee_universitaire_id' => 'required',
            ];
        }

    }
}
