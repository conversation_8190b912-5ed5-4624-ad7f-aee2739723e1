<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLyceeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'name_fr' => 'required|string|max:100',
            'name_ar' => 'required|string|max:100',
            'code_gouv' => 'required|integer',
            'code' => 'required|integer|unique:lycees,code',
            ];
    }
}
