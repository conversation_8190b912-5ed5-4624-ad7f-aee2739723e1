<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demande_types', function (Blueprint $table) {
            $table->boolean('bourse')->default(true)->nullable();
            $table->boolean('aide_sociale')->default(false)->nullable();
            $table->boolean('bourses_de_stage')->default(false)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demande_types', function (Blueprint $table) {
            $table->dropColumn('bourse');
            $table->dropColumn('aide_sociale');
            $table->dropColumn('bourses_de_stage');

        });
    }
};
