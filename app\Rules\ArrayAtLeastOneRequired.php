<?php

namespace App\Rules;

use App\Models\Etablissement;
use Illuminate\Contracts\Validation\InvokableRule;

class ArrayAtLeastOneRequired implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail): void
    {
        $r = false;
        foreach ($value as $arrayElement) {
            if (null !== $arrayElement) {
                $r = true;
            }
        }

        if (!$r) {
            $fail('validation.array_at_least_one_required')->translate();
        }
    }
}
