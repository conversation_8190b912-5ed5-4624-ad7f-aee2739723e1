<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DocumentClassificationDemandeResource;
use App\Http\Resources\DocumentClassificationResource;
use App\Models\Demande;
use App\Models\DocumentClassificationDemande;
use File;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class DocumentDemandeController extends Controller
{
    public function __construct()
    {
//        $this->middleware('permission:product-create', ['only' => ['create','store']]);

        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Demande $demande
     * @return AnonymousResourceCollection
     */
    public function documentsByDemande(Demande $demande): AnonymousResourceCollection
    {

        return DocumentClassificationDemandeResource::collection(DocumentClassificationDemande::where('demande_id', $demande->id)->get());
    }

    public function edit(Request $request,DocumentClassificationDemande $document): Application|ResponseFactory|Response|JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'document_file'=> 'required|mimes:png,jpg,jpeg,csv,txt,xlx,xls,xlsx,pdf',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }
        if($request->hasFile('document_file')){
            if($document->document_file){
                $old_path='/uploads/document_files/'.$document->demande->id.'/'.$document->document_file;
                if(Storage::exists($old_path)){
                    Storage::delete($old_path);
                }
            }

            $file = $request->file('document_file');
            $file_name='document_'.time().'.'.$request->document_file->extension();

            $file->storeAs('/uploads/document_files/'.$document->demande->id.'/', $file_name);

        } else {
            $file_name=$document->document_file;
        }
        $document->update([
            'document_file' => $file_name
        ]);

        return response(new DocumentClassificationResource($document) , 201);

    }

    public function getDemandeDocument(Request $request)
    {
        $request->validate([
            'id' => 'required',
        ]);

        $user = Auth::user();
        $doc =DocumentClassificationDemande::findOrFail($request->id);
        if (!$doc) {
            return response()->json('Document not found',404);
        }

        $filename = $doc->document_file;
        if (!Storage::exists('/uploads/document_files/'.$doc->demande_id.'/'. $filename)) {
            return response()->json(Storage::url('/uploads/document_files/'.$doc->demande_id.'/'. $filename), 404);
        }
        return Storage::download('/uploads/document_files/'.$doc->demande_id.'/'. $filename, $filename);

    }
}
