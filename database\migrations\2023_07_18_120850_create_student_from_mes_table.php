<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_from_mes', function (Blueprint $table) {
            $table->id();
            $table->string('NBAC')->nullable();
            $table->string('CIN')->nullable();
            $table->string('NOM_A')->nullable();
            $table->string('NOM_L')->nullable();
            $table->string('JJ')->nullable();
            $table->string('MM')->nullable();
            $table->string('AA')->nullable();
            $table->string('CD_LYC')->nullable();
            $table->string('CD_GOUV')->nullable();
            $table->string('SEX')->nullable();
            $table->string('PROF')->nullable();
            $table->string('CODE_FILIERE')->nullable();
            $table->string('TOUR')->nullable();
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_from_mes');
    }
};
