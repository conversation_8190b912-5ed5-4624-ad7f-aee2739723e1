<?php

namespace App\Http\Controllers\Api;

use App\Exports\ReclamationExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreReclamationRequest;
use App\Http\Requests\UpdateReclamationRequest;
use App\Http\Resources\ReclamationResource;
use App\Models\DocumentsAttestationUpload;
use App\Models\Reclamation;
use App\Models\User;
use App\Notifications\ReclamationNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ReclamationController extends Controller
{
    public function __construct()
    {
        // $this->middleware('permission:product-create', ['only' => ['create','store']]);
        // $this->middleware('role:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse|null
     */
    public function index(Request $request): ?JsonResponse
    {
        $reclamations = Reclamation::query();
        if ($request->has('q') && $request->q ) {
            $reclamations = $reclamations->where(function ($query) use ($request) {
                $query->where('detail', 'like', '%' . $request->q . '%');
            });
        }

        if ($request->q) {

            $userIds = User::where(function (Builder $query) use ($request) {
                $query
                    ->orWhere('email', 'like', '%' . $request->q . '%')
                    ->orWhere('name', 'like', '%' . $request->q . '%')
                    ->orWhere('name_ar', 'like', '%' . $request->q . '%')
                    ->orWhere('firstName', 'like', '%' . $request->q . '%')
                    ->orWhere('cin', 'like', '%' . $request->q . '%')
                    ->orWhere('num_bac', 'like', '%' . $request->q . '%')
                    ->orWhere('matricule', 'like', '%' . $request->q . '%')
                    ->orWhere('num_passport', 'like', '%' . $request->q . '%')
                ;
            })->pluck('id');

            $reclamations = $reclamations->where(function (Builder $query) use ($request,$userIds) {
                $query
                    ->orWhere('detail','like', '%' . $request->q . '%')
                    ->orWhereIn('student_id', $userIds);
            });
        }
        if ($request->has('reclamation_type_id') && $request->reclamation_type_id) {
            $reclamations = $reclamations->where('reclamation_type_id', $request->reclamation_type_id);
        }
        if ($request->has('etat') && $request->etat) {
            $reclamations = $reclamations->where('etat', $request->etat);
        }
        if ($request->has('name') && $request->name) {

            $userIds = User::where(function (Builder $query) use ($request) {
                $query
                    ->orWhere('email', 'like', '%' . $request->name . '%')
                    ->orWhere('name', 'like', '%' . $request->name . '%')
                    ->orWhere('name_ar', 'like', '%' . $request->name . '%')
                    ->orWhere('firstName', 'like', '%' . $request->name . '%')
                    ->orWhere('cin', 'like', '%' . $request->name . '%')
                    ->orWhere('num_bac', 'like', '%' . $request->name . '%')
                    ->orWhere('matricule', 'like', '%' . $request->name . '%')
                    ->orWhere('num_passport', 'like', '%' . $request->name . '%')
                ;
            })->pluck('id');

            $reclamations = $reclamations->WhereIn('student_id', $userIds);
        }
        /** Adding Order By */
        if ($request->orderByColumn && $request->orderByDirection) {
            $reclamations = $reclamations->orderBy($request->orderByColumn, $request->orderByDirection);
        } else {
            $reclamations = $reclamations->orderBy('id', 'desc');
        }

        $reclamations = $reclamations->with('student' , 'reclamationType');
//        return response()->json($reclamations->toSql(), 200);
        return response()->json($reclamations->paginate(
            $request->input('perPage') ?? config('constants.pagination'),
        ), 200);

    }

    /**
     * Display a listing of the resource.
     *
     * @param Reclamation $reclamation
     * @return ReclamationResource
     */
    public function show(Reclamation $reclamation): ReclamationResource
    {
        return new ReclamationResource($reclamation);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreReclamationRequest $request
     * @return Response
     */
    public function store(StoreReclamationRequest $request): Response
    {
        $data = $request->validated();

        $reclamation = Reclamation::create($data);
        $file_name = null;
        if($request->hasFile('document_file')){
            $file = $request->file('document_file');
            $file_name='document_reclamation_'.time().'.'.$request->document_file->extension();
            $file->move(public_path('/uploads/document_reclamation/'.$reclamation->id),$file_name);
        }
        $reclamation->update([
            'document_file' => $file_name
        ]);
        $file_name2 = null;
        if($request->hasFile('document_file2')){
            $file = $request->file('document_file2');
            $file_name2='document2_reclamation_'.time().'.'.$request->document_file2->extension();
            $file->move(public_path('/uploads/document_reclamation/'.$reclamation->id),$file_name);
        }
        $reclamation->update([
            'document_file2' => $file_name2
        ]);
        return response(new ReclamationResource($reclamation) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateReclamationRequest $request
     * @param Reclamation $reclamation
     * @return Response
     */
    public function edit(UpdateReclamationRequest $request,Reclamation $reclamation): Response
    {
        $data = $request->validated();
        if($request->hasFile('document_file')){
            if($reclamation->document_file){
                $old_path='/uploads/document_reclamation/'.$reclamation->id.'/'.$reclamation->document_file;
                if(File::exists($old_path)){
                    File::delete($old_path);
                }
            }


            $file = $request->file('document_file');
            $file_name='document_reclamation_'.time().'.'.$request->document_file->extension();
            $file->move(public_path('/uploads/document_reclamation/'.$reclamation->id),$file_name);

        }else{
            $file_name=$reclamation->document_file;
        }
        $data['document_file'] = $file_name;

        if($request->hasFile('document_file2')){
            if($reclamation->document_file2){
                $old_path='/uploads/document_reclamation/'.$reclamation->id.'/'.$reclamation->document_file2;
                if(File::exists($old_path)){
                    File::delete($old_path);
                }
            }


            $file = $request->file('document_file2');
            $file_name2='document2_reclamation_'.time().'.'.$request->document_file2->extension();
            $file->move(public_path('/uploads/document_reclamation/'.$reclamation->id),$file_name2);

        }else{
            $file_name2=$reclamation->document_file2;
        }
        $data['document_file2'] = $file_name2;
        $reclamation->update($data);
        if ($reclamation->etat === 'non_favorable' ||  $reclamation->etat === 'favorable'  ) {
            Notification::send([$reclamation->student], new ReclamationNotification($reclamation));
        }

        return response(new ReclamationResource($reclamation) , 201);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Reclamation $champ_predefini
     * @return Response
     */
    public function destroy(Reclamation $champ_predefini)
    {
        $champ_predefini->delete();
        return response("ok", 204);
    }


    public function getReclamationDocument(Request $request)
    {

        $request->validate([
            'id' => 'required',
            'document_type' => 'required',
        ]);

        $reclamation = Reclamation::findOrFail($request->id);
        if ($request->document_type === 'document_file') {
            $filename = $reclamation->document_file;
        }
        if ($request->document_type === 'document_file2') {
            $filename = $reclamation->document_file2;
        }
        //dd($filename);
        $path = storage_path('app/uploads/document_reclamation/' . $reclamation->id.'/'. $filename);
        //dd($path);
        if (!File::exists($path)) {
            return response()->json(Storage::path($path), 404);
        }
//
//        $file = File::get($path);
//        $type = File::mimeType($path);

        return response()->download($path, $filename); //

    }


    public function exportExcel(Request $request)
    {
        $export = new ReclamationExport($request);
        return Excel::download($export, 'Reclamation.xlsx');
    }
}
