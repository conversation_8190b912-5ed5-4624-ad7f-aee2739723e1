<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('absences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id')->nullable();
            $table->unsignedBigInteger('etablissement_id')->nullable();
            $table->unsignedBigInteger('annee_universitaire_id')->nullable();

            $table->integer('nb_jours');

            $table->foreign('etablissement_id')
                ->references('id')
                ->on('etablissements');

            $table->foreign('annee_universitaire_id')
                ->references('id')
                ->on('annee_universitaires');

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('absences');
    }
};
