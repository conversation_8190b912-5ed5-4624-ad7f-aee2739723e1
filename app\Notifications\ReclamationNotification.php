<?php

namespace App\Notifications;

use App\Models\Attestation;
use App\Models\Reclamation;
use App\Socket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReclamationNotification extends Notification
{
    use Queueable;
    private Reclamation $reclamation;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Reclamation $reclamation)
    {
        $this->reclamation = $reclamation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

//    /**
//     * Get the mail representation of the notification.
//     *
//     * @param  mixed  $notifiable
//     * @return MailMessage
//     */
//    public function toMail($notifiable)
//    {
//        return (new MailMessage)
//                    ->line('The introduction to the notification.')
//                    ->action('Notification Action', url('/'))
//                    ->line('Thank you for using our application!');
//    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        try {
            Socket::sendNotification([
                "senderName"=>$notifiable->username ,
                "receiverName"=>$notifiable->username ,
            ]);
            //Log::debug("Sent notification via websocket");

        } catch ( \Exception $e) {
            //Log::debug("cannot send notification via websocket");
        }
        if ($this->reclamation->etat === 'non_favorable'  ) {
            return [
                "title" => "Your request for certification has been refused",
                "subtitle" => "Your reclamation for ".$this->reclamation->reclamationType->title." has been refused",//n°: ". $this->demande->code,
                "title_fr" => "Votre réclamation a été refusée",
                "subtitle_fr" => "Votre réclamation '" . $this->reclamation->reclamationType->title_fr."' a été refusée",//de bourse n°: " . $this->demande->code,
                "title_ar" => "تم رفض طلبك ",
                "subtitle_ar" => " تم رفض طلبك  ". $this->reclamation->reclamationType->title_ar,//: " . $this->demande->code,
                "avatarIcon" => "traffic-cone",
                "avatarAlt" => "Reclamation",
                "avatarText" => "Reclamation",
                "avatarColor" => "warning",
                "type" => "reclamation",
                "target_id" => $this->reclamation->id,
                "target" => "reclamation",
                "model" => "reclamation",
                "url" => "/reclamations/"//. $this->attestation->id,
            ];
        }
            return [
                "title" => "Your request for certification has been accepted",
                "subtitle" => "Your reclamation for ".$this->reclamation->reclamationType->title." has been accepted",//n°: ". $this->demande->code,
                "title_fr" => "Votre réclamation a été acceptée",
                "subtitle_fr" => "Votre réclamation '" . $this->reclamation->reclamationType->title_fr."' a été acceptée",//de bourse n°: " . $this->demande->code,
                "title_ar" => "تم قبول طلبك ",
                "subtitle_ar" => " تم قبول طلبك". $this->reclamation->reclamationType->title_ar,//: " . $this->demande->code,
                "avatarIcon" => "confetti",
                "avatarAlt" => "Reclamation",
                "avatarText" => "Reclamation",
                "avatarColor" => "success",
                "type" => "reclamation",
                "target_id" => $this->reclamation->id,
                "target" => "reclamation",
                "model" => "reclamation",
                "url" => "/reclamations/",
            ];
        return [];
    }
}
