<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('attestations', function (Blueprint $table) {
            $table->unsignedBigInteger('etablissement_id')->nullable();

            $table->string('year')->nullable();

            $table->date('date_enregistrement_bureau_ordre')->nullable();
            $table->string('reference_bureau_ordre')->nullable();

            $table->foreign('etablissement_id')
                ->references('id')
                ->on('etablissements');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('attestations', function (Blueprint $table) {
            $table->dropForeign(['etablissement_id']);
            
            $table->dropColumn('reference_bureau_ordre');
            $table->dropColumn('date_enregistrement_bureau_ordre');
            $table->dropColumn('year');
            
        });
    }
};
