<?php
namespace App;
use ElephantIO\Client;

class Socket
{
    public static function sendNotification($data): void
    {
        $socket_notify =  config('app.socket_notify') ?? false;
        if ($socket_notify){
            $options = [
                'context' => [
                    'ssl' => [
                        'verify_peer' => false,
                        'verify_peer_name' => false
                    ]
                ]
            ];
            $url = config('app.socket_url') ?? 'http://localhost:5000';
            $client = new Client(Client::engine(Client::CLIENT_2X, $url, $options));

            try {
                $client->initialize();
                $client->emit('sendNotification', $data);
                $client->of('/');
            } catch (\Exception $e) {
//            dump($e);
                $client->close();
            }
        }
    }
}
