<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use OwenIt\Auditing\Contracts\Auditable;


class DocumentClassificationDemande extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;


    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $with = [
        'documentClassification',
//        'demande',
    ];

    protected $fillable = [
        'document_file',
        'attached_file',
        'etat',
        'active',
        'dans_complement',
        'document_classification_id',
        'demande_id',
    ];

    protected $casts = [
        'active' => 'boolean',
        'dans_complement' => 'boolean',
    ];

//    public $appends=[
//        'document_file_url',
//    ];

    public function documentClassification() : BelongsTo
    {
        return $this->belongsTo(DocumentClassification::class, 'document_classification_id');
    }
    public function demande() : BelongsTo
    {
        return $this->belongsTo(Demande::class, 'demande_id');
    }

//    public function getDocumentFileUrlAttribute(){
//        if ( $this->demande && $this->document_file ) {
//            return asset('uploads/document_files/'.$this->demande?->id.'/'.$this->document_file);
//        }
//        return null;
//    }

}
