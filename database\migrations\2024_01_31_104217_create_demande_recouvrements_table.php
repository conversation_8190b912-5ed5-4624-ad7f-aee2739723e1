<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('demande_recouvrements', function (Blueprint $table) {
            $table->id();

            $table->string('type');
            $table->unsignedBigInteger('student_id');
            $table->string('annee_universitaire');
            $table->string('status')->default("en_cours");
            $table->integer('montant')->default(0);
            $table->string('commentaire')->nullable();
            $table->string('num_quittance')->nullable();

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('demande_recouvrements');
    }
};
