<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\SignupRequest;
use App\Models\Admin;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    public function signupVerification(Request $request): JsonResponse
    {
        if ($request->request->get('step') === "0") {
            $validator1 = Validator::make($request->all(), [
                'cin' =>  ['required', 'min:6', 'unique:users,cin'],
                'email' =>  ['required', 'email', 'unique:users,email'],
                'password' => [
                    'required',
                    Password::min(6)
                        ->letters()
                        ->symbols()
                        ->numbers()
                ],
                'confirmPassword' => 'required|same:password'
            ]);
            if ($validator1->fails()) {
                return response()->json([
                    'message' => 'Validations fails',
                    'errors' => $validator1->errors()
                ], 422);
            }

            return response()->json([
                'message' => 'success step 1',
            ], 200);
        }

        if ($request->request->get('step') === "1") {
            $validator1 = Validator::make($request->all(), [
                'name'=>'required|min:5|max:40',
                'firstName'=>'required|min:5|max:40',
                'country'=>'required',
                'delegation'=>'required',
                'phoneNumber'=>'required|min:8',
                'address'=>'required',
                'zipCode'=>'required|min:5|max:5',
            ]);
            if ($validator1->fails()) {
                return response()->json([
                    'message' => 'Validations fails',
                    'errors' => $validator1->errors()
                ], 422);
            }

            return response()->json([
                'message' => 'success step 1',
            ], 200);
        }
        return response()->json([
            'message' => 'not step support',
        ], 422);


    }
    public function signup(SignupRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'cin' =>  ['required', 'min:6', 'unique:users,cin'],
            'email' =>  ['required', 'email', 'unique:users,email'],
            'password' => [
                'required',
                Password::min(6)
                    ->letters()
                    ->symbols()
                    ->numbers()
            ],
            'confirmPassword' => 'required|same:password',
            'name'=>'required|min:5|max:40',
            'firstName'=>'required|min:5|max:40',
            'country'=>'required',
            'delegation'=>'required',
            'phoneNumber'=>'required|min:8',
            'address'=>'required',
            'zipCode'=>'required|min:5|max:5',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        $user=Admin::create([
            'firstName' => $request->name,
            'name' => $request->name,
            'username' => $request->cin,
            'cin'=>$request->cin,
            'email'=>$request->email,
            'password'=>Hash::make($request->password),
            'country_id'=>$request->country_id,
            'delegation_id'=>$request->delegation_id,
            'phoneNumber'=>$request->phoneNumber,
            'address'=>$request->address,
            'zipCode'=>$request->zipCode,
            'role'=>'client',
        ]);

        $token = $user->createToken('main')->plainTextToken;
        return response(compact('user', 'token'),200);


    }

    public function login(LoginRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'email'=>'required|email',
            'password'=>'required|min:6|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message'=>'Validations fails',
                'errors'=>$validator->errors()
            ],422);
        }

        $credentials = $request->validated();
        if (!Auth::attempt($credentials)) {
            return response()->json([
                'errors' => [
                    "email"=>[__('auth.failed')]
                ]
            ], 422);
        }

        // checking of user status (active or not)
        /** @var \App\Models\Admin $user */
        $user = Auth::user();
        if ($user->status == 0 ) {
            return response()->json([
                'errors' => [
                    "email"=>[__('auth.notActive')]
                ]
            ], 422);
        }

        // cheking for active role for a user
        // $hasActiveRole = false;
        // $roles = $user->roles;
        // foreach($roles as $role)
        // {
        //     if($user->hasRoleWithinPeriod($role)){
        //         $hasActiveRole = true;
        //     }else{
        //         // remove that role from the user
        //         // $user->removeRole($role) ;
        //     }
        // }
        // if ($hasActiveRole == true){

        if ($user->hasRoleToday()){
            $token = $user->createToken('main')->plainTextToken;
            return response(compact('user', 'token'));
        }else{
            return response()->json([
                'errors' => [
                    "email"=>[__('auth.notActiveRole')]
                ]
            ], 422);
        }

    }

    public function logout(Request $request)
    {
        /** @var \App\Models\Admin $user */
        $user = $request->user();
        $user->currentAccessToken()->delete();
        return response('', 204);
    }
}
