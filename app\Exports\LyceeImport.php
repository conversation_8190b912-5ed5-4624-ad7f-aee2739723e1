<?php
namespace App\Exports;

use App\Models\Lycee;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Validator;
use Illuminate\Support\Collection;

class LyceeImport implements ToCollection, WithHeadingRow
{

    public function collection(Collection $rows): void
    {
        Validator::make($rows[0]->toArray(),
            [
                'lib_lycee_ar' => 'required',
//                'lib_lycee_fr' => 'required',
                'code_lycee' => 'required',
                'code_gouv' => 'required',
            ],
            [],
            [
                'lib_lycee_ar' => '(lib_lycee_ar)',
//                'lib_lycee_fr' => '(lib_lycee_fr)',
                'code_lycee' => '(code_lycee)',
                'code_gouv' => '(code_gouv)',
            ]
        )->validate();

        Validator::make($rows->toArray(), [
            '*.lib_lycee_ar' => 'required|string',
//            '*.lib_lycee_fr' => 'required|string',
            '*.code_lycee' => 'required|numeric',
            '*.code_gouv' => 'required|numeric',
        ])->validate();

        foreach ($rows as $row) {
            $gov = Lycee::where('code', $row['code_lycee'])->first();
            if ($gov){
                $gov->update([
                    'name' => $row['lib_lycee_ar'],
                    'name_fr' => $row['lib_lycee_ar'],
                    'name_ar' => $row['lib_lycee_ar'],
                    'code_gouv' => $row['code_gouv'],
                ]);
            } else {
                Lycee::create([
                    'name'  => $row['lib_lycee_ar'],
                    'name_fr'  => $row['lib_lycee_ar'],
                    'name_ar'  => $row['lib_lycee_ar'],
                    'code' => $row['code_lycee'],
                    'code_gouv' => $row['code_gouv'],
                ]);
            }
        }
    }
}
