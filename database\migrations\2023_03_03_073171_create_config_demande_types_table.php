<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('config_demande_types', function (Blueprint $table) {
            $table->id()->autoIncrement();

            $table->json('config')->nullable();
            $table->json('logic')->nullable();
            $table->unsignedBigInteger('demande_type_id');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('demande_type_id')
                ->references('id')
                ->on('demande_types') ;
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('config_demande_types');
    }
};
