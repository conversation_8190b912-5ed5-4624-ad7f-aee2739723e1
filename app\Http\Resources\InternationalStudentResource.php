<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class InternationalStudentResource extends JsonResource
{
    public static $wrap = false;
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'num_passport' => $this->num_passport,
            'matricule' => $this->matricule,
            'name' => $this->name,
            'firstName' => $this->firstName,
            'phoneNumber' => $this->phoneNumber,
            'address' => $this->address,
            'zipCode' => $this->zipCode,
            'dateOfBirth' => $this->dateOfBirth,
            'placeOfBirth' => $this->placeOfBirth,
            'sex' => $this->sex,
            'nationality_id' => $this->nationality_id,
            'nationality' => $this->nationality,
            'code_etab' => $this->code_etab,
            'etablissement' => $this->etablissement,
            'filiere_id' => $this->filiere_id,
            'filiere' => $this->filiere,
            'foyer' => $this->foyer,
            'annee_bac' => $this->annee_bac,
            'anneeBac' => $this->anneeBac,
            'annee_universitaire' => $this->annee_universitaire,
            'anneeUniversitaire' => $this->anneeUniversitaire,

            'created_at' => $this->created_at->format('Y-m-d'),
        ];
    }
}
