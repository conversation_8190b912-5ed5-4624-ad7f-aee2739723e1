<?php
namespace App\Exports;

use App\Models\Filiere;
use App\Models\Orientation;
use App\Models\StudentFromMes;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithUpserts;
use Maatwebsite\Excel\Concerns\WithValidation;
use Validator;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

class StudentFromMesModelImport implements ToModel, WithHeadingRow,WithUpserts, WithValidation, WithBatchInserts, ShouldQueue,WithChunkReading

{
    use Importable;

    private int $annee_bac;
    private int $annee_universitaire;

    /**
     * @return string|array
     */
    public function uniqueBy(): array|string
    {
        return 'NBAC';
    }

    public function __construct($annee_bac,$annee_universitaire){
        $this->annee_bac = $annee_bac;
        $this->annee_universitaire = $annee_universitaire;
    }

    public function model(array $row): ?StudentFromMes
    {
        $st = StudentFromMes::updateOrCreate([
            'NBAC'=>$row['nbac'] ,
            'annee_bac'=>$this->annee_bac
        ], [
            'NBAC' => $row['nbac'],
            'CIN' => $row['cin'],
            'NOM_A' => $row['nom_a']??'',
            'NOM_L' => $row['nom_l']??'',
            'JJ' => $row['jj']??'',
            'MM' => $row['mm']??'',
            'AA' => $row['aa']??'',
            'CD_LYC' => $row['cd_lyc']??'',
            'CD_GOUV' => $row['cd_gouv']??'',
            'SEX' => $row['sex']??'',
            'PROF' => $row['prof']??'',
            'CODE_FILIERE' => $row['code_filiere']??'',
            'TOUR' => $row['tour']??'',
            'annee_bac' => $this->annee_bac,
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
        ]);

        if ($row['code_filiere'] && $row['code_filiere'] != '999999' && $row['tour']) {

            Orientation::updateOrCreate([
                'student_id'=>$st->id,
                'code_filiere' => $row['code_filiere'],
            ],[
                'student_id'=>$st->id,
                'annee_universitaire'=>$this->annee_universitaire,
                'code_filiere' => $row['code_filiere'],
                'tour' => $row['tour'],
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);
        }

        return $st;
    }

    public function batchSize(): int
    {
        return 400;
    }
    public function chunkSize(): int
    {
        return 400;
    }

    public function rules(): array
    {
        return [
            '*.nbac' => 'required',
            '*.cin' => 'required',
            '*.code_filiere' => 'required',
            '0' => function($attribute, $value, $onFailure) {
                if (!$this->annee_universitaire) {
                    $onFailure('Cette année de bac ne correspond à aucune année universitaire');
                }
            }
        ];
    }
//    public function failed(?\Throwable $exception): void
//    {
//        // Send user notification of failure, etc...
//        $this->importedBy->notify(new ImportHasFailedNotification($exception));
//    }
//    public function registerEvents(): array
//    {
//        return [
//            ImportFailed::class => function(ImportFailed $event) {
//                $this->importedBy->notify(new ImportHasFailedNotification($event));
//            },
//        ];
//    }
}
