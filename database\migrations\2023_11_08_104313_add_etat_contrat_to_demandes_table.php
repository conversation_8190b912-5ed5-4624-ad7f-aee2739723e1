<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('etat_contrat')->nullable();
            $table->text('comment_contrat_incomplet')->nullable();
            $table->date('control_fiscal_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('etat_contrat');
            $table->dropColumn('comment_contrat_incomplet');
            $table->dropColumn('control_fiscal_date');
        });
    }
};
