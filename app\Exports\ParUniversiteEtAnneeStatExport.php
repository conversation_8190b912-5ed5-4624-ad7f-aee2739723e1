<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
class ParUniversiteEtAnneeStatExport implements WithEvents, FromView
{
    protected $data;
    protected $year;
    protected $totals;
    protected $universite;
    protected $date_export;

    public function __construct($data,$year,$totals,$universite)
    {
        $this->data = $data;
        $this->year = $year;
        $this->totals = $totals;
        $this->universite = $universite;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');

    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }


    public function view(): View
    {
        return view('statistiques.parUniversiteEtAnnee', [
            'data' => $this->data,
            'year' => $this->year,
            'totals' => $this->totals,
            'universite' => $this->universite,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title
        ]);
    }
}

