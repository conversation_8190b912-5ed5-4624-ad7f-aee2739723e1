<?php

namespace App\Http\Controllers;

use App\Models\AnneeUniversitaire;
use App\Http\Requests\StoreAnneeUniversitaireRequest;
use App\Http\Requests\UpdateAnneeUniversitaireRequest;

class AnneeUniversitaireController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAnneeUniversitaireRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreAnneeUniversitaireRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AnneeUniversitaire  $anneeUniversitaire
     * @return \Illuminate\Http\Response
     */
    public function show(AnneeUniversitaire $anneeUniversitaire)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AnneeUniversitaire  $anneeUniversitaire
     * @return \Illuminate\Http\Response
     */
    public function edit(AnneeUniversitaire $anneeUniversitaire)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateAnneeUniversitaireRequest  $request
     * @param  \App\Models\AnneeUniversitaire  $anneeUniversitaire
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateAnneeUniversitaireRequest $request, AnneeUniversitaire $anneeUniversitaire)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AnneeUniversitaire  $anneeUniversitaire
     * @return \Illuminate\Http\Response
     */
    public function destroy(AnneeUniversitaire $anneeUniversitaire)
    {
        //
    }
}
