<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stat_groups', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->double('mtm', 8, 3);
            $table->double('mtf', 8, 3);
            $table->integer('res');
            $table->integer('order');
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stat_groups');
    }
};
