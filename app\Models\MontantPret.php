<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class MontantPret extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'code_diplome',
        'annee_etude',
        'resultat',
        'montant',
    ];

    protected $casts = [
        'resultat' => 'boolean',
    ];

    public function diplome() : BelongsTo
    {
        return $this->belongsTo(Diplome::class,'code_diplome','code');
    }


}
