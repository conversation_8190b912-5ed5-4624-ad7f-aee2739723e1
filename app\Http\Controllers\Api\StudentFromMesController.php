<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreStudentFromMesRequest;
use App\Http\Requests\UpdateStudentFromMesRequest;
use App\Http\Resources\StudentFromMesResource;
use App\Models\Etablissement;
use App\Models\Filiere;
use App\Models\StudentFromMes;
use App\Models\Universite;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class StudentFromMesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $sort = $request->query('sort', 'asc');
        $page = $request->query('page', 0);
        $pageSize = $request->query('pageSize', 10);
        $sortColumn = $request->query('sortColumn', 'CIN');
        $annee_bac = $request->query('annee_bac', null);
        $etablissement_id = $request->query('etablissement_id', null);
        $universite_id = $request->query('university_id', null);


        $query =  StudentFromMes::with(['gouvernorat', 'lycee', 'filiere', 'profession','filiere.etablissement']);
        if ($annee_bac){
            $query->where('annee_bac', $annee_bac);
        }
        if ($etablissement_id){
            $filieresCodes = Filiere::where('code_etab', $etablissement_id)->pluck('code');
            $query->whereIn('CODE_FILIERE', $filieresCodes);
        } else {
            if ($universite_id){
                $universite = Universite::find($universite_id);
                $etablissementCodes = Etablissement::where('code_univ', $universite?->code)?->pluck('code');
                $filieresCodes = Filiere::whereIn('code_etab', $etablissementCodes)?->pluck('code');
                $query->whereIn('CODE_FILIERE', $filieresCodes);
            }
        }
        if ($request->q != ""){
            $query->when($request->q, function($q)use($request){
                $q->where('NOM_A', 'like', '%'.$request->q.'%')
                    ->orWhere('NOM_L', 'like', '%'.$request->q.'%')
                    ->orWhere('CIN', 'like', '%'.$request->q.'%')
                    ->orWhere('NBAC', 'like', '%'.$request->q.'%');
            });
        }


        $recordsTotal = $query->count();

        $query->sortable([$sortColumn => $sort])->paginate($pageSize, ['*'], 'page', $page);


        $studentFromMes = $query->get();

        return response()->json([
            "params"=> $request->all(),
            "allData"=> [],
            "data"=> StudentFromMesResource::collection($studentFromMes),
            "total"=> $recordsTotal
        ],200);
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index2(): AnonymousResourceCollection
    {
        return StudentFromMesResource::collection(StudentFromMes::sortable()->paginate(5));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreStudentFromMesRequest $request
     * @return Response
     */
    public function store(StoreStudentFromMesRequest $request): Response
    {
        $data = $request->validated();
        $documentPredefini = StudentFromMes::create($data);

        return response(new StudentFromMesResource($documentPredefini) , 201);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param UpdateStudentFromMesRequest $request
     * @param StudentFromMes $studentFromMes
     * @return Response
     */
    public function edit(UpdateStudentFromMesRequest $request, StudentFromMes $studentFromMes): Response
    {
        $data = $request->validated();
        $studentFromMes->update($data);
        return response(new StudentFromMesResource($studentFromMes) , 201);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param StudentFromMes $studentFromMes
     * @return Response
     */
    public function destroy(StudentFromMes $studentFromMes): Response
    {
        $studentFromMes->delete();
        return response("ok", 204);
    }
}
