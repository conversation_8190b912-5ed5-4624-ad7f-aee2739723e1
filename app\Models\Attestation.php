<?php

namespace App\Models;

use App\Models\Scopes\AttestationScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use OwenIt\Auditing\Auditable as AuditableTrait;
use App\Traits\AuditableRelatedModels;

class Attestation extends Model implements Auditable
{
    use HasFactory;
    use \OwenIt\Auditing\Auditable, AuditableRelatedModels {
        AuditableTrait::transformAudit as parentTransformAudit;
    }
    use SoftDeletes;

    public function transformAudit(array $data): array
    {
        $data = $this->parentTransformAudit($data);
        $data = $this->transformAuditRelatedModels($data);
        return $data;
    }

    protected static function booted()
    {
        static::addGlobalScope(new AttestationScope());
    }

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = ['attestation_types_id', 'status', 'detail', 'year', 'student_id', 'annee_universitaire_id', 'etablissement_id', 'date_enregistrement_bureau_ordre', 'reference_bureau_ordre', 'langue', 'office_id', 'response_date', 'montant', 'date_mandat', 'raison_non_retrait', 'num_emission'];

    public function student(): BelongsTo
    {
        return $this->setConnection(config('database.secondConnection'))->belongsTo(User::class, 'student_id');
    }

    public function attestationType(): BelongsTo
    {
        return $this->belongsTo(AttestationType::class, 'attestation_types_id');
    }

    public function etudiantAnneeUniversitaire(): BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class, 'annee_universitaire_id');
    }

    public function attestationDocuments(): HasMany
    {
        return $this->hasMany(DocumentsAttestationUpload::class, 'attestation_id');
    }

    public function etablissement(): BelongsTo
    {
        return $this->belongsTo(Etablissement::class, 'etablissement_id');
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class, 'office_id');
    }
}
