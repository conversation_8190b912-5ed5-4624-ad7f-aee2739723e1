<?php

use App\Models\Country;
use App\Models\Delegation;
use App\Models\Gouvernorat;
use App\Models\Classe;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('international_students', function (Blueprint $table) {
            $table->id();
            $table->string('num_passport');
            $table->string('matricule');
            $table->string('name')->nullable();
            $table->string('firstName')->nullable();
            $table->string('phoneNumber')->nullable();
            $table->string('address')->nullable();
            $table->string('zipCode')->nullable();
            $table->date('dateOfBirth')->nullable();
            $table->string('placeOfBirth')->nullable();
            $table->string('sex')->nullable();
            $table->string('nationality_id')->nullable();
            $table->string('code_etab')->nullable();
            $table->integer('filiere_id')->nullable();
            $table->string('foyer')->nullable();
            $table->integer('annee_bac')->nullable();

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('international_students');
    }
};
