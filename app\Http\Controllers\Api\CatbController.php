<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCatbRequest;
use App\Http\Requests\UpdateCatbRequest;
use App\Http\Resources\CatbResource;
use App\Models\Catb;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use JetBrains\PhpStorm\Pure;

class CatbController extends Controller
{
    protected $cache_seconds = 900;

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(): AnonymousResourceCollection
    {
        return Cache::remember('Catb', $this->cache_seconds, function () {
            return CatbResource::collection(Catb::all());
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreCatbRequest $request
     * @return Response
     */
    public function store(StoreCatbRequest $request): Response
    {
        $data = $request->validated();

        Cache::forget('Catb');
        Helpers::clearCacheIdp();

        $g = Catb::create($data);

        return response(new CatbResource($g) , 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Catb $catb
     * @return CatbResource
     */
    #[Pure] public function show(Catb $catb): CatbResource
    {
        return new CatbResource($catb);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param UpdateCatbRequest $request
     * @param Catb $catb
     * @return CatbResource
     */
    public function edit(UpdateCatbRequest $request, Catb $catb): CatbResource
    {
        $data = $request->validated();

        Cache::forget('Catb');
        Helpers::clearCacheIdp();

        $catb->update($data);

        return new CatbResource($catb);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Catb $catb
     * @return Response
     */
    public function destroy(Catb $catb): Response
    {
        Cache::forget('Catb');
        Helpers::clearCacheIdp();

        $catb->delete();

        return response("", 204);
    }
}
