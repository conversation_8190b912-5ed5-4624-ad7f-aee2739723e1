<?php

namespace App\Http\Controllers\Api;

use App\Exports\DemandeExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDemandeRequest;
use App\Http\Resources\DemandeResource;
use App\Jobs\GenerateExportedDemande;
use App\Jobs\NotifyUserOfCompletedExport;
use App\Jobs\ProcessDemandeExport;
use App\Models\Admin;
use App\Models\AnneeUniversitaire;
use App\Models\Attestation;
use App\Models\AttestationType;
use App\Models\Catb;
use App\Models\Classification;
use App\Models\Decision;
use App\Models\Demande;
use App\Models\DemandeAnneeEtude;
use App\Models\DemandeType;
use App\Models\Diplome;
use App\Models\DocumentClassificationDemande;
use App\Models\EtudiantAnneeUniversitaire;
use App\Models\ExportedFile;
use App\Models\Preparation;
use App\Models\RetraitInscription;
use App\Models\Scopes\DemandeScope;
use App\Models\User;
use App\Notifications\AccordPrincipePretNotification;
use App\Notifications\ContratPretNonConformeNotification;
use App\Notifications\DossierArriveNotification;
use App\Notifications\DossierControlFiscalNotification;
use App\Notifications\DossierEligibleBourseInsertionNotification;
use App\Notifications\DossierEligibleBourseNotification;
use App\Notifications\DossierEligibleBourseStageNotification;
use App\Notifications\DossierEligiblePretNotification;
use App\Notifications\DossierIncoherentAdminNotification;
use App\Notifications\DossierIncompletNotification;
use App\Notifications\DossierRefusNotification;
use App\Notifications\RefusBourseNotification;
use App\Notifications\RefusPretNotification;
use App\Rules\ClassificationFinalRequired;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Notification;

class DemandeController extends Controller
{
  public function all()
  {
    return Demande::all();
  }

  public function index(Request $request): JsonResponse
  {
    if ($request->demande_type_code === 'bureau_ordre') {
      $demandes = Demande::where(function ($query) {
        $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
          ->orWhere(function ($query) {
            $query->where('etat', Demande::ETAT['DOSSIER_EN_COURS'])
              ->where('etat_dossier', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
              ->where('etat_complement', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
              ->whereNotNull('etat_dossier');
          })
          ->orWhere(function ($query) {
            $query->where('etat', Demande::ETAT['PRISE_DE_DECISION'])
              ->where('etat_contrat', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE']);
          });
      });
      $type = null;
      $typeIds = [];
      if ($request->demande_type_id) {
        $type = DemandeType::find($request->demande_type_id);
      }
      if ($type) {
        $typeIds[] = $type?->id;
        if ($type?->fils) {
          foreach ($type->fils as $child) {
            $typeIds[] = $child->id;
            if ($child->fils) {
              foreach ($child->fils as $childd) {
                $typeIds[] = $childd->id;
                if ($childd->fils) {
                  foreach ($childd->fils as $childdd) {
                    $typeIds[] = $childdd->id;
                  }
                }
              }
            }
          }
        }
        $demandes = $demandes->whereIn('demande_type_id', $typeIds);
      }

      $demandes = $demandes->when(
        $request->has('annee_universitaire_id'),
        function ($query) use ($request) {
          return $query->where('annee_universitaire_id', $request->annee_universitaire_id);
        }
      );
      if ($request->status) {
        $demandes = $demandes->where('etat', '=', (int)$request->status);
      }

    } elseif ($request->demande_type_code === 'guichet') {
      $type = null;

      if ($request->demande_type_id) {
        $type = DemandeType::find($request->demande_type_id);
      }

      $demandes = Demande::withoutGlobalScope(DemandeScope::class);
      $typeIds = [];
      if ($type) {
        $typeIds[] = $type->id;
        if ($type?->fils) {
          foreach ($type->fils as $child) {
            $typeIds[] = $child->id;
            if ($child->fils) {
              foreach ($child->fils as $childd) {
                $typeIds[] = $childd->id;
                if ($childd->fils) {
                  foreach ($childd->fils as $childdd) {
                    $typeIds[] = $childdd->id;
                  }
                }
              }
            }
          }
        }
        $demandes = Demande::whereIn('demande_type_id', $typeIds);
      }

      if ($request->status) {
        $pieces = explode("---", $request->status);
        $group = $pieces[0];
        $key = $pieces[1];
        //status status_dossier status_complement status_contrat status_bourse status_bourse_insertion status_pret status_bourse_stage status_aide_sociale
        if ($group === 'status') {
          $demandes = $demandes->where('etat', '=', $key);
        }
        if ($group === 'status_dossier') {
          $demandes = $demandes->where('etat_dossier', '=', $key);
        }
        if ($group === 'status_complement') {
          $demandes = $demandes->where('etat_complement', '=', $key);
        }
        if ($group === 'status_contrat') {
          $demandes = $demandes->where('etat_contrat', '=', $key);
        }
        if ($group === 'status_bourse') {
//                    if($key === '4'){
//                        $demandes = $demandes->whereIn('etat_bourse',[ 4, 5, 6, 7 ]);
//                    } else {
//                        $demandes = $demandes->where('etat_bourse', '=', $key);
//                    }
          $demandes = $demandes->where('etat_bourse', '=', $key);
        }
        if ($group === 'status_bourse_insertion') {
//                    if($key === '4'){
//                        $demandes = $demandes->whereIn('etat_bourse_insertion',[ 4, 5, 6, 7 ]);
//                    } else {
//                        $demandes = $demandes->where('etat_bourse_insertion', '=', $key);
//                    }
          $demandes = $demandes->where('etat_bourse_insertion', '=', $key);
        }
        if ($group === 'status_pret') {
//                    if($key === '4'){
//                        $demandes = $demandes->whereIn('etat_pret',[ 4, 5, 6, 7 ]);
//                    } else {
//                        $demandes = $demandes->where('etat_pret', '=', $key);
//                    }
          $demandes = $demandes->where('etat_pret', '=', $key);
        }
        if ($group === 'status_bourse_stage') {
//                    if($key === '4'){
//                        $demandes = $demandes->whereIn('etat_bourse_stage',[ 4, 5, 6, 7 ]);
//                    } else {
//                        $demandes = $demandes->where('etat_bourse_stage', '=', $key);
//                    }
          $demandes = $demandes->where('etat_bourse_stage', '=', $key);
        }
        if ($group === 'status_aide_sociale') {
//                    if($key === '4'){
//                        $demandes = $demandes->whereIn('etat_aide_sociale',[ 4, 5, 6, 7 ]);
//                    } else {
//                        $demandes = $demandes->where('etat_aide_sociale', '=', $key);
//                    }
          $demandes = $demandes->where('etat_aide_sociale', '=', $key);
        }
      }
      if ($request->annee_id) {
        $demandes = $demandes->where('annee_universitaire_id', $request->annee_id);
      }
      if ($request->cin || $request->nom) {
        $userIds = User::where(function (Builder $query) use ($request) {
          if ($request->cin && !$request->nom) {
            $query->orWhere('cin', 'like', '%' . $request->cin . '%')
              ->orWhere('num_passport', 'like', '%' . $request->cin . '%')
              ->orWhere('matricule', 'like', '%' . $request->cin . '%');
          }
          if ($request->nom && !$request->cin) {
            $query->orWhere('name', 'like', '%' . $request->nom . '%')
              ->orWhere('name_ar', 'like', '%' . $request->nom . '%')
              ->orWhere('firstName', 'like', '%' . $request->nom . '%');
          }
          if ($request->nom && $request->cin) {
            $query->where(function (Builder $q) use ($request) {
              $q->orWhere('name', 'like', '%' . $request->nom . '%')
                ->orWhere('name_ar', 'like', '%' . $request->nom . '%');
            })->where(function (Builder $q) use ($request) {
              $q->orWhere('firstName', 'like', '%' . $request->nom . '%')
                ->orWhere('cin', 'like', '%' . $request->cin . '%')
                ->orWhere('matricule', 'like', '%' . $request->cin . '%')
                ->orWhere('num_passport', 'like', '%' . $request->cin . '%');
            });

          }
        })->pluck('id');
        $demandes = $demandes->whereIn('user_id', $userIds);
      }
    } elseif ($request->demande_type_code === 'preparation_paiement') {
      $type = null;
      $typeIds = [];

      if ($request->demande_type_id) {
        $type = DemandeType::find($request->demande_type_id);
      } else {
        if ($request->type_preparation === 'bourse' || $request->type_preparation === 'insertion' || $request->type_preparation === 'pret') {
          $type = DemandeType::where('code', 'bourses_universitaires')->first();
          if ($request->type_preparation === 'bourse') {
            $types = DemandeType::whereIn('code', ['int_nv', 'int_ce', 'int_rnv', 'int_mstr', 'int_doc'])->pluck('id')->toArray();
            foreach ($types as $typeId) {
              $typeIds[] = $typeId;
            }
          }
        }
        if ($request->type_preparation === 'aide_sociale') {
          $type = DemandeType::where('code', 'aide_sociale')->first();
        }
        if ($request->type_preparation === 'stage') {
          $type = DemandeType::where('code', 'bourses_de_stage')->first();
        }
      }
      if ($type) {
        $typeIds[] = $type?->id;
        if ($type?->fils) {
          foreach ($type->fils as $child) {
            $typeIds[] = $child->id;
            if ($child->fils) {
              foreach ($child->fils as $childd) {
                $typeIds[] = $childd->id;
                if ($childd->fils) {
                  foreach ($childd->fils as $childdd) {
                    $typeIds[] = $childdd->id;
                  }
                }
              }
            }
          }
        }
      }
      $demandes = Demande::whereIn('demande_type_id', $typeIds);

      if ($request->annee_universitaire_id) {
        $demandes = $demandes->where('annee_universitaire_id', $request->annee_universitaire_id);
      }

      if ($request->attestation_status === "true") {
        $attestationTypesNonBoursier = AttestationType::where('non_boursier', true)->pluck('id');
        $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
        if ($request->annee_universitaire_id) {
          $annee = AnneeUniversitaire::find($request->annee_universitaire_id);
          $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status', 'annulee');
        }

        $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
        $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
      }
      if ($request->attestation_status === "false") {
        $attestationTypesNonBoursier = AttestationType::where('non_boursier', true)->pluck('id');
        $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
        if ($request->annee_universitaire_id) {
          $annee = AnneeUniversitaire::find($request->annee_universitaire_id);
          $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status', 'annulee');
        }

        $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
        $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
      }
      if ($request->attestation_pret_status === "true") {
        $attestationTypesNonBoursier = AttestationType::where('non_pret', true)->pluck('id');
        $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
        if ($request->annee_universitaire_id) {
          $annee = AnneeUniversitaire::find($request->annee_universitaire_id);
          $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status', 'annulee');
        }

        $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
        $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
      }
      if ($request->attestation_pret_status === "false") {
        $attestationTypesNonBoursier = AttestationType::where('non_pret', true)->pluck('id');
        $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier);
        if ($request->annee_universitaire_id) {
          $annee = AnneeUniversitaire::find($request->annee_universitaire_id);
          $usersHaveAttestation = $usersHaveAttestation->where('year', $annee->title)->whereNot('status', 'refuse')->whereNot('status', 'annulee');
        }

        $usersHaveAttestation = $usersHaveAttestation->pluck('student_id');
        $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
      }
      if ($request->retrait_status === "true") {
        $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_id');
        $demandes = $demandes->whereIn('user_id', $usersRetrait);
      }
      if ($request->retrait_status === "false") {
        $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_id');
        $demandes = $demandes->whereNotIn('user_id', $usersRetrait);
      }

      if ($request->type_preparation === 'bourse' || $request->type_preparation === 'insertion' || $request->type_preparation === 'pret') {
        $demandes = $demandes->whereNot(function ($query) {
          $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
            ->orWhere(function ($query) {
              $query->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                ->whereNotNull('etat_dossier');
            })->orWhere(function ($query) {
              $query->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                ->whereNotNull('etat_contrat');
            });
        });
      }

      $demandes = $demandes->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION']);

      if ($request->type_preparation) {
        if ($request->type_preparation === 'bourse') {
          $demandes = $demandes->where('etat_bourse', '=', Demande::ETAT_BOURSE['ELIGIBLE']);
        }
        if ($request->type_preparation === 'insertion') {
          $demandes = $demandes->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']);
        }
        if ($request->type_preparation === 'pret') {
          $demandes = $demandes->where('etat_pret', '=', Demande::ETAT_PRET['ELIGIBLE']);
        }
        if ($request->type_preparation === 'aide_sociale') {
          $demandes = $demandes->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['ELIGIBLE']);
        }
        if ($request->type_preparation === 'stage') {
          $demandes = $demandes->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['ELIGIBLE']);
        }

      }

      if ($request->has('revenu_net_min') && $request->revenu_net_min != '') {
        $demandes = $demandes->where('revenu_net', '>=', $request->revenu_net_min);
      }
      if ($request->has('revenu_net_max') && $request->revenu_net_max != '') {
        $demandes = $demandes->where('revenu_net', '<=', $request->revenu_net_max);
      }

      if ($request->status) {
        $demandes = $demandes->where('etat', '=', $request->status);
      }

    } elseif ($request->demande_type_code === 'suivi_preparation_paiement') {
      $type = null;
      $typeIds = [];
      if ($request->demande_type_id) {
        $type = DemandeType::find($request->demande_type_id);
      } else {
        if ($request->type_preparation === 'bourse' || $request->type_preparation === 'insertion' || $request->type_preparation === 'pret') {
          $type = DemandeType::where('code', 'bourses_universitaires')->first();
          if ($request->type_preparation === 'bourse') {
            $types = DemandeType::whereIn('code', ['int_nv', 'int_ce', 'int_rnv', 'int_mstr', 'int_doc'])->pluck('id')->toArray();
            foreach ($types as $typeId) {
              $typeIds[] = $typeId;
            }
          }
        }
        if ($request->type_preparation === 'aide_sociale') {
          $type = DemandeType::where('code', 'aide_sociale')->first();
        }
        if ($request->type_preparation === 'stage') {
          $type = DemandeType::where('code', 'bourses_de_stage')->first();
        }
      }
      if ($type) {
        $typeIds[] = $type?->id;
        if ($type?->fils) {
          foreach ($type->fils as $child) {
            $typeIds[] = $child->id;
            if ($child->fils) {
              foreach ($child->fils as $childd) {
                $typeIds[] = $childd->id;
                if ($childd->fils) {
                  foreach ($childd->fils as $childdd) {
                    $typeIds[] = $childdd->id;
                  }
                }
              }
            }
          }
        }
      }
      $demandes = Demande::whereIn('demande_type_id', $typeIds);

      if ($request->attestation_status === "true") {
        $attestationTypesNonBoursier = AttestationType::where('code', 'like', '%non_boursier%')->pluck('id');
        $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)->pluck('student_id');
        $demandes = $demandes->whereIn('user_id', $usersHaveAttestation);
      }
      if ($request->attestation_status === "false") {
        $attestationTypesNonBoursier = AttestationType::where('code', 'like', '%non_boursier%')->pluck('id');
        $usersHaveAttestation = Attestation::whereIn('attestation_types_id', $attestationTypesNonBoursier)->pluck('student_id');
        $demandes = $demandes->whereNotIn('user_id', $usersHaveAttestation);
      }
      if ($request->retrait_status === "true") {
        $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_id');
        $demandes = $demandes->whereIn('user_id', $usersRetrait);
      }
      if ($request->retrait_status === "false") {
        $usersRetrait = RetraitInscription::where('annee_universitaire_id', $request->annee_universitaire_id)->pluck('student_id');
        $demandes = $demandes->whereNotIn('user_id', $usersRetrait);
      }

      if ($request->type_preparation === 'bourse' || $request->type_preparation === 'insertion' || $request->type_preparation === 'pret') {
        $demandes = $demandes->whereNot(function ($query) {
          $query->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
            ->orWhere(function ($query) {
              $query->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                ->whereNotNull('etat_dossier');
            })->orWhere(function ($query) {
              $query->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                ->whereNotNull('etat_contrat');
            });
        });
      }

      $demandes = $demandes->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION']);

      if ($request->type_preparation) {
        if ($request->type_preparation === 'bourse') {
          if ($request->decision_status) {
            $demandes = $demandes->where('etat_bourse', '=', $request->decision_status);
          } else {
            $demandes = $demandes->where(function ($q) {
              $q->where('etat_bourse', '=', Demande::ETAT_BOURSE['SUIVI_PAIEMENT'])
                ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_FAVORABLE'])
                ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_NON_FAVORABLE'])
                ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['DECISION_AMBIGU']);
            });
          }

          $demandes = $demandes->where('preparation_bourse_id', $request->preparation_id);
        }
        if ($request->type_preparation === 'insertion') {
          if ($request->decision_status) {
            $demandes = $demandes->where('etat_bourse_insertion', '=', $request->decision_status);
          } else {
            $demandes = $demandes->where(function ($q) {
              $q->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'])
                ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_FAVORABLE'])
                ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_NON_FAVORABLE'])
                ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['DECISION_AMBIGU']);
            });
          }
          $demandes = $demandes->where('preparation_insertion_id', $request->preparation_id);
        }
        if ($request->type_preparation === 'pret') {
          if ($request->decision_status) {
            $demandes = $demandes->where('etat_pret', '=', $request->decision_status);
          } else {
            $demandes = $demandes->where(function ($q) {
              $q->where('etat_pret', '=', Demande::ETAT_PRET['SUIVI_PAIEMENT'])
                ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_FAVORABLE'])
                ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_NON_FAVORABLE'])
                ->orWhere('etat_pret', '=', Demande::ETAT_PRET['DECISION_AMBIGU']);
            });
          }
          $demandes = $demandes->where('preparation_pret_id', $request->preparation_id);
        }
        if ($request->type_preparation === 'aide_sociale') {
          if ($request->decision_status) {
            $demandes = $demandes->where('etat_aide_sociale', '=', $request->decision_status);
          } else {
            $demandes = $demandes->where(function ($q) {
              $q->where('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['SUIVI_PAIEMENT'])
                ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_FAVORABLE'])
                ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_NON_FAVORABLE'])
                ->orWhere('etat_aide_sociale', '=', Demande::ETAT_AIDE_SOCIALE['DECISION_AMBIGU']);
            });
          }

          $demandes = $demandes->where('preparation_aide_sociale_id', $request->preparation_id);
        }
        if ($request->type_preparation === 'stage') {
          if ($request->decision_status) {
            $demandes = $demandes->where('etat_bourse_stage', '=', $request->decision_status);
          } else {
            $demandes = $demandes->where(function ($q) {
              $q->where('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['SUIVI_PAIEMENT'])
                ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_FAVORABLE'])
                ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_NON_FAVORABLE'])
                ->orWhere('etat_bourse_stage', '=', Demande::ETAT_BOURSE_STAGE['DECISION_AMBIGU']);
            });
          }
          $demandes = $demandes->where('preparation_stage_id', $request->preparation_id);
        }

      }

      if ($request->has('revenu_net_min') && $request->revenu_net_min != '') {
        $demandes = $demandes->where('revenu_net', '>=', $request->revenu_net_min);
      }
      if ($request->has('revenu_net_max') && $request->revenu_net_max != '') {
        $demandes = $demandes->where('revenu_net', '<=', $request->revenu_net_max);
      }

      if ($request->annee_universitaire_id) {
        $demandes = $demandes->where('annee_universitaire_id', $request->annee_universitaire_id);
      }

    } else {
      $type = DemandeType::where('code', $request->demande_type_code)->first();

      if ($request->demande_type_id) {
        $type = DemandeType::find($request->demande_type_id);
      }

      $typeIds = [];
      if ($request->demande_type_code === 'bourses_universitaires' && !$request->demande_type_id) {
        $types = DemandeType::whereIn('code', ['int_nv', 'int_ce', 'int_rnv', 'int_mstr', 'int_doc'])->pluck('id')->toArray();
        foreach ($types as $typeId) {
          $typeIds[] = $typeId;
        }
      }
      if ($type) {
        $typeIds[] = $type?->id;
        if ($type?->fils) {
          foreach ($type->fils as $child) {
            $typeIds[] = $child->id;
            if ($child->fils) {
              foreach ($child->fils as $childd) {
                $typeIds[] = $childd->id;
                if ($childd->fils) {
                  foreach ($childd->fils as $childdd) {
                    $typeIds[] = $childdd->id;
                  }
                }
              }
            }
          }
        }
      }
//            return response()->json($type);
      $demandes = Demande::whereIn('demande_type_id', $typeIds);
      if ($request->annee_universitaire_id) {
        $demandes = $demandes->where('annee_universitaire_id', $request->annee_universitaire_id);
      }

      // test refus
      if ($request->refus === 'refus') {
        $demandes = $demandes->where(
          function ($query) {
            $query
              ->orWhere('etat', '=', Demande::ETAT['REFUS_PAIEMENT'])
              ->orWhere('etat', '=', Demande::ETAT['DOSSIER_REFUS'])
              ->orWhere(function ($query) {
                $query
                  ->where('etat', '>=', Demande::ETAT['PREPARATION_PAIEMENT'])
                  ->where(function ($query) {
                    $query
                      ->orWhere(function ($query) {
                        $query
                          ->where('etat_bourse', '=', Demande::ETAT_BOURSE['NON_ELIGIBLE'])
                          ->where('etat_bourse_insertion', '>=', Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'])
                          ->where('is_pret', '=', false);
                      })
                      ->orWhere(function ($query) {
                        $query
                          ->where('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['NON_ELIGIBLE'])
                          ->where('etat_bourse', '>=', Demande::ETAT_BOURSE['SUIVI_PAIEMENT'])
                          ->where('is_pret', '=', false);;
                      })
                      ->orWhere(function ($query) {
                        $query
                          ->where('etat_bourse', '=', Demande::ETAT_BOURSE['NON_ELIGIBLE'])
                          ->where('etat_bourse_insertion', '>=', Demande::ETAT_BOURSE_INSERTION['SUIVI_PAIEMENT'])
                          ->where('is_pret', '=', true)
                          ->where('etat_pret', '=', Demande::ETAT_PRET['NON_ELIGIBLE']);
                      });
                  });
              });
          }
        );
      } else {
        $demandes = $demandes
          ->whereNot(function ($query) {
            $query
              ->orWhere('etat', '<', Demande::ETAT['DOSSIER_EN_COURS'])
              ->orWhere(function ($query) {
                $query
                  ->where('etat', '>=', Demande::ETAT['DOSSIER_EN_COURS'])
                  ->where('etat_dossier', '=', Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET'])
                  ->where('etat_complement', '=', Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'])
                  ->whereNotNull('etat_dossier');
              })
              ->orWhere(function ($query) {
                $query
                  ->where('etat', '=', Demande::ETAT['PRISE_DE_DECISION'])
                  ->where('etat_contrat', '=', Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'])
                  ->whereNotNull('etat_contrat');
              });
          })
          ->where(function ($query) {
            $query
              ->orWhere('etat', '<', Demande::ETAT['PREPARATION_PAIEMENT'])
              ->orWhere(function ($query) {
                $query
                  ->where('etat', '>=', Demande::ETAT['PREPARATION_PAIEMENT'])
                  ->where(function ($query) {
                    $query
                      ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['EN_ATTENTE'])
                      ->orWhere('etat_bourse', '=', Demande::ETAT_BOURSE['EN_INSTANCE'])
                      ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['EN_ATTENTE'])
                      ->orWhere('etat_bourse_insertion', '=', Demande::ETAT_BOURSE_INSERTION['EN_INSTANCE']);
                  });
              });
          });
      }

      // test is_pret
      if ($request->pret === 'pret') {
        $demandes = $demandes->where('is_pret', true);
      } else {
        $demandes = $demandes->where('is_pret', false);
      }
      if ($request->status) {
        $demandes = $demandes->where('etat', '=', $request->status);
      }
    }

    /** Adding Filter query */
    if ($request->q) {

      $userIds = User::where(function (Builder $query) use ($request) {
        $query
          ->orWhere('email', 'like', '%' . $request->q . '%')
          ->orWhere('name', 'like', '%' . $request->q . '%')
          ->orWhere('name_ar', 'like', '%' . $request->q . '%')
          ->orWhere('firstName', 'like', '%' . $request->q . '%')
          ->orWhere('cin', 'like', '%' . $request->q . '%')
          ->orWhere('num_bac', 'like', '%' . $request->q . '%')
          ->orWhere('matricule', 'like', '%' . $request->q . '%')
          ->orWhere('num_passport', 'like', '%' . $request->q . '%');
      })->pluck('id');
//            $demandes = $demandes->whereIn('user_id', $userIds);
      $demandes = $demandes->where(function (Builder $query) use ($request, $userIds) {
        $query
          ->orWhere('code', 'like', '%' . $request->q . '%')
          ->orWhereIn('user_id', $userIds);
      });
    }
    /** Adding Filter by dates */
    if ($request->dates && count($request->dates)) {
      $start = Carbon::createFromFormat('d/m/Y', $request->dates[0]);
      $end = Carbon::createFromFormat('d/m/Y', $request->dates[1]);
      $demandes = $demandes->whereDate('created_at', '>=', $start)->whereDate('created_at', '<=', $end);
    }

    /** Adding Order By */
    if ($request->orderByColumn && $request->orderByDirection) {
      $demandes = $demandes->orderBy($request->orderByColumn, $request->orderByDirection);
    } else {
      $demandes = $demandes->orderBy('created_at', 'desc');
    }

    $demandes = $demandes->with('demandeDocumentsClassifications', 'configDemandeType', 'demandeAnneeEtudes');
    return response()->json($demandes->paginate(
      $request->input('perPage') ?? config('constants.pagination'),
    ), 200);


  }

  public function historique_etudiant($user_id): JsonResponse
  {
    $historique = Demande::where('user_id', $user_id)
      ->where('etat', '>=', Demande::ETAT['PRISE_DE_DECISION'])
      ->with('anneeUniversitaire', 'classification', 'demandeType')->get();

    return response()->json($historique, 200);
  }

  public function dossierArrive(Demande $demande): Application|ResponseFactory|Response|JsonResponse
  {
    if ($demande->etat_complement == Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE']) {
      $demande->update([
        'etat_complement' => Demande::ETAT_COMPLEMENT['DOCUMENT_COMPLEMENT_RECU'],
      ]);
    } else {
      if ($demande->etat_contrat == Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE']) {
        $demande->update([
          'etat_contrat' => Demande::ETAT_CONTRAT['DOCUMENT_CONTRAT_RECU'],
        ]);
      } else {
        $demande->update([
          'etat' => Demande::ETAT['DOSSIER_EN_COURS'],
        ]);
      }
    }


    return response(new DemandeResource($demande), 201);

  }

  public function dossierArriveSearchAndValidate(string $code): Application|ResponseFactory|Response|JsonResponse
  {
    $demande = Demande::where('code', $code)->first();

    if ($demande) {
      if ($demande->etat < Demande::ETAT['DOSSIER_EN_COURS']) {
        $demande->update([
          'etat' => Demande::ETAT['DOSSIER_EN_COURS'],
        ]);
        Notification::send([$demande->user], new DossierArriveNotification($demande));

        return response('ok');
      } elseif (
        $demande->etat >= Demande::ETAT['DOSSIER_EN_COURS'] &&
        $demande->etat_complement === Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'] &&
        $demande->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET']
      ) {
        $demande->update([
          'etat_complement' => Demande::ETAT_COMPLEMENT['DOCUMENT_COMPLEMENT_RECU'],
        ]);
        Notification::send([$demande->user], new DossierArriveNotification($demande));

        return response('ok');
      } elseif (
        $demande->etat >= Demande::ETAT['PRISE_DE_DECISION'] &&
        $demande->etat_contrat === Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE']
      ) {
        $demande->update([
          'etat_contrat' => Demande::ETAT_CONTRAT['DOCUMENT_CONTRAT_RECU'],
        ]);
        Notification::send([$demande->user], new DossierArriveNotification($demande));

        return response('ok');
      } else {
        return response('en_cours');
      }

    }

    return response('fail');

  }

  public function editEtatDossierCalcule(Request $request, Demande $demande): Application|ResponseFactory|Response|JsonResponse
  {
    $validator1 = Validator::make($request->all(), [
      'etat_dossier' => 'nullable|string',
      'comment_incoherent' => 'nullable|string',
      'comment_incomplete' => 'nullable|string',
      'comment_refus' => 'nullable|string',

      'type_calcule' => 'nullable|string',
      'revenu_annuel_pere' => 'nullable|integer',
      'revenu_annuel_mere' => 'nullable|integer',
      'nbr_freres_soeurs' => 'nullable|integer',
      'nbr_freres_soeurs_parraines' => 'nullable|integer',
      'nbr_freres_soeurs_handicapes' => 'nullable|integer',
      'nbr_freres_soeurs_unite' => 'nullable|integer',
      'nbr_freres_soeurs_parraines_unite' => 'nullable|integer',
      'nbr_freres_soeurs_handicapes_unite' => 'nullable|integer',
      'distance' => 'nullable|integer',
      'distance_unite' => 'nullable|integer',
      'revenu_annuel_conjoint' => 'nullable|integer',
      'bs_nb_jour' => 'nullable|integer',
      'bs_start' => 'nullable',
      'bs_end' => 'nullable',
      'revenu_net' => 'nullable',
      'type_score' => 'nullable',
      'situation_etudiant' => 'nullable',
      'situation_familiale_dece' => 'nullable',
      'situation_familiale_divorce' => 'nullable',
      'situation_familiale_handicap' => 'nullable',
      'compare_resultat' => 'nullable',
      'score_total' => 'nullable',

    ]);
    if ($validator1->fails()) {
      return response()->json([
        'message' => 'Validations fails',
        'errors' => $validator1->errors(),
      ], 422);
    }
    $data = $validator1->validated();

    if ($request->validate === 'true') {
      if ($request->etat_dossier !== Demande::ETAT_DOSSIER['DOSSIER_COMPLET']) {
        $data['etat'] = Demande::ETAT['DOSSIER_EN_COURS'];
        if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET']) {
          // TODO : notify student if dossier incomplete : email
          $data['nbr_notif_incomplete'] = $demande->nbr_notif_incomplete ? $demande->nbr_notif_incomplete + 1 : 1;
          $data['etat_complement'] = Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'];
          if ($request->documentsList && count($request->documentsList) > 0) {
            foreach ($request->documentsList as $document) {
              if ($document['dans_complement'] == 'true') {
                $d = DocumentClassificationDemande::find($document['id']);
                $d->update([
                  'dans_complement' => true,
                ]);
              } else {
                $d = DocumentClassificationDemande::find($document['id']);
                $d->update([
                  'dans_complement' => false,
                ]);
              }
            }
          }
          Notification::send([$demande->user], new DossierIncompletNotification($demande));
        }
        if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_INCOHERENT']) {
          $data['nbr_notif_incoherent'] = $demande->nbr_notif_incoherent ? $demande->nbr_notif_incoherent + 1 : 1;
          $admins = Admin::whereHas('roles', function ($q) {
            $q->where('name', 'admin');
          })->get();
          Notification::send($admins, new DossierIncoherentAdminNotification($demande, auth()->user()));
          $data['etat'] = Demande::ETAT['EN_COURS_DE_DECISION'];
        }
        if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_REFUS']) {
          // notify student if dossier refus : database
          Notification::send([$demande->user], new DossierRefusNotification($demande));
          $data['etat'] = Demande::ETAT['DOSSIER_REFUS'];
        }
      } else {
        $data['etat_complement'] = Demande::ETAT_COMPLEMENT['CLOTURE'];
        $data['etat'] = Demande::ETAT['EN_COURS_DE_DECISION'];
      }
    }

    $demande->update($data);

    return response(new DemandeResource($demande), 201);

  }

  public function decision(Request $request, Demande $demande): Application|ResponseFactory|Response|JsonResponse
  {

    $validator1 = Validator::make($request->all(), [
      'etat_dossier' => 'nullable|string',
      'comment_incoherent' => 'nullable|string',
      'comment_incomplete' => 'nullable|string',
      'comment_refus' => 'nullable|string',

      'type_calcule' => 'nullable|string',
      'revenu_annuel_pere' => 'nullable|integer',
      'revenu_annuel_mere' => 'nullable|integer',
      'nbr_freres_soeurs' => 'nullable|integer',
      'nbr_freres_soeurs_parraines' => 'nullable|integer',
      'nbr_freres_soeurs_handicapes' => 'nullable|integer',
      'nbr_freres_soeurs_unite' => 'nullable|integer',
      'nbr_freres_soeurs_parraines_unite' => 'nullable|integer',
      'nbr_freres_soeurs_handicapes_unite' => 'nullable|integer',
      'distance' => 'nullable|integer',
      'distance_unite' => 'nullable|integer',
      'revenu_annuel_conjoint' => 'nullable|integer',
      'bs_nb_jour' => 'nullable|integer',
      'bs_start' => 'nullable',
      'bs_end' => 'nullable',
      'revenu_net' => 'nullable',
      'type_score' => 'nullable',
      'situation_etudiant' => 'nullable',
      'situation_familiale_dece' => 'nullable',
      'situation_familiale_divorce' => 'nullable',
      'situation_familiale_handicap' => 'nullable',
      'compare_resultat' => 'nullable',
      'score_total' => 'nullable',
    ]);
    if ($validator1->fails()) {
      return response()->json([
        'message' => 'Validations fails',
        'errors' => $validator1->errors(),
      ], 422);
    }
    $data = $validator1->validated();

    if ($request->validate === 'true') {
      if ($request->etat_dossier !== Demande::ETAT_DOSSIER['DOSSIER_COMPLET']) {
        $data['etat'] = Demande::ETAT['DOSSIER_EN_COURS'];
        if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_INCOMPLET']) {
          // TODO : notify student if dossier incomplete : email
          $data['nbr_notif_incomplete'] = $demande->nbr_notif_incomplete ? $demande->nbr_notif_incomplete + 1 : 1;
          $data['etat_complement'] = Demande::ETAT_COMPLEMENT['DEPOT_COMPLEMENT_EN_ATTENTE'];
          if ($request->documentsList && count($request->documentsList) > 0) {
            foreach ($request->documentsList as $document) {
              if ($document['dans_complement'] == 'true') {
                $d = DocumentClassificationDemande::find($document['id']);
                $d->update([
                  'dans_complement' => true,
                ]);
              } else {
                $d = DocumentClassificationDemande::find($document['id']);
                $d->update([
                  'dans_complement' => false,
                ]);
              }
            }
          }
          Notification::send([$demande->user], new DossierIncompletNotification($demande));
        }
        if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_INCOHERENT']) {
          $data['nbr_notif_incoherent'] = $demande->nbr_notif_incoherent ? $demande->nbr_notif_incoherent + 1 : 1;
          $data['etat'] = Demande::ETAT['EN_COURS_DE_DECISION'];
        }
        if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_REFUS']) {
          // notify student if dossier refus : database
          Notification::send([$demande->user], new DossierRefusNotification($demande));
          $data['etat'] = Demande::ETAT['DOSSIER_REFUS'];
        }
      } else {
        $data['etat_complement'] = Demande::ETAT_COMPLEMENT['CLOTURE'];
        $data['etat'] = Demande::ETAT['EN_COURS_DE_DECISION'];
      }
    }

    $demande->update($data);

    if ($request->etat_dossier === Demande::ETAT_DOSSIER['DOSSIER_COMPLET']) {
      $validator1 = Validator::make($request->all(), [
        'control_fiscal' => 'nullable|integer',
        'classification_final_id' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true'),
          new ClassificationFinalRequired,
        ],
        'profession_final_id' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true'),
        ],
        'control_fiscal_date' => 'sometimes|nullable',
        'num_bordereau' => 'sometimes|nullable',
        'centre_control_fiscal' => 'sometimes|nullable',

        'etat_bourse' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true' && !$request->control_fiscal),
        ],
        'etat_pret' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true' && !$request->control_fiscal && $request->etat_bourse == 2),
        ],
        'etat_bourse_insertion' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true' && !$request->control_fiscal),
        ],
        'contrat_end_date' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true' &&
            !$request->control_fiscal &&
            $request->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE'] &&
            $request->etat_pret == Demande::ETAT_PRET['PROPOSITION']
          ),
        ],
        'contrat_montant' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true' &&
            !$request->control_fiscal &&
            $request->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE'] &&
            $request->etat_pret == Demande::ETAT_PRET['PROPOSITION']
          ),
        ],
        'etat_contrat' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->validate === 'true' &&
            !$request->control_fiscal &&
            $request->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE'] &&
            $request->etat_pret == Demande::ETAT_PRET['PROPOSITION'] && $demande->etat_contrat
          ),
        ],
        'comment_contrat_incomplet' => [
          'sometimes',
          Rule::requiredIf(fn() => $request->etat_pret == Demande::ETAT_PRET['PROPOSITION'] && $request->etat_contrat === 'document_contrat_non_conforme'
          ),
        ],


        'etat_bourse_stage' => 'sometimes|nullable',
        'etat_aide_sociale' => 'sometimes|nullable',
        'comment_refus_aide_sociale' => 'sometimes|nullable',
        'comment_refus_bourse_stage' => 'sometimes|nullable',
        'comment_refus_pret' => 'sometimes|nullable',
        'comment_refus_bourse_insertion' => 'sometimes|nullable',
        'comment_refus_bourse' => 'sometimes|nullable',

      ]);
      if ($validator1->fails()) {
        return response()->json([
          'message' => 'Validations fails',
          'errors' => $validator1->errors(),
        ], 422);
      }
      $data = $validator1->validated();


      if ($request->validate === 'true') {
        if ($request->control_fiscal) {
          $data['etat'] = Demande::ETAT['CONTROL_FISCAL'];
          $data['etat_bourse'] = null;
          $data['etat_bourse_insertion'] = null;
          $data['etat_pret'] = null;
          $data['etat_bourse_stage'] = null;
          $data['etat_aide_sociale'] = null;
          $data['nbr_notif_control_fiscal'] = $demande->nbr_notif_control_fiscal ? $demande->nbr_notif_control_fiscal + 1 : 1;
          Notification::send([$demande->user], new DossierControlFiscalNotification($demande));
        } else {
          if ($request->etat_bourse or $request->etat_bourse_insertion or $request->etat_pret) {
            $data['etat'] = Demande::ETAT['PRISE_DE_DECISION'];
            if ($request->etat_bourse == Demande::ETAT_BOURSE['ELIGIBLE']) {
              if ($demande->etat_bourse != Demande::ETAT_BOURSE['ELIGIBLE']) {
                Notification::send([$demande->user], new DossierEligibleBourseNotification($demande));
              }
            }
            if ($request->etat_bourse_insertion == Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']) {
              if ($demande->etat_bourse_insertion != Demande::ETAT_BOURSE_INSERTION['ELIGIBLE']) {
                $data['is_bourse_insertion'] = true;
                Notification::send([$demande->user], new DossierEligibleBourseInsertionNotification($demande));
              }
            }

            if ($request->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE'] && $request->etat_pret == Demande::ETAT_PRET['PROPOSITION']) {
              if (!$request->etat_contrat) {
                $data['etat_contrat'] = Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'];
                $data['etat_pret'] = Demande::ETAT_PRET['PROPOSITION'];
                $data['is_pret'] = true;
                Notification::send([$demande->user], new DossierEligiblePretNotification($demande));
              } else {
                if ($request->etat_contrat == Demande::ETAT_CONTRAT['DOCUMENT_CONTRAT_NON_CONFORME']) {
                  $data['etat_contrat'] = Demande::ETAT_CONTRAT['DEPOT_CONTRAT_EN_ATTENTE'];
                  $data['etat_pret'] = Demande::ETAT_PRET['PROPOSITION'];
                  $data['is_pret'] = true;
                  Notification::send([$demande->user], new ContratPretNonConformeNotification($demande));
                } else {
                  if ($request->etat_decision_contrat == Demande::ETAT_CONTRAT['REFUS']) {
                    $data['etat_contrat'] = Demande::ETAT_CONTRAT['REFUS'];
                    $data['etat_pret'] = Demande::ETAT_PRET['NON_ELIGIBLE'];
                    Notification::send([$demande->user], new RefusPretNotification($demande));
                  }
                  if ($request->etat_decision_contrat == Demande::ETAT_CONTRAT['ACCEPTE']) {
                    $data['etat_contrat'] = Demande::ETAT_CONTRAT['ACCEPTE'];
                    $data['etat_pret'] = Demande::ETAT_PRET['ELIGIBLE'];
                    $data['is_pret'] = true;
                    Notification::send([$demande->user], new AccordPrincipePretNotification($demande));
                  }
                }

              }
            }
            if (
              ($request->etat_bourse == Demande::ETAT_BOURSE['ELIGIBLE'] && $request->etat_bourse_insertion != Demande::ETAT_BOURSE_INSERTION['EN_INSTANCE']) ||
              ($request->etat_bourse_insertion == Demande::ETAT_BOURSE_INSERTION['ELIGIBLE'] && $request->etat_bourse != Demande::ETAT_BOURSE['EN_INSTANCE']) ||
              ($request->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE'] && (array_key_exists('etat_pret', $data) && $data['etat_pret'] != Demande::ETAT_PRET['PROPOSITION']))
            ) {
              $data['etat'] = Demande::ETAT['PREPARATION_PAIEMENT'];
            }
            if (
              $request->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE'] &&
              $request->etat_bourse_insertion == Demande::ETAT_BOURSE_INSERTION['NON_ELIGIBLE'] &&
              (!$request->has('etat_pret') || $request->etat_pret == Demande::ETAT_PRET['NON_ELIGIBLE'])
            ) {
              Notification::send([$demande->user], new RefusBourseNotification($demande));
              $data['etat'] = Demande::ETAT['REFUS_PAIEMENT'];
            }
          }
        }

        if (array_key_exists('etat_bourse_stage', $data) && $data['etat_bourse_stage']) {
          $data['etat'] = Demande::ETAT['PRISE_DE_DECISION'];
          if ($data['etat_bourse_stage'] == Demande::ETAT_BOURSE_STAGE['ELIGIBLE']) {
            Notification::send([$demande->user], new DossierEligibleBourseStageNotification($demande));
            $data['etat'] = Demande::ETAT['PREPARATION_PAIEMENT'];
          }
          if ($data['etat_bourse_stage'] == Demande::ETAT_BOURSE_STAGE['NON_ELIGIBLE']) {
//                    Notification::send([$demande->user], new DossierEligibleBourseStageNotification($demande));
            $data['etat'] = Demande::ETAT['REFUS_PAIEMENT'];
          }
        }

        if (array_key_exists('etat_aide_sociale', $data) && $data['etat_aide_sociale']) {
          $data['etat'] = Demande::ETAT['PRISE_DE_DECISION'];
          if ($data['etat_aide_sociale'] == Demande::ETAT_AIDE_SOCIALE['ELIGIBLE']) {
//                    Notification::send([$demande->user], new DossierEligibleAideSocialeNotification($demande));
            $data['etat'] = Demande::ETAT['PREPARATION_PAIEMENT'];
          }
          if ($data['etat_aide_sociale'] == Demande::ETAT_AIDE_SOCIALE['NON_ELIGIBLE']) {
//                    Notification::send([$demande->user], new DossierEligibleAideSocialeNotification($demande));
            $data['etat'] = Demande::ETAT['REFUS_PAIEMENT'];
          }
        }

        if (
          array_key_exists('classification_final_id', $data) &&
          $data['classification_final_id'] &&
          ($data['etat'] === Demande::ETAT['REFUS_PAIEMENT'] || $data['etat'] === Demande::ETAT['PREPARATION_PAIEMENT'] || $data['etat'] === Demande::ETAT['PRISE_DE_DECISION']) &&
          (!$demande->lot || ($demande->lot && $demande->classification_final_id != $data['classification_final_id']))
        ) {
          $last = Demande::where('annee_universitaire_id', $demande->annee_universitaire_id)
            ->where('classification_final_id', $data['classification_final_id'])
            ->orderBy('code_decision', 'DESC')->first();
          Log::info("last: " . print_r($last, true));

          $classificationFinal = Classification::find($data['classification_final_id']);
          $data['code_decision'] = $last?->code_decision ? $last->code_decision + 1 : 1;
          $clfinalLength = $classificationFinal?->code ? strlen($classificationFinal->code) : 0;
          if ($clfinalLength) {
            $format = "%0" . (6 - $clfinalLength) . "d";
            $data['lot'] = $classificationFinal?->code . sprintf($format, $last?->code_decision ? $last->code_decision + 1 : 1);
          }
        }
      }

      $demande->update($data);
      foreach ($demande->demandeDocumentsClassifications as $demandeDocumentClassification) {
        $demandeDocumentClassification->update([
          'dans_complement' => false
        ]);
      }
    }
    return response(new DemandeResource($demande), 201);

  }

  public function confirmDocumentByAdmin(Demande $demande): Response|JsonResponse
  {
    $existAll = true;
    foreach ($demande->demandeDocumentsClassifications as $demandeDocumentClassification) {
      if (!$demandeDocumentClassification->document_file) {
        $existAll = false;
        break;
      }
    }
    if (!$existAll) {
      return response()->json([
        'message' => 'Validations fails',
      ], 422);
    }
    $demande->update([
      'etat_document' => Demande::ETAT_DOCUMENT['CONFIRMEE_PAR_ADMIN'],
    ]);

    return response($demande, 201);
  }

  public function exportExcel(Request $request)
  {

//        try{
//            $row = ExportedFile::create([
//                'type' => $request->demande_type_code,
//                'vue' => 0,
//                'etat' => 'en_cours',
//            ]);
//
//            (new DemandeExport($request->all(), $row->id))->store('uploads/exportedFile/demande_' . $row->type . '_' . $row->id . '.xlsx')->chain([
//                new NotifyUserOfCompletedExport(request()->user(), $row->type, $row->id),
//            ]);
//        } catch (Exception $e) {
//            return response()->json($e->getMessage(), 500);
//        }
    ini_set('memory_limit', '-1');

    dispatch(new ProcessDemandeExport(auth()->user(), $request->all()));

    return response()->json("success", 200);
  }

  public function downloadExistingExportedFile(Request $request)
  {
    $request->validate([
      'id' => 'required',
    ]);

    $filename = ExportedFile::findOrFail($request->id)?->attached_file;
    $path = 'uploads/exportedFile/' . $filename;


    if (!Storage::exists($path)) {
      return response()->json(Storage::path($path), 404);
    }
    return Storage::download($path, $filename);
  }

  public function createDemande(StoreDemandeRequest $request, User $user): Response
  {
    $data = $request->validated();
    if ($user) {
      $data['user_id'] = $user->id;
    }
    $currentCode = random_int(100, 999) . "_" . Carbon::now()->timestamp;
    $data['code'] = $currentCode;
    $data['etat'] = Demande::ETAT['DOSSIER_EN_ATTENTE'];
    $data['etat_document'] = Demande::ETAT_DOCUMENT['EN_COURS'];
    $data['created_at'] = Carbon::now();
    $data['demande_type_id'] = $request->service;
    $data['config'] = $request->config;

    $config = json_decode($data['config'], true);

    $classifications = Classification::where('demande_type_id', $request->service)->where('classable', true)->get();
    $demandeType = DemandeType::find($request->service);

    $compArray = [];

    $data['config_demande_type_id'] = $demandeType?->last_config?->id;
    $data['type'] = $demandeType?->root_parent_code;

    if ($demandeType->bourse) {
      $data['is_bourse'] = true;
    }
    if ($demandeType->aide_sociale) {
      $data['is_aide_sociale'] = true;
    }
    if ($demandeType->bourses_de_stage) {
      $data['is_bourse_stage'] = true;
    }

    $anneeUniversitaireCurrent = AnneeUniversitaire::where('start', '<=', Carbon::now())->where('end', '>', Carbon::now())->first();
    $anneeUniversitaireLast = AnneeUniversitaire::where('start', '<=', Carbon::now()->subYear())->where('end', '>=', Carbon::now()->subYear())->first();
    if ($demandeType->bourses_de_stage) {
      $data['annee_universitaire_id'] = $anneeUniversitaireLast?->id;
    } else {
      $data['annee_universitaire_id'] = $anneeUniversitaireCurrent?->id;
    }
//        dd($data);

    foreach (json_decode($demandeType?->last_config?->config) as $row) {
      foreach ($row->children as $col) {
        foreach ($col->children as $comp) {
          $compArray[$comp->id] = $comp;
        }
      }
    }

    $exist = false;
    if (!$classifications->isEmpty()) {
      if (count($classifications) == 1) {
        $exist = true;
        $data['classification_final_id'] = $classifications[0]->id;
        $data['classification_id'] = $classifications[0]->id;
        $data['code'] = $classifications[0]->code . '_' . $currentCode;
      } else {
        foreach ($classifications as $classification) {
          if ($classification->config) {
            $classificationConfig = json_decode($classification->config);
            $configArray = [];
            foreach ($classificationConfig as $row) {
              $configArray[$row->name] = $row->value;
            }
            $exist = true;
            foreach ($config as $key => $value) {
              if (isset($compArray[$key]) && isset($compArray[$key]->config?->affectClassification) && $compArray[$key]->config?->affectClassification) {
                if (!isset($configArray[$key]) || $configArray[$key] != $value) {
                  $exist = false;
                }
              }
            }
            if ($exist) {
              $data['classification_final_id'] = $classification->id;
              $data['classification_id'] = $classification->id;
              $data['code'] = $classification->code . '_' . $currentCode;
              break;
            }
          }
        }
      }

    }

    $etudiantCurrentAnnee = EtudiantAnneeUniversitaire::where('user_id', $user->id)->where('annee_universitaire_id', $anneeUniversitaireCurrent?->id)->first();

    if ($user->type === 'tunisien') {
      if ($etudiantCurrentAnnee) {
        $dip = Diplome::where('code', $etudiantCurrentAnnee->code_diplome)->first();
        $decisions = Decision::where('cin', $user->cin)->get();

        // set is_pret = true when user last decision is P and type is pret
        foreach ($decisions as $decision) {
          if ($demandeType->pret && $decision->annee_id === $anneeUniversitaireLast->id && $decision->situa === 'P' && $decision->type === 'pret') {
            $data['is_pret'] = true;
          }
        }
        if ($dip?->troisieme_cycle === true) {
          $catb = Catb::where('tunisien', true)->where('troisieme_cycle', true)->where('type_etude', 'indifferent')->first();
          $data['code_catb'] = $catb?->code;
        } elseif ($dip?->troisieme_cycle === false) {
          if ($anneeUniversitaireCurrent?->anneeBac?->id == $user->annee_bac) {
            $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'nouveau_bachelier')->first();
            $data['code_catb'] = $catb?->code;
          } else {
            $renouvellement = false;
            $retablissement = false;
            $enCoursEtude = false;
            foreach ($decisions as $decision) {
              if ($decision->annee_id === $anneeUniversitaireLast->id && $decision->situa === 'P') {
                $renouvellement = true;
                $retablissement = false;
                $enCoursEtude = false;
                break;
              }
              if ($decision->situa === 'P') {
                $retablissement = true;
              }
            }
            if (!$retablissement && !$renouvellement) {
              $enCoursEtude = true;
            }
            if ($renouvellement) {
              $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'renouvellement')->first();
              $data['code_catb'] = $catb?->code;
            }
            if ($retablissement) {
              $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'retablissement')->first();
              $data['code_catb'] = $catb?->code;
            }
            if ($enCoursEtude) {
              $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'en_cours_d_etude')->first();
              $data['code_catb'] = $catb?->code;
            }

          }
        }
      }
    } elseif ($user->type === 'etranger') {
      if ($etudiantCurrentAnnee) {
        $dip = Diplome::where('code', $etudiantCurrentAnnee->code_diplome)->first();
        if ($dip?->troisieme_cycle) {
          $catb = Catb::where('tunisien', false)->where('troisieme_cycle', true)->where('type_etude', 'indifferent')->first();
          $data['code_catb'] = $catb?->code;
        } else {
          $catb = Catb::where('tunisien', false)->where('troisieme_cycle', false)->where('type_etude', 'indifferent')->first();
          $data['code_catb'] = $catb?->code;
        }
      }
    }


    $g = Demande::create($data);

    foreach ($user->etudiantAnneeUniversitaires as $etudiantAnneeUniversitaire) {
      DemandeAnneeEtude::create([
        'demande_id' => $g->id,
        'user_id' => $user->id,
        'filiere_id' => $etudiantAnneeUniversitaire->filiere_id,
        'code_etab' => $etudiantAnneeUniversitaire->code_etab,
        'code_diplome' => $etudiantAnneeUniversitaire->code_diplome,
        'annee_etude' => $etudiantAnneeUniversitaire->annee_etude,
        'annee_universitaire_id' => $etudiantAnneeUniversitaire->annee_universitaire_id,
        'resultat_id' => $etudiantAnneeUniversitaire->resultat_id,
        'moyenne' => $etudiantAnneeUniversitaire->moyenne,
        'credit' => $etudiantAnneeUniversitaire->credit,
        'is_nouveau_bachelier' => $etudiantAnneeUniversitaire->is_nouveau_bachelier,
      ]);
    }
    $g->current_code_etab = $g->demande_last_annee_etude->code_etab;
    $g->etudiant_annee_universitaire_id = $g->demande_last_annee_etude->id;
    $g->save();
    if ($exist) {
      foreach ($g->classification->documentsClassifications as $documentsClassification) {
        DocumentClassificationDemande::create([
          'demande_id' => $g->id,
          'document_classification_id' => $documentsClassification->id,
        ]);
      }
    }
    $demande = Demande::with('demandeDocumentsClassifications', 'configDemandeType', 'demandeAnneeEtudes')->find($g->id);
    return response($demande, 201);
  }

  public function incoherentDemande(StoreDemandeRequest $request, Demande $demande): Response
  {
    $data = $request->validated();

    if ($demande) {
      $data['user_id'] = $demande->user_id;
    }
    $user = User::find($demande->user_id);
//        $data['code'] = Carbon::now()->timestamp;
//        $data['etat'] = Demande::ETAT['DOSSIER_EN_ATTENTE'];
//        $data['etat_document'] = Demande::ETAT_DOCUMENT['EN_COURS'];
//        $data['created_at'] = Carbon::now();
    $data['demande_type_id'] = $request->service;
    $data['config'] = $request->config;

    $config = json_decode($data['config'], true);

    $classifications = Classification::where('demande_type_id', $data['demande_type_id'])->where('classable', true)->get();
    $demandeType = DemandeType::find($data['demande_type_id']);

    $compArray = [];

    $data['config_demande_type_id'] = $demandeType?->last_config?->id;
    $data['type'] = $demandeType?->root_parent_code;

    if ($demandeType->bourse) {
      $data['is_bourse'] = true;
    }
    if ($demandeType->aide_sociale) {
      $data['is_aide_sociale'] = true;
    }
    if ($demandeType->bourses_de_stage) {
      $data['is_bourse_stage'] = true;
    }

    $anneeUniversitaireCurrent = AnneeUniversitaire::where('start', '<=', Carbon::now())->where('end', '>', Carbon::now())->first();
    $anneeUniversitaireLast = AnneeUniversitaire::where('start', '<=', Carbon::now()->subYear())->where('end', '>=', Carbon::now()->subYear())->first();
    if ($demandeType->bourses_de_stage) {
      $data['annee_universitaire_id'] = $anneeUniversitaireLast?->id;
    } else {
      $data['annee_universitaire_id'] = $anneeUniversitaireCurrent?->id;
    }
    foreach (json_decode($demandeType?->last_config?->config) as $row) {
      foreach ($row->children as $col) {
        foreach ($col->children as $comp) {
          $compArray[$comp->id] = $comp;
        }
      }
    }

    $exist = false;
    if (!$classifications->isEmpty()) {
      if (count($classifications) == 1) {
        $exist = true;
        $data['classification_final_id'] = $classifications[0]->id;
        $data['classification_id'] = $classifications[0]->id;

        if (
          $data['classification_final_id'] &&
          ($demande->etat == Demande::ETAT['REFUS_PAIEMENT'] || $demande->etat == Demande::ETAT['PREPARATION_PAIEMENT'] || $demande->etat == Demande::ETAT['PRISE_DE_DECISION']) &&
          (!$demande->lot || ($demande->lot && $demande->classification_final_id != $data['classification_final_id']))
        ) {

          $data['lot'] = null;
          $data['code_decision'] = null;
        }
      } else {
        foreach ($classifications as $classification) {
          if ($classification->config) {
            $classificationConfig = json_decode($classification->config);
            $configArray = [];
            foreach ($classificationConfig as $row) {
              $configArray[$row->name] = $row->value;
            }
            $exist = true;
            foreach ($config as $key => $value) {
              if (isset($compArray[$key]) && isset($compArray[$key]->config?->affectClassification) && $compArray[$key]->config?->affectClassification) {
                if (!isset($configArray[$key]) || $configArray[$key] != $value) {
                  $exist = false;
                }
              }
            }
            if ($exist) {
              $data['classification_final_id'] = $classification->id;
              $data['classification_id'] = $classification->id;
              if (
                $data['classification_final_id'] &&
                ($demande->etat === Demande::ETAT['REFUS_PAIEMENT'] || $demande->etat === Demande::ETAT['PREPARATION_PAIEMENT'] || $demande->etat === Demande::ETAT['PRISE_DE_DECISION']) &&
                (!$demande->lot || ($demande->lot && $demande->classification_final_id != $data['classification_final_id']))
              ) {
                $data['lot'] = null;
                $data['code_decision'] = null;
              }
              break;
            }
          }
        }
      }

    }

    $etudiantCurrentAnnee = EtudiantAnneeUniversitaire::where('user_id', $user->id)->where('annee_universitaire_id', $anneeUniversitaireCurrent?->id)->first();

    if ($user->type === 'tunisien') {
      if ($etudiantCurrentAnnee) {
        $dip = Diplome::where('code', $etudiantCurrentAnnee->code_diplome)->first();
        $decisions = Decision::where('cin', $user->cin)->get();

        // set is_pret = true when user decision is P and type is pret
        foreach ($decisions as $decision) {
          if ($demandeType->pret && $decision->annee_id === $anneeUniversitaireLast->id && $decision->situa === 'P' && $decision->type === 'pret') {
            $data['is_pret'] = true;
          }
        }
        if ($dip?->troisieme_cycle === true) {
          $catb = Catb::where('tunisien', true)->where('troisieme_cycle', true)->where('type_etude', 'indifferent')->first();
          $data['code_catb'] = $catb?->code;
        } elseif ($dip?->troisieme_cycle === false) {
          if ($anneeUniversitaireCurrent?->anneeBac?->id == $user->annee_bac) {
            $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'nouveau_bachelier')->first();
            $data['code_catb'] = $catb?->code;
          } else {
            $renouvellement = false;
            $retablissement = false;
            $enCoursEtude = false;
            foreach ($decisions as $decision) {
              if ($decision->annee_id === $anneeUniversitaireLast->id && $decision->situa === 'P') {
                $renouvellement = true;
                $retablissement = false;
                $enCoursEtude = false;
                break;
              }
              if ($decision->situa === 'P') {
                $retablissement = true;
              }
            }
            if (!$retablissement && !$renouvellement) {
              $enCoursEtude = true;
            }
            if ($renouvellement) {
              $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'renouvellement')->first();
              $data['code_catb'] = $catb?->code;
            }
            if ($retablissement) {
              $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'retablissement')->first();
              $data['code_catb'] = $catb?->code;
            }
            if ($enCoursEtude) {
              $catb = Catb::where('tunisien', true)->where('troisieme_cycle', false)->where('type_etude', 'en_cours_d_etude')->first();
              $data['code_catb'] = $catb?->code;
            }

          }
        }
      }
    } elseif ($user->type === 'etranger') {
      if ($etudiantCurrentAnnee) {
        $dip = Diplome::where('code', $etudiantCurrentAnnee->code_diplome)->first();
        if ($dip?->troisieme_cycle) {
          $catb = Catb::where('tunisien', false)->where('troisieme_cycle', true)->where('type_etude', 'indifferent')->first();
          $data['code_catb'] = $catb?->code;
        } else {
          $catb = Catb::where('tunisien', false)->where('troisieme_cycle', false)->where('type_etude', 'indifferent')->first();
          $data['code_catb'] = $catb?->code;
        }
      }
    }

//        dd($data);
    // log all data
    // Log::info("data: " . print_r($data, true));
    $demande->update($data);
//    Log::info("$demande: " . print_r($demande, true));

    $demande->demandeAnneeEtudes()->delete();

    foreach ($user->etudiantAnneeUniversitaires as $etudiantAnneeUniversitaire) {
      DemandeAnneeEtude::create([
        'demande_id' => $demande->id,
        'user_id' => $user->id,
        'filiere_id' => $etudiantAnneeUniversitaire->filiere_id,
        'code_etab' => $etudiantAnneeUniversitaire->code_etab,
        'code_diplome' => $etudiantAnneeUniversitaire->code_diplome,
        'annee_etude' => $etudiantAnneeUniversitaire->annee_etude,
        'annee_universitaire_id' => $etudiantAnneeUniversitaire->annee_universitaire_id,
        'resultat_id' => $etudiantAnneeUniversitaire->resultat_id,
        'moyenne' => $etudiantAnneeUniversitaire->moyenne,
        'credit' => $etudiantAnneeUniversitaire->credit,
      ]);

    }

    $demande->current_code_etab = $demande->demande_last_annee_etude->code_etab;
    $demande->etudiant_annee_universitaire_id = $demande->demande_last_annee_etude->id;
    $demande->save();

    if ($exist) {
      $demande->demandeDocumentsClassifications()->delete();
      $demande->save();
      $classificationf = Classification::find($data['classification_final_id']);
      foreach ($classificationf?->documentsClassifications as $documentsClassification) {
        DocumentClassificationDemande::create([
          'demande_id' => $demande->id,
          'document_classification_id' => $documentsClassification->id,
        ]);
      }
    }

    $d = Demande::with('demandeDocumentsClassifications', 'configDemandeType', 'demandeAnneeEtudes')->find($demande->id);
    return response($d, 201);
  }

  public function restoreToDecision(Demande $demande): Application|ResponseFactory|Response|JsonResponse
  {
    if ($demande->etat == Demande::ETAT['REFUS_PAIEMENT']) {
      $demande->update([
        'etat' => Demande::ETAT['EN_COURS_DE_DECISION'],
      ]);
    }

    if ($demande->etat == Demande::ETAT['DOSSIER_REFUS']) {
      $demande->update([
        'etat' => Demande::ETAT['DOSSIER_EN_COURS'],
      ]);
    }

    if ($demande->etat_bourse == Demande::ETAT_BOURSE['NON_ELIGIBLE']) {
      $demande->update([
        'etat_bourse' => Demande::ETAT_BOURSE['EN_ATTENTE'],
        'etat' => Demande::ETAT['PRISE_DE_DECISION'],
      ]);
    }

    if ($demande->etat_bourse_insertion == Demande::ETAT_BOURSE_INSERTION['NON_ELIGIBLE']) {
      $demande->update([
        'etat_bourse_insertion' => Demande::ETAT_BOURSE_INSERTION['EN_ATTENTE'],
        'etat' => Demande::ETAT['PRISE_DE_DECISION'],
      ]);
    }

    if ($demande->etat_pret == Demande::ETAT_PRET['NON_ELIGIBLE']) {
      $demande->update([
        'etat_pret' => Demande::ETAT_PRET['EN_ATTENTE'],
        'etat' => Demande::ETAT['PRISE_DE_DECISION'],
      ]);
    }

    return response(new DemandeResource($demande), 201);

  }

  public function destroy(int $id): JsonResponse
  {
    $demande = Demande::find($id);
    $demande->demandeAnneeEtudes()->delete();
    $demande->demandeDocumentsClassifications()->delete();
    $demande->delete();
    return response()->json([
      "result" => "success"
    ], 200);
  }

  public function restore(int $id): JsonResponse
  {
    $demande = Demande::find($id);
    $demande->restore();
    $demande->demandeAnneeEtudes()->restore();
    $demande->demandeDocumentsClassifications()->restore();
    return response()->json([
      "result" => "success"
    ], 200);
  }
}
