<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreHistoriqueRequest extends FormRequest
{
/*    private static function parseFilesize(string $size): int|float
    {
        if ('' === $size) {
            return 0;
        }

        $size = strtolower($size);

        $max = ltrim($size, '+');
        if (str_starts_with($max, '0x')) {
            $max = \intval($max, 16);
        } elseif (str_starts_with($max, '0')) {
            $max = \intval($max, 8);
        } else {
            $max = (int) $max;
        }

        switch (substr($size, -1)) {
            case 't': $max *= 1024;
            // no break
            case 'g': $max *= 1024;
            // no break
            case 'm': $max *= 1024;
            // no break
            case 'k': $max *= 1024;
        }

        return $max;
    }

    public static function getMaxFilesize(): int|float
    {
        $sizePostMax = self::parseFilesize(\ini_get('post_max_size'));
        $sizeUploadMax = self::parseFilesize(\ini_get('upload_max_filesize'));

        return intdiv(min($sizePostMax ?: \PHP_INT_MAX, $sizeUploadMax ?: \PHP_INT_MAX),1000);
    }*/
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => 'required',
            'annee_universitaire_id' => 'required',
            'file' => 'required|mimes:csv|max:21288',
//            'file' => 'required|mimes:csv|max:'.self::getMaxFilesize(),
        ];
    }
}
