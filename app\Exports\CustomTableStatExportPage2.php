<?php

namespace App\Exports;

use App\Models\AnneeUniversitaire;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class CustomTableStatExportPage2 implements WithEvents, FromView
{
    protected $data;
    protected $year;
    protected $labels;
    protected $date_export;

    public function __construct($data, $year,$labels)
    {
        $this->data = $data;
        $this->year = $year;
        $this->labels = $labels;
        $this->date_export = Carbon::parse(now())->format('d-m-Y H:i:s');


    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->setRightToLeft(true);
            },
        ];
    }


    public function view(): View
    {
        return view('statistiques.customTableStatPage2', [
            'data' => $this->data,
            'labels' => $this->labels,
            'year' => $this->year,
            'date_export' => $this->date_export,
            'year_export' => AnneeUniversitaire::whereDate('start', '<=', Carbon::now())->whereDate('end', '>', Carbon::now())->first()->title
        ]);
    }
}

