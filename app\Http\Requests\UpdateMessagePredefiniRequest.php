<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateMessagePredefiniRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'text' => 'required|string',
            'text_fr' => 'required|string',
            'text_ar' => 'required|string',
            'type' => 'required|string|unique:message_predefinis,type,'.$this->id,
            'active' => 'nullable|boolean|sometimes',
            ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }
}
