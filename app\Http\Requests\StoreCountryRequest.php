<?php

namespace App\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class StoreCountryRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name'=> 'required|string',
            'name_fr'=> 'required|string',
            'name_ar'=> 'required|string',
            'code'=> 'sometimes|nullable|string',
            'code_mes'=> 'nullable|sometimes',
            'active' => 'required|boolean',
        ];
    }
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => Helpers::toBoolean($this->active),
        ]);
    }
}
