<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateClassificationRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $methode = $this->method();
        if ($methode === 'PUT') {
            return [
                'code' => 'required|string|max:40|unique:classifications,code,'.$this->id,
                'title' => 'required|string',
                'title_fr' => 'nullable|string',
                'title_ar' => 'nullable|string',
                'classable' => 'nullable|boolean',
                'document_online' => 'nullable|boolean',
                'show_documents' => 'nullable|boolean',
                'active' => 'nullable|boolean',
                'config' => 'nullable|json',
                'parent_id' => 'nullable|integer',
                'demande_type_id' => 'nullable|integer',
                'profession_id' => 'nullable|integer',
            ];
        }

        return [
            'code' => 'sometimes|required|string|max:40|unique:classifications,code,'.$this->id,
            'title' => 'sometimes|required|string',
            'title_fr' => 'sometimes|nullable|string',
            'title_ar' => 'sometimes|nullable|string',
            'classable' => 'sometimes|nullable|boolean',
            'classable_par_admin' => 'sometimes|nullable|boolean',
            'document_online' => 'sometimes|nullable|boolean',
            'show_documents' => 'sometimes|nullable|boolean',
            'active' => 'sometimes|nullable|boolean',
            'config' => 'sometimes|nullable|json',
            'parent_id' => 'sometimes|nullable|integer',
            'demande_type_id' => 'sometimes|nullable|integer',
            'profession_id' => 'sometimes|nullable|integer',

        ];
    }
    /**
     * Prepare inputs for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'classable_par_admin' => $this->toBoolean($this->classable_par_admin),
            'classable' => $this->toBoolean($this->classable),
            'document_online' => $this->toBoolean($this->document_online),
            'show_documents' => $this->toBoolean($this->show_documents),
            'active' => $this->toBoolean($this->active),
            'parent_id' => $this->parent_id ?: null,
            'demande_type_id' => $this->demande_type_id ?: null,
//            'config' => json_encode($this->config),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param $booleable
     * @return boolean
     */
    private function toBoolean($booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}
