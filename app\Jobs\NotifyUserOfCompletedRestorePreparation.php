<?php

namespace App\Jobs;

use App\Models\Admin;
use App\Models\ExportedFile;
use App\Notifications\ExportCompletedNotification;
use App\Notifications\RestoreCompletedNotification;
use App\Notifications\RestoreNotCompletedNotification;
use App\Notifications\SyncCompletedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NotifyUserOfCompletedRestorePreparation
{
    use Queueable, SerializesModels;

    public $user;
    public $err;

    public $tries = 2;

    public $timeout = 360;

    public function __construct(?Admin $user, ?bool $err = false)
    {
        $this->user = $user;
        $this->err = $err;
    }

    public function handle()
    {
        if ($this->err) {
            $this->user->notify(new RestoreNotCompletedNotification());
        }
        $this->user->notify(new RestoreCompletedNotification());
    }
}
