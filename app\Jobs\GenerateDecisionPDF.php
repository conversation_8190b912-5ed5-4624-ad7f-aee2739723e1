<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\AnneeUniversitaire;
use App\Models\Decision;
use App\Models\DecisionsFile;
// use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use NumberToWords\NumberToWords;
use \Mpdf\Mpdf as PDF;

class GenerateDecisionPDF implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;
    public $id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data, $id)
    {
        $this->data = $data;
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            ini_set('memory_limit', '-1');

            $request = $this->data;
            switch (strtolower($request['type'])) {
                case 'bs':
                    $this->decision_bourse_stage($request['ndec'], $request['annee_universitaire_id'], $request['annee_gestion']);

                    break;
                case 'bi':
                    $this->decision_bourse_insertion($request['ndec'], $request['annee_universitaire_id'], $request['annee_gestion']);

                    break;
                case 'aide':
                    $this->decision_aide($request['ndec'], $request['annee_universitaire_id'], $request['annee_gestion']);

                    break;
                case 'pret':
                    $this->decision_pret($request['ndec'], $request['annee_universitaire_id'], $request['annee_gestion']);

                    break;
                default:
                    $this->decision_bourse($request['ndec'], $request['annee_universitaire_id'], $request['annee_gestion']);

                    break;
            }
        } catch (\Exception $e) {
            $this->failed($e);
        }
    }

    public function failed(\Exception $exception)
    {
        DecisionsFile::findOrFail($this->id)->update([
            'etat' => 2
        ]);
    }


    function decision_bourse($num_decision, $annee_univ, $annee_gestion)
    {
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');
        $historiques = Decision::where('annee_id', $annee_univ)
            ->where('ndec', $num_decision)
            ->where('office', 'like', 'C%')
            ->where('situa', 'P')
            ->with('etablissement')
            // ->select('cin', 'nom', 'anet', 'discip', 'mbs', 'mf', 'montanttotal')
            ->get();
        $annee_universitaire = AnneeUniversitaire::find($annee_univ)->title;

        $mpdf->SetHTMLHeader("Décision de Bourse N° " . $num_decision . ", " . $annee_universitaire . "<hr>");
        $mpdf->SetHTMLFooter('<div style="text-align:center">-- {PAGENO}/{nb} --</div>');

        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>Décision de Bourse N° " . $num_decision . "<br>Année universitaire " . $annee_universitaire . "</P>");

        $mpdf->WriteHTML($this->get_decret($annee_universitaire, $annee_gestion));
        $mpdf->AddPage();
        $mpdf->WriteHTML("<table align=center border=1 style='width:100%;border-collapse:collapse;'><thead>");
        $mpdf->WriteHTML("<tr><th>CIN</th><th>Nom/Prénom</th><th>A.Etude</th><th>Diplôme</th><th>Etab.Ens</th><th>Taux Mensuel</th><th>Mnt Bourse Annuelle</th><th>Mnt Fournitures</th></tr></thead>");

        $nbr_etd = 0;
        $total_bourse = 0;
        $montantAnnuelBourses = 0;
        $montantFournitures = 0;

        // $stmt=$pdo->query("SELECT br.*,f.`nomFac` FROM `br` , `faculte` f where br.`fac`=f.`codFac` and br.`annee_universitaire`=".$annee_universitaire." and br.`NDEC`='".$num_decision."' order by br.`cin`");
        foreach ($historiques as $key => $row) {
            $nbr_etd += 1;
            $total_bourse += (int)$row->mbs;
            $montantAnnuelBourses += (int)$row->montanttotal;
            $montantFournitures += (int)$row->mf;
            $mpdf->WriteHTML("<tr><td>" . $row->cin . "</td><td>" . $row->nom . "</td><td>" . $row->anet . "</td><td>" . $row->discip . "</td><td>" . $row->etablissement->name . "</td><td>" . $row->mbs . "</td><td>" . $row->montanttotal . "</td><td>" . $row->mf . "</td><</tr>");
        }
        $montantGlobal = $montantAnnuelBourses + $montantFournitures;



        $mpdf->WriteHTML("</table>");

        $mpdf->WriteHTML("<br><br><p>NOMBRE D'ETUDIANTS: " . $nbr_etd . "<BR>MONTANT MENSUEL DES BOURSES: " . $total_bourse . "<BR>MONTANT ANNUEL DES BOURSES: " . $montantAnnuelBourses . "<br>MONTANT DES FOURNITURES: " . $montantFournitures . "<BR>MONTANT GLOBAL: " . $montantGlobal . "</p><BR>ARTICLE 2: LES DEPENSES RESULTANT DE L'APPLICATION DE LA PRESENTE DECISION SONT INPUTEES SUR LES CREDITS DU MINISTERE DE L'ENSEIGNEMENT SUPERIEUR ET DE LA RECHERCHE SCIENTIFIQUE</p><BR><p align=center>SOUSSE LE : <BR>P/LE MINISTRE</p>");

        $filename = 'decisions/decision_bourse_' . $num_decision . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        DecisionsFile::findOrFail($this->id)->update([
            'etat' => 1,
            'path' => $filename
        ]);
    }

    function decision_bourse_insertion($num_decision, $annee_univ, $annee_gestion)
    {
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');
        $historiques = Decision::where('annee_id', $annee_univ)
            ->where('ndec', $num_decision)
            ->where('office', 'like', 'C%')
            ->where('situa', 'P')
            ->with('etablissement')
            // ->select('cin', 'nom', 'anet', 'discip', 'mbs', 'mf', 'montanttotal')
            ->get();
        $annee_universitaire = AnneeUniversitaire::find($annee_univ)->title;

        $mpdf->SetHTMLHeader("Décision de Bourse d'insertion N° " . $num_decision . ", " . $annee_universitaire . "<hr>");
        $mpdf->SetHTMLFooter('<div style="text-align:center">-- {PAGENO}/{nb} --</div>');

        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>DECISION DE BOURSE DE L’INTEGRATION N° " . $num_decision . "<br>ANNEE UNIVERSITAIRE " . $annee_universitaire . "</P>");

        $mpdf->WriteHTML($this->get_decret_bi($annee_universitaire, $annee_gestion));
        $mpdf->AddPage();
        $mpdf->WriteHTML("<table align=center border=1 style='width:100%;border-collapse:collapse;'><thead>");
        $mpdf->WriteHTML("<tr><th>CIN</th><th>Nom/Prénom</th><th>A.Etude</th><th>Diplôme</th><th>Etab.Ens</th><th>Mnt Bourse Annuelle</th></tr></thead>");

        $nbr_etd = 0;
        $total_bourse = 0;
        $montantAnnuelBourses = 0;
        $montantFournitures = 0;

        // $stmt=$pdo->query("SELECT br.*,f.`nomFac` FROM `br` , `faculte` f where br.`fac`=f.`codFac` and br.`annee_universitaire`=".$annee_universitaire." and br.`NDEC`='".$num_decision."' order by br.`cin`");
        foreach ($historiques as $key => $row) {
            $nbr_etd += 1;
            $total_bourse += (int)$row->mbs;
            $montantAnnuelBourses += (int)$row->montanttotal;
            $montantFournitures += (int)$row->mf;
            $mpdf->WriteHTML("<tr><td>" . $row->cin . "</td><td>" . $row->nom . "</td><td>" . $row->anet . "</td><td>" . $row->discip . "</td><td>" . $row->etablissement->name . "</td><td>" . $row->montanttotal . "</td><</tr>");
        }
        $montantGlobal = $montantAnnuelBourses + $montantFournitures;



        $mpdf->WriteHTML("</table>");

        $mpdf->WriteHTML("<br><br><p>NOMBRE D'ETUDIANTS: " . $nbr_etd . "<BR>MONTANT ANNUEL DES BOURSES: " . $montantAnnuelBourses .  "<BR>MONTANT GLOBAL: " . $montantGlobal . "</p><BR>ARTICLE 2:  LES DEPENSES RESULTANT DE L’APPLICATION DE LA PRESENTE DECISION SONT IMPUTEES SUR LES CREDITS DU MINIS. ENSEIG. SUP. RECH. SCIENTIFIQUE ............... ET DELEGUES PAR ORDONNANCE N ....... DU ............</p><BR><p align=center>SOUSSE LE : <BR>P/LE MINISTRE</p>");

        $filename = 'decisions/decision_bourse_insertion_' . $num_decision . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        DecisionsFile::findOrFail($this->id)->update([
            'etat' => 1,
            'path' => $filename
        ]);
    }

    function decision_bourse_stage($num_decision, $annee_univ, $annee_gestion)
    {
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');
        $historiques = Decision::where('annee_id', $annee_univ)
            ->where('ndec', $num_decision)
            ->where('office', 'like', 'C%')
            ->where('situa', 'P')
            ->with('etablissement')
            // ->select('cin', 'nom', 'anet', 'discip', 'mbs', 'mf', 'montanttotal')
            ->get();
        $annee_universitaire = AnneeUniversitaire::find($annee_univ)->title;

        $mpdf->SetHTMLHeader("Décision de Bourse de Stage N° " . $num_decision . ", " . $annee_universitaire . "<hr>");
        $mpdf->SetHTMLFooter('<div style="text-align:center">-- {PAGENO}/{nb} --</div>');

        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>DECISION D’OCTROI DE BOURSE NATIONALE<br>ANNEE UNIVERSITAIRE " . $annee_universitaire . "<br>Décision N° " . $num_decision . "</P>");
        $mpdf->WriteHTML($this->get_decret_bs($annee_universitaire, $annee_gestion));
        $mpdf->AddPage();
        $mpdf->WriteHTML("<table align=center border=1 style='width:100%;border-collapse:collapse;'><thead>");
        $mpdf->WriteHTML("<tr><th>CIN</th><th>Nom/Prénom</th><th>A.Etude</th><th>Diplôme</th><th>Etab.Ens</th><th>Taux Mensuel</th><th>Mnt Bourse Annuelle</th><th>Mnt Fourniture</th></tr></thead>");

        $nbr_etd = 0;
        $total_bourse = 0;
        $montantAnnuelBourses = 0;
        $montantFournitures = 0;

        // $stmt=$pdo->query("SELECT br.*,f.`nomFac` FROM `br` , `faculte` f where br.`fac`=f.`codFac` and br.`annee_universitaire`=".$annee_universitaire." and br.`NDEC`='".$num_decision."' order by br.`cin`");
        foreach ($historiques as $key => $row) {
            $nbr_etd += 1;
            $total_bourse += (int)$row->mbs;
            $montantAnnuelBourses += (int)$row->montanttotal;
            $montantFournitures += (int)$row->mf;
            $mpdf->WriteHTML("<tr><td>" . $row->cin . "</td><td>" . $row->nom . "</td><td>" . $row->anet . "</td><td>" . $row->discip . "</td><td>" . $row->etablissement->name . "</td><td>" . $row->mbs . "</td><td>" . $row->montanttotal . "</td><td>" . $row->mf . "</td></tr>");
        }
        $montantGlobal = $montantAnnuelBourses + $montantFournitures;

        $mpdf->WriteHTML("</table>");

        $mpdf->WriteHTML("<br><br><p>NOMBRE D'ETUDIANTS: " . $nbr_etd . "<BR>MONTANT GLOBAL: " . $montantGlobal . "</p><BR>ARTICLE 2: LES DEPENSES RESULTANT DE L’APPLICATION DE LA PRESENTE DECISION SONT IMPUTEES SUR LES CREDITS DU MINIS. ENSEIG. SUP. RECH. SCIENTIFIQUE ............... ET DELEGUES PAR ORDONNANCE N ....... DU ............</p><BR><p align=center>SOUSSE LE : <BR>P/LE MINISTRE</p>");

        $filename = 'decisions/decision_bourse_stage_' . $num_decision . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        DecisionsFile::findOrFail($this->id)->update([
            'etat' => 1,
            'path' => $filename
        ]);
    }

    function decision_pret($num_decision, $annee_univ, $annee_gestion)
    {
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');
        $historiques = Decision::where('annee_id', $annee_univ)
            ->where('ndec', $num_decision)
            ->where('office', 'like', 'C%')
            ->where('situa', 'P')
            ->with('etablissement')
            // ->select('cin', 'nom', 'anet', 'discip', 'mbs', 'mf', 'montanttotal')
            ->get();
        $annee_universitaire = AnneeUniversitaire::find($annee_univ)->title;
        $mpdf->SetHTMLHeader("Décision de Prêt N° " . $num_decision . ", " . $annee_universitaire . "<hr>");
        $mpdf->SetHTMLFooter('<div style="text-align:center">-- {PAGENO}/{nb} --</div>');

        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>DECISION D’OCTROI DE PRET UNIVERSITAIRE<br>ANNEE UNIVERSITAIRE " . $annee_universitaire . "<br>Décision N° " . $num_decision .  "</P>");
        $mpdf->WriteHTML($this->get_decret_pret($annee_universitaire, $annee_gestion));
        $mpdf->AddPage();
        $mpdf->WriteHTML("<table align=center border=1 style='width:100%;border-collapse:collapse;'><thead>");
        $mpdf->WriteHTML("<tr><th>CIN</th><th>Nom/Prénom</th><th>A.Etude</th><th>Diplôme</th><th>Etab.Ens</th><th>Mnt Prêt</th></tr></thead>");

        $nbr_etd = 0;
        $total_bourse = 0;
        $montantAnnuelBourses = 0;
        $montantFournitures = 0;

        foreach ($historiques as $key => $row) {
            $nbr_etd += 1;
            $total_bourse += (int)$row->mbs;
            $montantAnnuelBourses += (int)$row->montanttotal;
            $montantFournitures += (int)$row->mf;
            $mpdf->WriteHTML("<tr><td>" . $row->cin . "</td><td>" . $row->nom . "</td><td>" . $row->anet . "</td><td>" . $row->discip . "</td><td>" . $row->etablissement->name . "</td><td>" . $row->montanttotal . "</td></tr>");
        }
        $montantGlobal = $montantAnnuelBourses + $montantFournitures;

        $mpdf->WriteHTML("</table>");

        $mpdf->AddPage();

        $mpdf->WriteHTML("<p>NOMBRE D'ETUDIANTS: " . $nbr_etd . "<BR>MONTANT GLOBAL DES PRETS: " . $montantAnnuelBourses . " DINARS<br><br>ARTICLE 2: LES DEPENSES RESULTANT DE L’APPLICATION DE LA PRESENTE DECISION SONT IMPUTEES SUR LES CREDITS DU MINIS. ENSEIG. SUP. RECH. SCIENTIFIQUE ............... ET DELEGUES PAR ORDONNANCE N ....... DU ............</p><BR><p align=center>SOUSSE LE : <BR>P/LE MINISTRE</p>");

        $filename = 'decisions/decision_pret_' . $num_decision . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        DecisionsFile::findOrFail($this->id)->update([
            'etat' => 1,
            'path' => $filename
        ]);
    }

    function decision_aide($num_decision, $annee_univ, $annee_gestion)
    {
        $mpdf = new PDF([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 10,
            'margin_right' => '10',
            'margin_top' => '20',
            'margin_bottom' => '13',
            'margin_left' => '10',
            'margin_footer' => '2',
        ]);
        $mpdf->SetDisplayMode('fullpage');
        $historiques = Decision::where('annee_id', $annee_univ)
            ->where('ndec', $num_decision)
            ->where('office', 'like', 'C%')
            ->where('situa', 'P')
            ->with('etablissement')
            // ->select('cin', 'nom', 'anet', 'discip', 'mbs', 'mf', 'montanttotal')
            ->get();
        $annee_universitaire = AnneeUniversitaire::find($annee_univ)->title;
        $mpdf->SetHTMLHeader("Décision N° " . $num_decision . ", " . $annee_universitaire . "<hr>");
        $mpdf->SetHTMLFooter('<div style="text-align:center">-- {PAGENO}/{nb} --</div>');

        $mpdf->WriteHTML("REPUBLIQUE TUNISIENNE<BR>MINISTERE DE L'ENSEIGNEMENT SUPERIEUR<BR>ET DE LA RECHERCHE SCIENTIFIQUE<P ALIGN=CENTER>OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE<br>DECISION D’OCTROI D’AIDE SOCIALE<br>ANNEE UNIVERSITAIRE " . $annee_universitaire . "<br>Décision N° " . $num_decision .  "</P>");
        $mpdf->WriteHTML($this->get_decret_aide($annee_universitaire, $annee_gestion));
        $mpdf->AddPage();
        $mpdf->WriteHTML("<table align=center border=1 style='width:100%;border-collapse:collapse;'><thead>");
        $mpdf->WriteHTML("<tr><th>N°</th><th>CIN</th><th>LOT</th><th>NOM</th><th>A.Etude</th><th>DIS</th><th>Etab.Ens</th><th>REVCAL</th><th>MONTANT</th></tr></thead>");

        $nbr_etd = 0;
        $total_bourse = 0;
        $montanttotal = 0;
        $montantFournitures = 0;

        foreach ($historiques as $key => $row) {
            $nbr_etd += 1;
            $total_bourse += (int)$row->mbs;
            $montanttotal += (int)$row->montanttotal;
            $montantFournitures += (int)$row->mf;
            $mpdf->WriteHTML("<tr><td>" . $key+1 . "</td><td>" . $row->cin . "</td><td>" . $row->lot . "</td><td>" . $row->nom . "</td><td>" . $row->anet . "</td><td>" . $row->discip . "</td><td>" . $row->etablissement->name . "</td><td>" . ((int)$row->revp+(int)$row->revm) . "</td><td>" . $row->montanttotal . "</td></tr>");
        }
        $mpdf->WriteHTML("<tr><td  style='border:none' colspan='7'></td><td style='font-weight: bold'>Total</td><td>".number_format($montanttotal/1000, 3, ' ')."</td></tr>");
        $mpdf->WriteHTML("</table>");

        $mpdf->AddPage();
        $numberToWords = new NumberToWords();
        $words = $numberToWords->getNumberTransformer('fr')->toWords($montanttotal);
        $mpdf->WriteHTML("<p>Arrêter la présente liste à la somme de ". $words."</p><BR><p align=center>P/LE MINISTRE</p>");

        $filename = 'decision_pret_' . $num_decision . '_' . time() . '.pdf';

        Storage::put($filename, $mpdf->Output($filename, "S"));

        DecisionsFile::findOrFail($this->id)->update([
            'etat' => 1,
            'path' => $filename
        ]);
    }

    function get_decret($annee_univ, $annee_gestion)
    {
        return "<p align=justify>
        LE MINISTRE <br><br>
        VU LA LOI N°:88-136 DU 3 DECEMBRE 1988 PORTANT CREATION DE L’OFFICE DES OEUVRES UNIVERSITAIRES POUR LE CENTRE.<br><br>
        VU LA LOI N°:2008-19 DU 25 FEVRIER 2008 RELATIVE A L'ENSEIGNEMENT SUPERIEUR, TELLE QUE MODIFIEE PAR LE DECRET-LOI 2011-31 DU 26 AVRIL 2011.<br><br>
        VU LE DECRET N°: 2009-3040 DU 19 OCTOBRE 2009, RELATIF AUX BOURSES NATIONALES ET AUX PRETS UNIVERSITAIRES AU PROFIT DES ETUDIANTS ET ELEVES DE L’ENSEIGNEMENT SUPERIEUR, TEL QUE MODIFIE ET COMPLETE PAR LE DECRET N°2012-2392 DU 09 OCTOBRE 2012.<br><br>
        VU L’ARRETE DU MINISTRE DE L’ENSEIGNEMENT SUPERIEUR ET DE LA RECHERCHE SCIENTIFIQUE ET DE LA TECHNOLOGIE DU 26 OCTOBRE 2009, FIXANT LES MODALITES D’ATTRIBUTION ET DE RENOUVELLEMENT DES BOURSES NATIONALES ET DES PRETS UNIVERSITAIRES AU PROFIT DES ETUDIANTS ET ELEVES DE L'ENSEIGNEMENT SUPERIEUR, TEL QUE MODIFIE ET COMPLETE PAR L’ARRETE DU MINISTRE DE L’ENSEIGNEMENT SUPERIEUR ET DE LA RECHERCHE SCIENTIFIQUE DU 09 OCTOBRE 2012.<br><br>
        VU LE DECRET PRESIDENTIEL N°: 2015-35 DU 06 FEVRIER 2015, PORTANT NOMINATION DU CHEF DU GOUVERNEMENT ET DE SES MEMBRES. <br><br>
        VU L’ARRETE DU MINISTRE DE L'ENSEIGNEMENT SUPERIEUR ET DE LA RECHERCHE SCIENTIFIQUE  DU 04 DECEMBRE 2015, FIXANT LES MONTANTS DES BOURSES NATIONALES D’ETUDES SUPERIEURES EN TUNISIE.<br><br>
        VU LE DECRET N°2017-668 DU 05 JUIN 2017 FIXANT LE SALAIRE INTERPROEESSIONNEL MINIMUM GARANTI.<br><br>
        VU LES CREDITS INSCRITS AU BUDGET DU MINISTERE DE L’ENSEIGNEMENT SUPERIEUR ET DE LA RECHERCHE SCIENTIFIQUE, ARTICLE 03.03336.7400.001 ET DELEGUES A l’OFFICE DES ŒUVRES UNIVERSITAIRES POUR LE CENTRE AU TITRE DE LA GESTION " . $annee_gestion . ".<br><br>
        CONSIDERANT QUE LES ETUDIANTS CI-DESSOUS DESIGNES ONT ADRESSE LEURS DEMANDES DE BOURSE DANS LES DELAIS PRESCRITS, DEMANDES JUSTIFIEES PAR LEURS INSCRIPTIONS DANS UN ETABLISSEMENT D’ENSEIGNEMENT SUPERIEUR POUR L’ANNEE UNIVERSITAIRE " . $annee_univ . ".<br><br>
        <div align='center'>D E C I D E</div>*************<br>
        ARTICLE PREMIER : IL EST ALLOUE AUX ETUDIANTS CI-DESSOUS INDIQUES ET POURSUIVANT LEURS ETUDES EN TUNISIE :<br><br>
        1) UNE BOURSE NATIONALE AU TAUX FIXE AU TABLEAU CI-APRES. <br><br>
        2) UNE ALLOCATION POUR ACHAT DE FOURNITURES SCOLAIRES DONT LE MONTANT EST FIXE CI-APRES.</p>
        ";
    }

    function get_decret_pret($annee_univ, $annee_gestion)
    {
        return "<p align=justify>
        LE MINISTRE <br><br>
        Vu la loi n° 88-136 du 3 décembre 1988, portant création de l'office des œuvres universitaires pour le Centre, telle que modifiée par la loi n° 96-89 du 6 novembre 1996,<br><br>
        Vu la loi n° 2008-19 du 25 février 2008, relative à l'enseignement supérieur, ensemble les textes qui l'ont modifiée et notamment la loi n° 2017-38 du 2 mai 2017,<br><br>
        Vu le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, ensemble les textes qui l'ont modifié et complété et notamment le décret gouvernemental n° 2018-927 du 13 novembre 2018,<br><br>
        Vu le décret Présidentiel n° 2021-138 du 11 octobre 2021, portant nomination des membres du Gouvernement,<br><br>
        Vu le décret n° 2018-927 du 13 novembre 2018, modifiant et complétant le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, <br><br>
        Vu l'arrêté du Ministre de l'enseignement supérieur, de la recherche scientifique et de la technologique du 26 octobre 2009, fixant les conditions et les modalités d'attribution et de renouvellement des bourses nationales et des prêts universitaires au profit des étudiants et des élèves de l'enseignement supérieur, tel que modifié et complété par l'arrêté du 9 octobre 2012,<br><br>
        Vu  L’arrêté du Ministre de l'enseignement supérieur et de la recherche scientifique du 4 décembre 2018, fixant les conditions d'attribution de la bourse de l'insertion universitaire et son montant.<br><br>
        Vu les crédits inscrits au budget de ministère de l’enseignement supérieur et de la recherche scientifique, article 31300.1101.010 et délégués a l’Office des Œuvres universitaires pour le Centre au titre de la gestion " . $annee_gestion . ",<br><br>
        Considérant que les étudiants ci-dessous désignes ont adressé leur demandes de bourse de lintegration universitaire dans les délais prescrits, demandes justifiées par leurs inscriptions dans un établissement d’enseignement supérieur pour l’année universitaire " . $annee_univ . ",<br><br>
        <div align='center'>D E C I D E</div>*************<br>
        ARTICLE PREMIER : il est alloué aux étudiants ci-dessous indiqués et poursuivant leurs études en Tunisie une bourse d’insertion au taux fixés au tableau ci-après :</p>
        ";
    }

    function get_decret_bi($annee_univ, $annee_gestion)
    {
        return "<p align=justify>
            LE MINISTRE <br><br>
            Vu la loi n° 88-136 du 3 décembre 1988, portant création de l'office des œuvres universitaires pour le Centre, telle que modifiée par la loi n° 96-89 du 6 novembre 1996,<br><br>
            Vu la loi n° 2008-19 du 25 février 2008, relative à l'enseignement supérieur, ensemble les textes qui l'ont modifiée et notamment la loi n° 2017-38 du 2 mai 2017,<br><br>
            Vu le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, ensemble les textes qui l'ont modifié et complété et notamment le décret gouvernemental n° 2018-927 du 13 novembre 2018,<br><br>
            Vu le décret Présidentiel n° 2020-84 du 2 septembre 2020, portant nomination du chef du gouvernement et de ses membres,<br><br>
            Vu le décret n° 2018-927 du 13 novembre 2018, modifiant et complétant le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, <br><br>
            Vu le décret n°2022-769 du 19 octobre 2022 fixant le salaire interprofessionnel minimum garanti,<br><br>

            Vu l'arrêté du ministre de l'enseignement supérieur, de la recherche scientifique et de la technologique du 26 octobre 2009, fixant les conditions et les modalités d'attribution et de renouvellement des bourses nationales et des prêts universitaires au profit des étudiants et des élèves de l'enseignement supérieur, tel que modifié et complété par l'arrêté du 9 octobre 2012,<br><br>
            Vu l'arrêté du ministre de l'enseignement supérieur, de la recherche scientifique du 09 octobre 2012, portant modification de l’arrêté du 24 novembre 2010 fixant les montants des prêts universitaires,<br><br>

            Vu les crédits inscrits au budget de ministère de l’enseignement supérieur et de la recherche scientifique, article 31300.1101.003 et délégués a l’office des œuvres universitaire pour le centre au titre de la gestion " . $annee_gestion . ",<br><br>
            Considérant que les étudiants ci-dessous désignes ont adressé leur demandes de prêt dans les délais prescrits, demandes justifiées par leurs inscriptions dans un établissement d’enseignement supérieur pour l’année universitaire " . $annee_univ . ",<br><br>
            <div align='center'>D E C I D E</div>*************<br>
            ARTICLE PREMIER : un prêt universitaire est accordé aux étudiants poursuivants leurs études en Tunisie au titre de l’année universitaire 2021/2022 dans les conditions ci-après.</p>
            ";
    }

    function get_decret_bs($annee_univ, $annee_gestion)
    {
        return "<p align=justify>
        LE MINISTRE <br><br>
        Vu la loi n° 88-136 du 3 décembre 1988, portant création de l'office des œuvres universitaires pour le Centre, telle que modifiée par la loi n° 96-89 du 6 novembre 1996,<br><br>
        Vu la loi n° 2008-19 du 25 février 2008, relative à l'enseignement supérieur, ensemble les textes qui l'ont modifiée et notamment la loi n° 2017-38 du 2 mai 2017,<br><br>
        Vu le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, ensemble les textes qui l'ont modifié et complété et notamment le décret gouvernemental n° 2018-927 du 13 novembre 2018,<br><br>
        Vu le décret Présidentiel n° 2021-138 du 11 octobre 2021, portant nomination des membres du Gouvernement,<br><br>
        Vu le décret n° 2018-927 du 13 novembre 2018, modifiant et complétant le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, <br><br>
        Vu le décret n°2019-454 du 28 mai 2019 fixant le salaire interprofessionnel minimum garanti,<br><br>
        Vu l'arrêté du ministre de l'enseignement supérieur, de la recherche scientifique et de la technologique du 26 octobre 2009, fixant les conditions et les modalités d'attribution et de renouvellement des bourses nationales et des prêts universitaires au profit des étudiants et des élèves de l'enseignement supérieur, tel que modifié et complété par l'arrêté du 9 octobre 2012,<br><br>
        Vu l'arrêté du ministre de l'enseignement supérieur, de la recherche scientifique du 04 décembre 2015 fixant les montants des bourses nationales d’études supérieures en Tunisie,<br><br>
        Vu les crédits inscrits au budget de ministère de l’enseignement supérieur et de la recherche scientifique, article 31300.1101.001 et délégués a l’office des œuvres universitaire pour le centre au titre de la gestion " . $annee_gestion . ".<br><br>
        Considérant que les étudiants ci-dessous désignes ont adresse leur demandes de bourse de stage dans les délais prescrits, demandes justifiées par leurs inscriptions dans un établissement d’enseignement supérieur pour l’année universitaire " . $annee_univ . ".<br><br>
        <div align='center'>D E C I D E</div>*************<br>
        ARTICLE PREMIER : il est alloué aux étudiants ci-dessous indiqués et poursuivants leurs études en Tunisie :<br><br>
        1) Une bourse de stage au taux fixés au tableau ci-après : <br><br>
        </p>
        ";
    }

    function get_decret_aide($annee_univ, $annee_gestion)
    {
        return "<p align=justify>
        LE MINISTRE <br><br>
        Vu la loi n° 88-136 du 3 décembre 1988, portant création de l'office des œuvres universitaires pour le Centre, telle que modifiée par la loi n° 96-89 du 6 novembre 1996,<br><br>
        Vu la loi n° 2008-19 du 25 février 2008, relative à l'enseignement supérieur, ensemble les textes qui l'ont modifiée et notamment la loi n° 2017-38 du 2 mai 2017,<br><br>
        Vu le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur, ensemble les textes qui l'ont modifié et complété et notamment le décret gouvernemental n° 2018-927 du 13 novembre 2018,<br><br>
        Vu le décret Présidentiel n° 2021-138 du 11 octobre 2021, portant nomination des membres du Gouvernement,<br><br>
        Vu le décret n° 2018-927 du 13 novembre 2018, modifiant et complétant le décret n° 2009-3040 du 19 octobre 2009, relatif aux bourses nationales et aux prêts universitaires au profit des étudiants et élèves de l'enseignement supérieur,<br><br>
        Vu le décret n°2022-769 du 19 mai octobre 2022 fixant le salaire interprofessionnel minimum garanti,<br><br>
        Vu l'arrêté du ministre de l'enseignement supérieur, de la recherche scientifique et de la technologique du 26 octobre 2009, fixant les conditions et les modalités d'attribution et de renouvellement des bourses nationales et des prêts universitaires au profit des étudiants et des élèves de l'enseignement supérieur, tel que modifié et complété par l'arrêté du 9 octobre 2012,<br><br>
        Vu les crédits inscrits au budget de ministère de l’enseignement supérieur et de la recherche scientifique, article ……………………………………………… et délégués à l’office des œuvres universitaire pour le centre au titre de la gestion " . $annee_gestion . ".<br><br>
        Considérant que les étudiants ci-dessous désignes ont adressé leur demandes d’aide sociale dans les délais prescrits, demandes justifiées par leurs inscriptions dans un établissement d’enseignement supérieur pour l’année universitaire " . $annee_univ . ".<br><br>
        <div align='center'>D E C I D E</div>*************<br>
        1.  ARTICLE PREMIER :  il est alloué aux étudiants ci-dessous indiqués et poursuivants leurs études en Tunisie une aide sociale au taux fixés au tableau ci-après.<br><br></p>
        ";
    }
}
