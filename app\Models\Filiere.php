<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Filiere extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;

    public function __construct()
    {
        parent::__construct();
        $this->table = DB::connection(config('database.default'))->getDatabaseName() . '.' . $this->getTable();
        $this->connection = config('database.default');
    }

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'code',
        'active',
        'code_etab',
        'code_diplome',
        'annee_universitaire',
    ];


    public function etablissement() : BelongsTo
    {
        return $this->belongsTo(Etablissement::class,"code_etab","code");
    }
    public function diplome() : BelongsTo
    {
        return $this->belongsTo(Diplome::class,"code_diplome","code");
    }
    public function anneeUniversitaire() : BelongsTo
    {
        return $this->belongsTo(AnneeUniversitaire::class,"annee_universitaire","id");
    }


    public function orientations() : HasMany
    {
        return $this->hasMany(Orientation::class,'code_filiere','code')->where('annee_universitaire', $this->annee_universitaire);
    }
}
